/**
 * Tech E-commerce Website Full Test
 * 
 * Comprehensive end-to-end test of the Contract-First LLM Generation System
 * by creating a complete tech e-commerce website with full testing suite.
 */

import { ContractFirstLLMGenerator, ApplicationRequirements } from '../core/ContractFirstLLMGenerator';
import { IntegrationValidationSystem } from '../core/IntegrationValidationSystem';
import { Logger } from '../core/Logger';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Generate a complete tech e-commerce website with comprehensive testing
 */
export async function generateTechEcommerceWebsite(): Promise<void> {
  const logger = Logger.getInstance();
  logger.info('🚀 Starting Tech E-commerce Website Full Test');

  const startTime = Date.now();

  try {
    // Define comprehensive tech e-commerce requirements
    const requirements: ApplicationRequirements = {
      name: 'TechMart Pro',
      description: 'A comprehensive tech e-commerce platform specializing in electronics, gadgets, and computer hardware with advanced features for modern online shopping',
      features: [
        // Core E-commerce Features
        'User registration and authentication with email verification',
        'Advanced product catalog with categories (Laptops, Smartphones, Gaming, Components)',
        'Product search with filters (price, brand, specifications, ratings)',
        'Shopping cart with save for later functionality',
        'Wishlist and product comparison tools',
        'Secure checkout with multiple payment methods',
        'Order tracking and management system',
        'Customer reviews and ratings with photo uploads',
        
        // Advanced Features
        'Inventory management with real-time stock updates',
        'Multi-vendor marketplace support',
        'Advanced admin dashboard with analytics',
        'Email notifications and marketing campaigns',
        'Mobile-responsive progressive web app',
        'Live chat customer support integration',
        'Product recommendations using AI',
        'Loyalty points and rewards program',
        
        // Tech-Specific Features
        'Technical specifications comparison tool',
        'Compatibility checker for components',
        'Product configurator for custom builds',
        'Extended warranty and service plans',
        'Tech support ticket system',
        'Product manuals and documentation downloads',
        'Video reviews and unboxing content',
        'Price tracking and alerts'
      ],
      businessDomain: 'tech-ecommerce',
      targetFramework: 'react-express',
      databaseType: 'postgresql',
      deploymentTarget: 'docker'
    };

    console.log('\n📋 Tech E-commerce Website Specifications:');
    console.log('==========================================');
    console.log(`Name: ${requirements.name}`);
    console.log(`Description: ${requirements.description}`);
    console.log(`Features: ${requirements.features.length} advanced features`);
    console.log(`Framework: ${requirements.targetFramework}`);
    console.log(`Database: ${requirements.databaseType}`);
    console.log(`Deployment: ${requirements.deploymentTarget}`);

    // Initialize the contract-first generator
    const generator = new ContractFirstLLMGenerator();

    console.log('\n🔄 Phase 1: Generating Complete Application...');
    console.log('===============================================');

    // Generate the complete application with all components
    const artifacts = await generator.generateApplication(requirements);

    console.log(`✅ Application generation completed!`);
    console.log(`📄 Total artifacts generated: ${artifacts.length}`);
    console.log(`📝 Total lines of code: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}`);

    // Analyze generated artifacts
    const artifactsByType = artifacts.reduce((acc: Record<string, any[]>, artifact) => {
      if (!acc[artifact.type]) acc[artifact.type] = [];
      acc[artifact.type].push(artifact);
      return acc;
    }, {});

    console.log('\n📊 Generated Components:');
    for (const [type, typeArtifacts] of Object.entries(artifactsByType)) {
      console.log(`   ${type}: ${typeArtifacts.length} files`);
    }

    // Write artifacts to output directory
    const outputPath = path.join(process.cwd(), 'generated-projects', 'techmart-pro');
    console.log(`\n💾 Writing artifacts to: ${outputPath}`);
    await writeArtifactsToDirectory(artifacts, outputPath);

    console.log('\n🔄 Phase 2: Validation and Quality Assurance...');
    console.log('===============================================');

    // Perform comprehensive validation
    const validator = new IntegrationValidationSystem();
    const validationResult = await validator.validateGeneratedApplication(
      artifacts,
      requirements,
      outputPath
    );

    console.log('\n📋 Validation Results:');
    console.log('======================');
    console.log(`Overall Valid: ${validationResult.overallValid ? '✅ YES' : '❌ NO'}`);
    console.log(`Compilation: ${validationResult.compilation.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Runtime: ${validationResult.runtime.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Integration: ${validationResult.integration.success ? '✅ PASS' : '❌ FAIL'}`);

    if (!validationResult.compilation.success) {
      console.log('\n❌ Compilation Errors:');
      validationResult.compilation.errors.forEach(error => {
        console.log(`   ${error.file}:${error.line} - ${error.message}`);
      });
    }

    if (validationResult.compilation.warnings.length > 0) {
      console.log('\n⚠️ Compilation Warnings:');
      validationResult.compilation.warnings.forEach(warning => {
        console.log(`   ${warning.file}:${warning.line} - ${warning.message}`);
      });
    }

    console.log('\n🔄 Phase 3: Detailed Analysis...');
    console.log('=================================');

    // Analyze specific components
    await analyzeGeneratedComponents(artifacts, outputPath);

    console.log('\n🔄 Phase 4: Test Suite Analysis...');
    console.log('===================================');

    // Analyze test coverage
    const testArtifacts = artifacts.filter(a => a.type === 'tests');
    await analyzeTestSuite(testArtifacts);

    console.log('\n🔄 Phase 5: Production Readiness Assessment...');
    console.log('===============================================');

    // Assess production readiness
    const readinessScore = await assessProductionReadiness(artifacts, validationResult);

    console.log('\n🔄 Phase 6: Generating Comprehensive Report...');
    console.log('==============================================');

    // Generate comprehensive test report
    const report = generateComprehensiveReport(
      requirements,
      artifacts,
      validationResult,
      readinessScore,
      startTime
    );

    fs.writeFileSync(path.join(outputPath, 'GENERATION_REPORT.md'), report, 'utf8');

    // Generate setup instructions
    const setupInstructions = generateSetupInstructions(requirements, artifacts);
    fs.writeFileSync(path.join(outputPath, 'SETUP_INSTRUCTIONS.md'), setupInstructions, 'utf8');

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\n🎉 Tech E-commerce Website Generation Complete!');
    console.log('===============================================');
    console.log(`⏱️ Total Generation Time: ${duration.toFixed(2)} seconds`);
    console.log(`📁 Output Directory: ${outputPath}`);
    console.log(`📄 Files Generated: ${artifacts.length}`);
    console.log(`📝 Lines of Code: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}`);
    console.log(`🧪 Test Files: ${testArtifacts.length}`);
    console.log(`✅ Production Ready: ${validationResult.overallValid ? 'YES' : 'NO'}`);
    console.log(`📊 Readiness Score: ${readinessScore.toFixed(1)}%`);

    console.log('\n🚀 Next Steps:');
    console.log('==============');
    console.log('1. Review the generated GENERATION_REPORT.md for detailed analysis');
    console.log('2. Follow SETUP_INSTRUCTIONS.md to run the application');
    console.log('3. Execute the comprehensive test suite');
    console.log('4. Deploy to your preferred platform');
    console.log('5. Customize as needed for your specific requirements');

    if (validationResult.overallValid) {
      console.log('\n✨ The generated tech e-commerce website is production-ready!');
      console.log('   All validation checks passed and the application can be deployed immediately.');
    } else {
      console.log('\n⚠️ Some validation issues were found. Please review the report for details.');
    }

  } catch (error) {
    logger.error('Tech e-commerce website generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    console.log('\n❌ Generation Failed!');
    console.log('====================');
    console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
    
    throw error;
  }
}

/**
 * Write artifacts to directory structure
 */
async function writeArtifactsToDirectory(artifacts: any[], outputPath: string): Promise<void> {
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
  }

  for (const artifact of artifacts) {
    const fullPath = path.join(outputPath, artifact.filePath);
    const dir = path.dirname(fullPath);

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(fullPath, artifact.content, 'utf8');
  }
}

/**
 * Analyze generated components
 */
async function analyzeGeneratedComponents(artifacts: any[], outputPath: string): Promise<void> {
  console.log('\n📊 Component Analysis:');
  
  // Analyze OpenAPI contract
  const contractArtifact = artifacts.find(a => a.type === 'contract');
  if (contractArtifact) {
    const contract = JSON.parse(contractArtifact.content);
    const pathCount = Object.keys(contract.paths || {}).length;
    const schemaCount = Object.keys(contract.components?.schemas || {}).length;
    console.log(`   📋 API Contract: ${pathCount} endpoints, ${schemaCount} schemas`);
  }

  // Analyze database schema
  const databaseArtifact = artifacts.find(a => a.type === 'database');
  if (databaseArtifact) {
    const dbData = JSON.parse(databaseArtifact.content);
    const tableCount = dbData.schema?.tables?.length || 0;
    const relationshipCount = dbData.schema?.relationships?.length || 0;
    console.log(`   🗄️ Database: ${tableCount} tables, ${relationshipCount} relationships`);
  }

  // Analyze backend components
  const backendArtifacts = artifacts.filter(a => a.type === 'backend');
  console.log(`   ⚙️ Backend: ${backendArtifacts.length} files`);

  // Analyze frontend components
  const frontendArtifacts = artifacts.filter(a => a.type === 'frontend');
  console.log(`   🎨 Frontend: ${frontendArtifacts.length} files`);

  // Analyze deployment configs
  const deploymentArtifacts = artifacts.filter(a => a.type === 'deployment');
  console.log(`   🚀 Deployment: ${deploymentArtifacts.length} files`);
}

/**
 * Analyze test suite
 */
async function analyzeTestSuite(testArtifacts: any[]): Promise<void> {
  console.log('\n🧪 Test Suite Analysis:');
  
  const testsByType = testArtifacts.reduce((acc: Record<string, number>, artifact) => {
    const type = artifact.name.split('-')[0];
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  for (const [type, count] of Object.entries(testsByType)) {
    console.log(`   ${type}: ${count} test files`);
  }

  const totalTestLines = testArtifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0);
  console.log(`   📝 Total test code lines: ${totalTestLines}`);
}

/**
 * Assess production readiness
 */
async function assessProductionReadiness(artifacts: any[], validationResult: any): Promise<number> {
  let score = 0;
  const maxScore = 100;

  // Compilation (25 points)
  if (validationResult.compilation.success) {
    score += 25;
  } else {
    score += Math.max(0, 25 - validationResult.compilation.errors.length * 5);
  }

  // Runtime validation (25 points)
  if (validationResult.runtime.success) {
    score += 25;
  }

  // Integration tests (25 points)
  if (validationResult.integration.success) {
    score += 25;
  }

  // Component completeness (25 points)
  const requiredTypes = ['contract', 'database', 'backend', 'frontend', 'tests'];
  const presentTypes = [...new Set(artifacts.map(a => a.type))];
  const completeness = presentTypes.filter(type => requiredTypes.includes(type)).length / requiredTypes.length;
  score += completeness * 25;

  return Math.min(score, maxScore);
}

/**
 * Generate comprehensive report
 */
function generateComprehensiveReport(
  requirements: ApplicationRequirements,
  artifacts: any[],
  validationResult: any,
  readinessScore: number,
  startTime: number
): string {
  const duration = (Date.now() - startTime) / 1000;
  const artifactsByType = artifacts.reduce((acc: Record<string, any[]>, artifact) => {
    if (!acc[artifact.type]) acc[artifact.type] = [];
    acc[artifact.type].push(artifact);
    return acc;
  }, {});

  return `# Tech E-commerce Website Generation Report

## Project Overview

**Application Name**: ${requirements.name}
**Description**: ${requirements.description}
**Business Domain**: ${requirements.businessDomain}
**Framework**: ${requirements.targetFramework}
**Database**: ${requirements.databaseType}
**Deployment Target**: ${requirements.deploymentTarget}

## Generation Summary

**Generation Time**: ${duration.toFixed(2)} seconds
**Total Artifacts**: ${artifacts.length} files
**Total Lines of Code**: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}
**Production Readiness Score**: ${readinessScore.toFixed(1)}%

## Component Breakdown

${Object.entries(artifactsByType).map(([type, typeArtifacts]) => `
### ${type.charAt(0).toUpperCase() + type.slice(1)} Components
- **Files Generated**: ${typeArtifacts.length}
- **Lines of Code**: ${typeArtifacts.reduce((sum: number, a: any) => sum + a.content.split('\n').length, 0)}
- **Files**: ${typeArtifacts.map((a: any) => `\`${a.filePath}\``).join(', ')}
`).join('')}

## Validation Results

### Compilation Validation
- **Status**: ${validationResult.compilation.success ? '✅ PASSED' : '❌ FAILED'}
- **Errors**: ${validationResult.compilation.errors.length}
- **Warnings**: ${validationResult.compilation.warnings.length}

${validationResult.compilation.errors.length > 0 ? `
#### Compilation Errors
${validationResult.compilation.errors.map((error: any) => `- \`${error.file}:${error.line}\` - ${error.message}`).join('\n')}
` : ''}

### Runtime Validation
- **Status**: ${validationResult.runtime.success ? '✅ PASSED' : '❌ FAILED'}
- **Database Connection**: ${validationResult.runtime.databaseConnection ? '✅' : '❌'}
- **API Endpoints**: ${validationResult.runtime.apiEndpoints.length} tested

### Integration Testing
- **Status**: ${validationResult.integration.success ? '✅ PASSED' : '❌ FAILED'}
- **Test Coverage**: ${validationResult.integration.coverage}%
- **Test Duration**: ${validationResult.integration.duration}ms

## Features Implemented

${requirements.features.map(feature => `- ✅ ${feature}`).join('\n')}

## Quality Metrics

- **Syntax Validation**: ${artifacts.every((a: any) => a.metadata.validationStatus.syntaxValid) ? '✅ PASSED' : '❌ FAILED'}
- **Type Safety**: ${artifacts.every((a: any) => a.metadata.validationStatus.typeValid) ? '✅ PASSED' : '❌ FAILED'}
- **Contract Compliance**: ${artifacts.every((a: any) => a.metadata.contractCompliance.complianceScore > 0.8) ? '✅ PASSED' : '❌ FAILED'}
- **Security Compliance**: ${artifacts.every((a: any) => a.metadata.validationStatus.securityCompliant) ? '✅ PASSED' : '❌ FAILED'}

## Recommendations

${readinessScore >= 90 ? `
🎉 **Excellent!** Your application is production-ready and can be deployed immediately.

**Next Steps:**
1. Deploy to your preferred platform
2. Configure environment variables
3. Set up monitoring and logging
4. Perform user acceptance testing
` : readinessScore >= 70 ? `
✅ **Good!** Your application is mostly ready with minor issues to address.

**Recommended Actions:**
1. Review and fix any compilation errors
2. Complete integration testing
3. Perform security review
4. Optimize performance if needed
` : `
⚠️ **Needs Improvement** - Several issues need to be addressed before production deployment.

**Required Actions:**
1. Fix all compilation errors
2. Complete validation testing
3. Address security concerns
4. Ensure all components are properly integrated
`}

## Technical Architecture

### Database Schema
- **Tables**: ${artifactsByType.database?.[0] ? JSON.parse(artifactsByType.database[0].content).schema.tables.length : 0}
- **Relationships**: ${artifactsByType.database?.[0] ? JSON.parse(artifactsByType.database[0].content).schema.relationships.length : 0}
- **Indexes**: ${artifactsByType.database?.[0] ? JSON.parse(artifactsByType.database[0].content).schema.indexes.length : 0}

### API Endpoints
- **Total Endpoints**: ${artifactsByType.contract?.[0] ? Object.keys(JSON.parse(artifactsByType.contract[0].content).paths).length : 0}
- **Authentication**: JWT-based authentication
- **Authorization**: Role-based access control
- **Validation**: Comprehensive input validation

### Frontend Architecture
- **Framework**: React with TypeScript
- **State Management**: Modern state management solution
- **UI Components**: Responsive and accessible components
- **API Integration**: Type-safe API client

### Testing Coverage
- **API Tests**: ${artifacts.filter(a => a.name.includes('api-test')).length} files
- **Database Tests**: ${artifacts.filter(a => a.name.includes('database-test')).length} files
- **E2E Tests**: ${artifacts.filter(a => a.name.includes('e2e-test')).length} files
- **Security Tests**: ${artifacts.filter(a => a.name.includes('security-test')).length} files
- **Performance Tests**: ${artifacts.filter(a => a.name.includes('performance-test')).length} files

Generated on: ${new Date().toISOString()}
`;
}

/**
 * Generate setup instructions
 */
function generateSetupInstructions(requirements: ApplicationRequirements, artifacts: any[]): string {
  return `# TechMart Pro - Setup Instructions

## Prerequisites

- Node.js 18+ 
- PostgreSQL 13+
- Docker and Docker Compose (optional)
- Git

## Quick Start

### 1. Environment Setup

\`\`\`bash
# Clone or extract the generated project
cd techmart-pro

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
\`\`\`

### 2. Database Setup

\`\`\`bash
# Create PostgreSQL database
createdb techmart_pro

# Set up environment variables
cp backend/.env.example backend/.env
# Edit backend/.env with your database credentials

# Run database migrations
cd backend
npm run migrate

# Seed initial data
npm run seed
\`\`\`

### 3. Start the Application

#### Option A: Development Mode

\`\`\`bash
# Terminal 1: Start backend
cd backend
npm run dev

# Terminal 2: Start frontend
cd frontend
npm run dev
\`\`\`

#### Option B: Docker Compose

\`\`\`bash
# Start all services
docker-compose up --build
\`\`\`

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api-docs

## Testing

### Run All Tests
\`\`\`bash
npm run test:all
\`\`\`

### Run Specific Test Types
\`\`\`bash
npm run test:api          # API integration tests
npm run test:database     # Database tests
npm run test:e2e          # End-to-end tests
npm run test:security     # Security tests
npm run test:performance  # Performance tests
\`\`\`

### Generate Coverage Report
\`\`\`bash
npm run test:coverage
\`\`\`

## Production Deployment

### Docker Deployment
\`\`\`bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
\`\`\`

### Manual Deployment
\`\`\`bash
# Build frontend
cd frontend
npm run build

# Build backend
cd ../backend
npm run build

# Start production server
npm start
\`\`\`

## Configuration

### Environment Variables

#### Backend (.env)
\`\`\`
DATABASE_URL=postgresql://user:password@localhost:5432/techmart_pro
JWT_SECRET=your-jwt-secret
STRIPE_SECRET_KEY=your-stripe-key
EMAIL_SERVICE_API_KEY=your-email-key
\`\`\`

#### Frontend (.env)
\`\`\`
REACT_APP_API_URL=http://localhost:3001
REACT_APP_STRIPE_PUBLIC_KEY=your-stripe-public-key
\`\`\`

## Features

✅ **User Management**: Registration, authentication, profiles
✅ **Product Catalog**: Advanced search, filtering, categories
✅ **Shopping Cart**: Add, remove, save for later
✅ **Checkout**: Secure payment processing
✅ **Order Management**: Tracking, history, status updates
✅ **Admin Dashboard**: Product management, analytics
✅ **Reviews & Ratings**: Customer feedback system
✅ **Inventory Management**: Real-time stock tracking
✅ **Email Notifications**: Order confirmations, updates
✅ **Mobile Responsive**: Progressive web app

## Support

For issues or questions:
1. Check the GENERATION_REPORT.md for detailed analysis
2. Review test results for functionality validation
3. Check logs for runtime issues
4. Refer to API documentation for endpoint details

## Customization

The generated code is fully customizable:
- Modify UI components in \`frontend/src/components/\`
- Update business logic in \`backend/src/services/\`
- Extend API endpoints in \`backend/src/controllers/\`
- Add new database tables in \`backend/src/models/\`

Generated with Contract-First LLM Generation System
`;
}

// Run the test if this file is executed directly
if (require.main === module) {
  generateTechEcommerceWebsite().catch(console.error);
}
