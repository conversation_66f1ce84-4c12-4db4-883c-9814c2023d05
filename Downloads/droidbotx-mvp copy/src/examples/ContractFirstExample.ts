/**
 * Contract-First LLM Generation System Example
 * 
 * Demonstrates the complete contract-first LLM-driven code generation system
 * that produces production-ready applications with full validation.
 */

import { ContractFirstLLMGenerator, ApplicationRequirements } from '../core/ContractFirstLLMGenerator';
import { Logger } from '../core/Logger';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Example: E-commerce Platform Generation
 */
export async function generateEcommercePlatform(): Promise<void> {
  const logger = Logger.getInstance();
  logger.info('Starting e-commerce platform generation example');

  try {
    // Define application requirements
    const requirements: ApplicationRequirements = {
      name: 'ModernEcommerce',
      description: 'A modern e-commerce platform with product catalog, shopping cart, user management, and order processing',
      features: [
        'User registration and authentication',
        'Product catalog with categories',
        'Shopping cart functionality',
        'Order management system',
        'Payment processing integration',
        'Admin dashboard',
        'Inventory management',
        'Customer reviews and ratings',
        'Search and filtering',
        'Email notifications'
      ],
      businessDomain: 'e-commerce',
      targetFramework: 'react-express',
      databaseType: 'postgresql',
      deploymentTarget: 'docker'
    };

    // Initialize the contract-first generator
    const generator = new ContractFirstLLMGenerator();

    // Generate the complete application
    logger.info('Generating complete e-commerce application...');
    const artifacts = await generator.generateApplication(requirements);

    // Write artifacts to output directory
    const outputPath = path.join(process.cwd(), 'generated-projects', 'modern-ecommerce');
    await writeArtifactsToDirectory(artifacts, outputPath);

    logger.info('E-commerce platform generation completed successfully', {
      outputPath,
      artifactCount: artifacts.length,
      totalLines: artifacts.reduce((sum, artifact) => sum + artifact.content.split('\n').length, 0)
    });

    // Display generation summary
    displayGenerationSummary(artifacts, outputPath);

  } catch (error) {
    logger.error('E-commerce platform generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Example: Task Management System Generation
 */
export async function generateTaskManagementSystem(): Promise<void> {
  const logger = Logger.getInstance();
  logger.info('Starting task management system generation example');

  try {
    const requirements: ApplicationRequirements = {
      name: 'TaskMaster',
      description: 'A comprehensive task management system with project organization, team collaboration, and progress tracking',
      features: [
        'Project and task management',
        'Team collaboration tools',
        'Time tracking',
        'File attachments',
        'Comments and discussions',
        'Progress reporting',
        'Deadline management',
        'Role-based permissions',
        'Dashboard and analytics',
        'Mobile responsive design'
      ],
      businessDomain: 'project-management',
      targetFramework: 'nextjs',
      databaseType: 'postgresql',
      deploymentTarget: 'vercel'
    };

    const generator = new ContractFirstLLMGenerator();
    const artifacts = await generator.generateApplication(requirements);

    const outputPath = path.join(process.cwd(), 'generated-projects', 'task-master');
    await writeArtifactsToDirectory(artifacts, outputPath);

    logger.info('Task management system generation completed successfully', {
      outputPath,
      artifactCount: artifacts.length
    });

    displayGenerationSummary(artifacts, outputPath);

  } catch (error) {
    logger.error('Task management system generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Example: Healthcare Management System Generation
 */
export async function generateHealthcareSystem(): Promise<void> {
  const logger = Logger.getInstance();
  logger.info('Starting healthcare management system generation example');

  try {
    const requirements: ApplicationRequirements = {
      name: 'HealthCare Pro',
      description: 'A comprehensive healthcare management system for patient records, appointments, and medical billing',
      features: [
        'Patient registration and records',
        'Appointment scheduling',
        'Medical history tracking',
        'Prescription management',
        'Billing and insurance',
        'Doctor and staff management',
        'Medical reports generation',
        'HIPAA compliance features',
        'Telemedicine integration',
        'Analytics and reporting'
      ],
      businessDomain: 'healthcare',
      targetFramework: 'react-express',
      databaseType: 'postgresql',
      deploymentTarget: 'aws'
    };

    const generator = new ContractFirstLLMGenerator();
    const artifacts = await generator.generateApplication(requirements);

    const outputPath = path.join(process.cwd(), 'generated-projects', 'healthcare-pro');
    await writeArtifactsToDirectory(artifacts, outputPath);

    logger.info('Healthcare management system generation completed successfully', {
      outputPath,
      artifactCount: artifacts.length
    });

    displayGenerationSummary(artifacts, outputPath);

  } catch (error) {
    logger.error('Healthcare management system generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Write artifacts to directory structure
 */
async function writeArtifactsToDirectory(artifacts: any[], outputPath: string): Promise<void> {
  // Create output directory
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
  }

  // Write each artifact to its designated path
  for (const artifact of artifacts) {
    const fullPath = path.join(outputPath, artifact.filePath);
    const dir = path.dirname(fullPath);

    // Create directory if it doesn't exist
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write file content
    fs.writeFileSync(fullPath, artifact.content, 'utf8');
  }

  // Create README.md with setup instructions
  const readmeContent = generateReadmeContent(artifacts);
  fs.writeFileSync(path.join(outputPath, 'README.md'), readmeContent, 'utf8');
}

/**
 * Generate README content with setup instructions
 */
function generateReadmeContent(artifacts: any[]): string {
  return `# Generated Application

This application was generated using the Contract-First LLM Generation System.

## Project Structure

\`\`\`
${generateProjectStructure(artifacts)}
\`\`\`

## Setup Instructions

### Prerequisites
- Node.js 18+ 
- PostgreSQL 13+
- Docker (optional)

### Backend Setup
1. Navigate to the backend directory:
   \`\`\`bash
   cd backend
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Set up environment variables:
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your database credentials
   \`\`\`

4. Run database migrations:
   \`\`\`bash
   npm run migrate
   \`\`\`

5. Start the backend server:
   \`\`\`bash
   npm run dev
   \`\`\`

### Frontend Setup
1. Navigate to the frontend directory:
   \`\`\`bash
   cd frontend
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Start the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

### Docker Setup (Alternative)
1. Build and run with Docker Compose:
   \`\`\`bash
   docker-compose up --build
   \`\`\`

## API Documentation

The API documentation is available at:
- Development: http://localhost:3000/api-docs
- Swagger UI: http://localhost:3000/swagger

## Testing

Run the test suite:
\`\`\`bash
# Backend tests
cd backend && npm test

# Frontend tests  
cd frontend && npm test

# Integration tests
npm run test:integration
\`\`\`

## Deployment

See the deployment documentation in the \`deployment/\` directory for platform-specific instructions.

## Generated Files

This application includes:
- ✅ Complete OpenAPI specification
- ✅ Database schema with migrations
- ✅ Backend API with full CRUD operations
- ✅ Frontend application with TypeScript
- ✅ Authentication and authorization
- ✅ Input validation and error handling
- ✅ Comprehensive test suite
- ✅ Deployment configurations
- ✅ Documentation and setup guides

All code is production-ready and follows industry best practices.
`;
}

/**
 * Generate project structure visualization
 */
function generateProjectStructure(artifacts: any[]): string {
  const structure: Record<string, string[]> = {};
  
  for (const artifact of artifacts) {
    const parts = artifact.filePath.split('/');
    let current = structure;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!current[part]) {
        current[part] = [];
      }
    }
    
    const dir = parts.slice(0, -1).join('/') || '.';
    const file = parts[parts.length - 1];
    
    if (!structure[dir]) {
      structure[dir] = [];
    }
    structure[dir].push(file);
  }

  // Convert to tree structure string
  let result = '';
  for (const [dir, files] of Object.entries(structure)) {
    result += `${dir}/\n`;
    for (const file of files) {
      result += `  ${file}\n`;
    }
  }
  
  return result;
}

/**
 * Display generation summary
 */
function displayGenerationSummary(artifacts: any[], outputPath: string): void {
  console.log('\n🎉 Application Generation Complete!');
  console.log('=====================================');
  console.log(`📁 Output Directory: ${outputPath}`);
  console.log(`📄 Files Generated: ${artifacts.length}`);
  console.log(`📝 Total Lines: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}`);
  
  console.log('\n📋 Generated Components:');
  const componentCounts = artifacts.reduce((counts: Record<string, number>, artifact) => {
    counts[artifact.type] = (counts[artifact.type] || 0) + 1;
    return counts;
  }, {});
  
  for (const [type, count] of Object.entries(componentCounts)) {
    console.log(`   ${type}: ${count} files`);
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Review the generated README.md for setup instructions');
  console.log('2. Install dependencies and configure environment variables');
  console.log('3. Run the application and verify functionality');
  console.log('4. Customize the code as needed for your specific requirements');
  console.log('5. Deploy to your preferred platform');
  
  console.log('\n✨ The generated application is production-ready and includes:');
  console.log('   • Complete API with OpenAPI documentation');
  console.log('   • Database schema with proper relationships');
  console.log('   • Frontend with TypeScript and modern UI');
  console.log('   • Authentication and authorization');
  console.log('   • Comprehensive test suite');
  console.log('   • Deployment configurations');
  console.log('   • Security best practices');
}

/**
 * Main execution function
 */
export async function runExamples(): Promise<void> {
  console.log('🤖 Contract-First LLM Generation System Examples');
  console.log('================================================\n');

  try {
    // Run e-commerce example
    console.log('1. Generating E-commerce Platform...');
    await generateEcommercePlatform();
    
    console.log('\n2. Generating Task Management System...');
    await generateTaskManagementSystem();
    
    console.log('\n3. Generating Healthcare Management System...');
    await generateHealthcareSystem();
    
    console.log('\n🎉 All examples completed successfully!');
    
  } catch (error) {
    console.error('❌ Example execution failed:', error);
    process.exit(1);
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runExamples().catch(console.error);
}
