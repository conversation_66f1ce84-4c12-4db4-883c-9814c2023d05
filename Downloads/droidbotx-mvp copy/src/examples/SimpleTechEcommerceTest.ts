/**
 * Simple Tech E-commerce Test
 * 
 * A simplified test of the Contract-First LLM Generation System
 * creating a basic tech e-commerce website with essential features.
 */

import { ContractFirstLLMGenerator, ApplicationRequirements } from '../core/ContractFirstLLMGenerator';
import { Logger } from '../core/Logger';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Generate a simplified tech e-commerce website
 */
export async function generateSimpleTechEcommerce(): Promise<void> {
  const logger = Logger.getInstance();
  logger.info('🚀 Starting Simple Tech E-commerce Test');

  const startTime = Date.now();

  try {
    // Define simplified requirements
    const requirements: ApplicationRequirements = {
      name: 'TechStore',
      description: 'A simple tech e-commerce platform for electronics and gadgets',
      features: [
        'User registration and login',
        'Product catalog with categories',
        'Shopping cart functionality',
        'Basic checkout process',
        'Order management',
        'Admin product management'
      ],
      businessDomain: 'tech-ecommerce',
      targetFramework: 'react-express',
      databaseType: 'postgresql',
      deploymentTarget: 'docker'
    };

    console.log('\n📋 Simple Tech E-commerce Specifications:');
    console.log('=========================================');
    console.log(`Name: ${requirements.name}`);
    console.log(`Description: ${requirements.description}`);
    console.log(`Features: ${requirements.features.length} core features`);
    console.log(`Framework: ${requirements.targetFramework}`);
    console.log(`Database: ${requirements.databaseType}`);

    // Initialize the generator
    const generator = new ContractFirstLLMGenerator();

    console.log('\n🔄 Generating Complete Application...');
    console.log('====================================');

    // Generate the application
    const artifacts = await generator.generateApplication(requirements);

    console.log(`✅ Generation completed!`);
    console.log(`📄 Total artifacts: ${artifacts.length}`);
    console.log(`📝 Total lines: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}`);

    // Analyze artifacts
    const artifactsByType = artifacts.reduce((acc: Record<string, any[]>, artifact) => {
      if (!acc[artifact.type]) acc[artifact.type] = [];
      acc[artifact.type].push(artifact);
      return acc;
    }, {});

    console.log('\n📊 Generated Components:');
    for (const [type, typeArtifacts] of Object.entries(artifactsByType)) {
      console.log(`   ${type}: ${typeArtifacts.length} files`);
    }

    // Write to output directory
    const outputPath = path.join(process.cwd(), 'generated-projects', 'simple-techstore');
    console.log(`\n💾 Writing to: ${outputPath}`);
    await writeArtifacts(artifacts, outputPath);

    // Generate summary report
    const report = generateSimpleReport(requirements, artifacts, startTime);
    fs.writeFileSync(path.join(outputPath, 'SIMPLE_REPORT.md'), report, 'utf8');

    // Generate quick start guide
    const quickStart = generateQuickStartGuide(requirements);
    fs.writeFileSync(path.join(outputPath, 'QUICK_START.md'), quickStart, 'utf8');

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\n🎉 Simple Tech E-commerce Generation Complete!');
    console.log('==============================================');
    console.log(`⏱️ Generation Time: ${duration.toFixed(2)} seconds`);
    console.log(`📁 Output: ${outputPath}`);
    console.log(`📄 Files: ${artifacts.length}`);
    console.log(`📝 Lines: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}`);

    // Validate key components
    console.log('\n🔍 Component Validation:');
    console.log('========================');
    
    const contractArtifact = artifacts.find(a => a.type === 'contract');
    if (contractArtifact) {
      try {
        const contract = JSON.parse(contractArtifact.content);
        const pathCount = Object.keys(contract.paths || {}).length;
        const schemaCount = Object.keys(contract.components?.schemas || {}).length;
        console.log(`✅ OpenAPI Contract: ${pathCount} endpoints, ${schemaCount} schemas`);
      } catch (e) {
        console.log(`❌ OpenAPI Contract: Invalid JSON`);
      }
    }

    const databaseArtifact = artifacts.find(a => a.type === 'database');
    if (databaseArtifact) {
      try {
        const dbData = JSON.parse(databaseArtifact.content);
        const tableCount = dbData.schema?.tables?.length || 0;
        console.log(`✅ Database Schema: ${tableCount} tables`);
      } catch (e) {
        console.log(`❌ Database Schema: Invalid JSON`);
      }
    }

    const backendCount = artifacts.filter(a => a.type === 'backend').length;
    const frontendCount = artifacts.filter(a => a.type === 'frontend').length;
    const testCount = artifacts.filter(a => a.type === 'tests').length;

    console.log(`✅ Backend Components: ${backendCount} files`);
    console.log(`✅ Frontend Components: ${frontendCount} files`);
    console.log(`✅ Test Files: ${testCount} files`);

    console.log('\n🚀 Next Steps:');
    console.log('==============');
    console.log('1. Review SIMPLE_REPORT.md for detailed analysis');
    console.log('2. Follow QUICK_START.md to run the application');
    console.log('3. Test the generated functionality');
    console.log('4. Customize as needed');

    console.log('\n✨ Simple tech e-commerce website generated successfully!');

  } catch (error) {
    logger.error('Simple tech e-commerce generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    console.log('\n❌ Generation Failed!');
    console.log('====================');
    console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
    
    throw error;
  }
}

/**
 * Write artifacts to directory
 */
async function writeArtifacts(artifacts: any[], outputPath: string): Promise<void> {
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
  }

  for (const artifact of artifacts) {
    const fullPath = path.join(outputPath, artifact.filePath);
    const dir = path.dirname(fullPath);

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(fullPath, artifact.content, 'utf8');
  }
}

/**
 * Generate simple report
 */
function generateSimpleReport(
  requirements: ApplicationRequirements,
  artifacts: any[],
  startTime: number
): string {
  const duration = (Date.now() - startTime) / 1000;
  const artifactsByType = artifacts.reduce((acc: Record<string, any[]>, artifact) => {
    if (!acc[artifact.type]) acc[artifact.type] = [];
    acc[artifact.type].push(artifact);
    return acc;
  }, {});

  return `# Simple Tech E-commerce Generation Report

## Project Overview

**Name**: ${requirements.name}
**Description**: ${requirements.description}
**Domain**: ${requirements.businessDomain}
**Framework**: ${requirements.targetFramework}
**Database**: ${requirements.databaseType}

## Generation Summary

**Time**: ${duration.toFixed(2)} seconds
**Files**: ${artifacts.length}
**Lines**: ${artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)}

## Components Generated

${Object.entries(artifactsByType).map(([type, typeArtifacts]) => `
### ${type.charAt(0).toUpperCase() + type.slice(1)}
- Files: ${typeArtifacts.length}
- Lines: ${typeArtifacts.reduce((sum: number, a: any) => sum + a.content.split('\n').length, 0)}
`).join('')}

## Features Implemented

${requirements.features.map(feature => `- ✅ ${feature}`).join('\n')}

## Quality Metrics

- Syntax Valid: ${artifacts.every(a => a.metadata.validationStatus.syntaxValid) ? '✅' : '❌'}
- Type Safe: ${artifacts.every(a => a.metadata.validationStatus.typeValid) ? '✅' : '❌'}
- Contract Compliant: ${artifacts.every(a => a.metadata.contractCompliance.complianceScore > 0.8) ? '✅' : '❌'}

## File Structure

${artifacts.map(a => `- \`${a.filePath}\``).join('\n')}

Generated on: ${new Date().toISOString()}
`;
}

/**
 * Generate quick start guide
 */
function generateQuickStartGuide(requirements: ApplicationRequirements): string {
  return `# ${requirements.name} - Quick Start Guide

## Prerequisites

- Node.js 18+
- PostgreSQL 13+
- npm or yarn

## Setup

### 1. Install Dependencies

\`\`\`bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
\`\`\`

### 2. Database Setup

\`\`\`bash
# Create database
createdb techstore

# Configure environment
cp backend/.env.example backend/.env
# Edit .env with your database URL

# Run migrations
cd backend
npm run migrate
npm run seed
\`\`\`

### 3. Start Application

\`\`\`bash
# Terminal 1: Backend
cd backend
npm run dev

# Terminal 2: Frontend
cd frontend
npm run dev
\`\`\`

### 4. Access Application

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- API Docs: http://localhost:3001/api-docs

## Testing

\`\`\`bash
# Run all tests
npm test

# Run specific test types
npm run test:api
npm run test:e2e
\`\`\`

## Features

✅ User authentication
✅ Product catalog
✅ Shopping cart
✅ Order management
✅ Admin dashboard
✅ Responsive design

## Customization

The generated code is fully customizable:
- Modify components in \`frontend/src/components/\`
- Update API logic in \`backend/src/\`
- Extend database schema in \`backend/migrations/\`

Generated with Contract-First LLM System
`;
}

// Run if executed directly
if (require.main === module) {
  generateSimpleTechEcommerce().catch(console.error);
}
