/**
 * Functional Testing Generator Test
 * 
 * Comprehensive test suite to validate the FunctionalTestingGenerator
 * produces complete, working test suites for generated applications.
 */

import { FunctionalTestingGenerator } from '../core/FunctionalTestingGenerator';
import { OpenAPIContractGenerator } from '../core/OpenAPIContractGenerator';
import { DatabaseSchemaGenerator } from '../core/DatabaseSchemaGenerator';
import { ApplicationRequirements, GeneratedArtifact } from '../core/ContractFirstLLMGenerator';
import { Logger } from '../core/Logger';

describe('FunctionalTestingGenerator', () => {
  let generator: FunctionalTestingGenerator;
  let logger: Logger;

  beforeAll(() => {
    generator = new FunctionalTestingGenerator();
    logger = Logger.getInstance();
  });

  describe('API Test Generation', () => {
    test('should generate comprehensive API tests', async () => {
      const requirements: ApplicationRequirements = {
        name: 'TestAPI',
        description: 'API for testing functional test generation',
        features: ['User management', 'Product catalog', 'Order processing'],
        businessDomain: 'e-commerce',
        targetFramework: 'react-express',
        databaseType: 'postgresql',
        deploymentTarget: 'docker'
      };

      // Create mock OpenAPI contract
      const contractGenerator = new OpenAPIContractGenerator();
      const contractArtifact = await contractGenerator.generateOpenAPIContract(requirements, 'api-test');

      // Create mock database schema
      const schemaGenerator = new DatabaseSchemaGenerator();
      const databaseArtifact = await schemaGenerator.generateDatabaseSchema(contractArtifact, requirements, 'api-test');

      // Create mock backend and frontend artifacts
      const backendArtifacts: GeneratedArtifact[] = [{
        type: 'backend',
        name: 'test-backend',
        content: '// Test backend code',
        filePath: 'backend/src/app.ts',
        dependencies: [],
        exports: [],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'test',
          validationStatus: {
            syntaxValid: true,
            typeValid: true,
            contractCompliant: true,
            securityCompliant: true,
            errors: [],
            warnings: []
          },
          contractCompliance: {
            openApiCompliant: true,
            databaseSchemaCompliant: true,
            typeDefinitionsCompliant: true,
            securityCompliant: true,
            complianceScore: 1.0,
            violations: []
          }
        }
      }];

      const frontendArtifacts: GeneratedArtifact[] = [{
        type: 'frontend',
        name: 'test-frontend',
        content: '// Test frontend code',
        filePath: 'frontend/src/App.tsx',
        dependencies: [],
        exports: [],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'test',
          validationStatus: {
            syntaxValid: true,
            typeValid: true,
            contractCompliant: true,
            securityCompliant: true,
            errors: [],
            warnings: []
          },
          contractCompliance: {
            openApiCompliant: true,
            databaseSchemaCompliant: true,
            typeDefinitionsCompliant: true,
            securityCompliant: true,
            complianceScore: 1.0,
            violations: []
          }
        }
      }];

      // Generate functional tests
      const testArtifacts = await generator.generateFunctionalTests(
        contractArtifact,
        databaseArtifact,
        backendArtifacts,
        frontendArtifacts,
        requirements,
        'functional-test'
      );

      // Validate test artifacts
      expect(testArtifacts).toBeDefined();
      expect(testArtifacts.length).toBeGreaterThan(0);

      // Check for different test types
      const testTypes = testArtifacts.map(a => a.name);
      expect(testTypes.some(name => name.includes('api-test'))).toBe(true);
      expect(testTypes.some(name => name.includes('database-test'))).toBe(true);
      expect(testTypes.some(name => name.includes('e2e-test'))).toBe(true);
      expect(testTypes.some(name => name.includes('security-test'))).toBe(true);
      expect(testTypes.some(name => name.includes('performance-test'))).toBe(true);
      expect(testTypes.some(name => name.includes('integration-test'))).toBe(true);

      // Validate test artifact structure
      for (const artifact of testArtifacts) {
        expect(artifact.type).toBe('tests');
        expect(artifact.content).toBeTruthy();
        expect(artifact.filePath).toBeTruthy();
        expect(artifact.metadata.validationStatus.syntaxValid).toBe(true);
        expect(artifact.metadata.contractCompliance.complianceScore).toBeGreaterThan(0.8);
      }

      logger.info('API test generation validation completed', {
        testArtifactCount: testArtifacts.length,
        testTypes: testTypes.length
      });
    }, 180000); // 3 minutes timeout for comprehensive test generation
  });

  describe('Test Content Validation', () => {
    test('should generate valid test code with proper structure', async () => {
      const requirements: ApplicationRequirements = {
        name: 'ValidationTestApp',
        description: 'App for validating test code structure',
        features: ['Authentication', 'Data management'],
        businessDomain: 'business',
        targetFramework: 'react-express',
        databaseType: 'postgresql',
        deploymentTarget: 'docker'
      };

      // Create minimal test artifacts
      const contractArtifact: GeneratedArtifact = {
        type: 'contract',
        name: 'test-contract',
        content: JSON.stringify({
          openapi: '3.0.3',
          info: { title: 'Test API', version: '1.0.0' },
          paths: {
            '/api/users': {
              get: {
                summary: 'Get users',
                responses: {
                  '200': {
                    description: 'Success',
                    content: {
                      'application/json': {
                        schema: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/User' }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          components: {
            schemas: {
              User: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' }
                }
              }
            }
          }
        }),
        filePath: 'api/openapi.json',
        dependencies: [],
        exports: [],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'test',
          validationStatus: {
            syntaxValid: true,
            typeValid: true,
            contractCompliant: true,
            securityCompliant: true,
            errors: [],
            warnings: []
          },
          contractCompliance: {
            openApiCompliant: true,
            databaseSchemaCompliant: true,
            typeDefinitionsCompliant: true,
            securityCompliant: true,
            complianceScore: 1.0,
            violations: []
          }
        }
      };

      const databaseArtifact: GeneratedArtifact = {
        type: 'database',
        name: 'test-database',
        content: JSON.stringify({
          schema: {
            tables: [{
              name: 'users',
              columns: [
                { name: 'id', type: 'uuid', nullable: false },
                { name: 'name', type: 'varchar', nullable: false },
                { name: 'email', type: 'varchar', nullable: false }
              ],
              primaryKey: ['id'],
              foreignKeys: []
            }],
            relationships: [],
            indexes: [],
            constraints: []
          }
        }),
        filePath: 'database/schema.json',
        dependencies: [],
        exports: [],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'test',
          validationStatus: {
            syntaxValid: true,
            typeValid: true,
            contractCompliant: true,
            securityCompliant: true,
            errors: [],
            warnings: []
          },
          contractCompliance: {
            openApiCompliant: true,
            databaseSchemaCompliant: true,
            typeDefinitionsCompliant: true,
            securityCompliant: true,
            complianceScore: 1.0,
            violations: []
          }
        }
      };

      const testArtifacts = await generator.generateFunctionalTests(
        contractArtifact,
        databaseArtifact,
        [],
        [],
        requirements,
        'validation-test'
      );

      // Validate test code structure
      for (const artifact of testArtifacts) {
        // Check for common test patterns
        expect(artifact.content).toContain('describe');
        expect(artifact.content).toContain('test') || expect(artifact.content).toContain('it');
        expect(artifact.content).toContain('expect');
        
        // Check for proper imports
        if (artifact.content.includes('import')) {
          expect(artifact.content).toMatch(/import.*from/);
        }

        // Check for TypeScript syntax
        if (artifact.filePath.endsWith('.ts')) {
          expect(artifact.content).not.toContain('var ');
          expect(artifact.content).toMatch(/:\s*(string|number|boolean|object|any)/);
        }
      }

      logger.info('Test content validation completed', {
        validatedArtifacts: testArtifacts.length
      });
    }, 120000); // 2 minutes timeout
  });

  describe('Test Coverage Validation', () => {
    test('should generate tests covering all major test categories', async () => {
      const requirements: ApplicationRequirements = {
        name: 'CoverageTestApp',
        description: 'App for validating test coverage',
        features: ['Full feature set', 'Complete workflows'],
        businessDomain: 'comprehensive',
        targetFramework: 'react-express',
        databaseType: 'postgresql',
        deploymentTarget: 'docker'
      };

      // Create comprehensive contract
      const contractGenerator = new OpenAPIContractGenerator();
      const contractArtifact = await contractGenerator.generateOpenAPIContract(requirements, 'coverage-test');

      const schemaGenerator = new DatabaseSchemaGenerator();
      const databaseArtifact = await schemaGenerator.generateDatabaseSchema(contractArtifact, requirements, 'coverage-test');

      const testArtifacts = await generator.generateFunctionalTests(
        contractArtifact,
        databaseArtifact,
        [],
        [],
        requirements,
        'coverage-test'
      );

      // Check for comprehensive test coverage
      const testCategories = {
        api: false,
        database: false,
        e2e: false,
        security: false,
        performance: false,
        integration: false,
        config: false,
        utils: false,
        data: false,
        docs: false
      };

      for (const artifact of testArtifacts) {
        if (artifact.name.includes('api-test')) testCategories.api = true;
        if (artifact.name.includes('database-test')) testCategories.database = true;
        if (artifact.name.includes('e2e-test')) testCategories.e2e = true;
        if (artifact.name.includes('security-test')) testCategories.security = true;
        if (artifact.name.includes('performance-test')) testCategories.performance = true;
        if (artifact.name.includes('integration-test')) testCategories.integration = true;
        if (artifact.name.includes('test-config')) testCategories.config = true;
        if (artifact.name.includes('test-util')) testCategories.utils = true;
        if (artifact.name.includes('test-data')) testCategories.data = true;
        if (artifact.name.includes('test-doc')) testCategories.docs = true;
      }

      // Validate all categories are covered
      for (const [category, covered] of Object.entries(testCategories)) {
        expect(covered).toBe(true);
      }

      logger.info('Test coverage validation completed', {
        totalCategories: Object.keys(testCategories).length,
        coveredCategories: Object.values(testCategories).filter(Boolean).length
      });
    }, 240000); // 4 minutes timeout for comprehensive coverage test
  });

  describe('Test Quality Validation', () => {
    test('should generate production-ready test code', async () => {
      const requirements: ApplicationRequirements = {
        name: 'QualityTestApp',
        description: 'App for validating test quality',
        features: ['Quality assurance', 'Production readiness'],
        businessDomain: 'quality',
        targetFramework: 'react-express',
        databaseType: 'postgresql',
        deploymentTarget: 'docker'
      };

      const contractGenerator = new OpenAPIContractGenerator();
      const contractArtifact = await contractGenerator.generateOpenAPIContract(requirements, 'quality-test');

      const schemaGenerator = new DatabaseSchemaGenerator();
      const databaseArtifact = await schemaGenerator.generateDatabaseSchema(contractArtifact, requirements, 'quality-test');

      const testArtifacts = await generator.generateFunctionalTests(
        contractArtifact,
        databaseArtifact,
        [],
        [],
        requirements,
        'quality-test'
      );

      // Validate test quality metrics
      for (const artifact of testArtifacts) {
        // Check for proper error handling
        if (artifact.content.includes('try') || artifact.content.includes('catch')) {
          expect(artifact.content).toMatch(/try\s*{[\s\S]*}\s*catch/);
        }

        // Check for proper async/await usage
        if (artifact.content.includes('async')) {
          expect(artifact.content).toMatch(/async\s+\w+/);
          expect(artifact.content).toContain('await');
        }

        // Check for proper test descriptions
        expect(artifact.content).toMatch(/describe\s*\(\s*['"`][^'"`]+['"`]/);
        expect(artifact.content).toMatch(/(test|it)\s*\(\s*['"`][^'"`]+['"`]/);

        // Check for proper assertions
        expect(artifact.content).toMatch(/expect\s*\(/);

        // Validate no syntax errors in basic structure
        expect(artifact.content).not.toContain('undefined');
        expect(artifact.content).not.toContain('null');
        expect(artifact.content).not.toContain('{{');
        expect(artifact.content).not.toContain('}}');
      }

      logger.info('Test quality validation completed', {
        qualityCheckedArtifacts: testArtifacts.length
      });
    }, 180000); // 3 minutes timeout
  });
});
