/**
 * Contract-First API Development System
 * Generates OpenAPI specifications from business logic and ensures type safety across all layers
 */

import { Logger } from './Logger';

// Define a minimal interface for business logic data needed for API generation
interface BusinessLogicData {
  projectName?: string;
  description?: string;
  domainAPIs?: Record<string, any>;
  databaseSchema?: {
    tables?: Record<string, any>;
    [key: string]: any;
  };
}

export interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description: string;
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  paths: Record<string, any>;
  components: {
    schemas: Record<string, any>;
    securitySchemes?: Record<string, any>;
  };
}

export interface TypeScriptInterface {
  name: string;
  properties: Record<string, {
    type: string;
    required: boolean;
    description?: string;
  }>;
  imports?: string[];
}

export interface APIClientCode {
  typescript: string;
  react: string;
  hooks: string;
}

export class ContractFirstAPIGenerator {
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance();
  }

  /**
   * Generate OpenAPI specification from business logic
   */
  async generateOpenAPISpec(businessLogic: BusinessLogicData): Promise<OpenAPISpec> {
    this.logger.info('Generating OpenAPI specification from business logic');

    const spec: OpenAPISpec = {
      openapi: '3.0.3',
      info: {
        title: businessLogic.projectName || 'Generated API',
        version: '1.0.0',
        description: businessLogic.description || 'Auto-generated API from business logic'
      },
      servers: [
        {
          url: 'http://localhost:3001/api',
          description: 'Development server'
        },
        {
          url: 'https://api.example.com',
          description: 'Production server'
        }
      ],
      paths: {},
      components: {
        schemas: {},
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      }
    };

    // Generate schemas from database schema
    if (businessLogic.databaseSchema?.tables) {
      for (const [tableName, table] of Object.entries(businessLogic.databaseSchema.tables)) {
        const schemaName = this.toPascalCase(tableName);
        spec.components.schemas[schemaName] = this.generateSchemaFromTable(table);
        spec.components.schemas[`Create${schemaName}Request`] = this.generateCreateRequestSchema(table);
        spec.components.schemas[`Update${schemaName}Request`] = this.generateUpdateRequestSchema(table);
      }
    }

    // Generate paths from domain APIs
    if (businessLogic.domainAPIs) {
      for (const [apiName, apiConfig] of Object.entries(businessLogic.domainAPIs)) {
        if (apiName && typeof apiName === 'string') {
          const basePath = `/${apiName.toLowerCase()}`;
          this.generateCRUDPaths(spec, basePath, apiName, apiConfig);
        }
      }
    }

    this.logger.info('OpenAPI specification generated successfully', {
      pathCount: Object.keys(spec.paths).length,
      schemaCount: Object.keys(spec.components.schemas).length
    });

    return spec;
  }

  /**
   * Generate TypeScript interfaces from OpenAPI spec
   */
  generateTypeScriptInterfaces(spec: OpenAPISpec): TypeScriptInterface[] {
    this.logger.info('Generating TypeScript interfaces from OpenAPI spec');

    const interfaces: TypeScriptInterface[] = [];

    // Generate interfaces from schemas
    for (const [schemaName, schema] of Object.entries(spec.components.schemas)) {
      const tsInterface = this.convertSchemaToInterface(schemaName, schema);
      interfaces.push(tsInterface);
    }

    this.logger.info('TypeScript interfaces generated', { count: interfaces.length });
    return interfaces;
  }

  /**
   * Generate typed API client code
   */
  generateAPIClient(spec: OpenAPISpec): APIClientCode {
    this.logger.info('Generating typed API client code');

    const typescript = this.generateTypeScriptClient(spec);
    const react = this.generateReactClient(spec);
    const hooks = this.generateReactHooks(spec);

    return { typescript, react, hooks };
  }

  private generateSchemaFromTable(table: any): any {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    for (const [columnName, column] of Object.entries(table.columns || {})) {
      const columnDef = column as any;
      properties[columnName] = {
        type: this.mapDatabaseTypeToOpenAPI(columnDef.type),
        description: columnDef.comment || `${columnName} field`
      };

      if (columnDef.nullable === false || columnDef.primaryKey) {
        required.push(columnName);
      }
    }

    return {
      type: 'object',
      properties,
      required
    };
  }

  private generateCreateRequestSchema(table: any): any {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    for (const [columnName, column] of Object.entries(table.columns || {})) {
      const columnDef = column as any;
      
      // Skip auto-generated fields
      if (columnDef.primaryKey && columnDef.autoIncrement) continue;
      if (columnName === 'created_at' || columnName === 'updated_at') continue;

      properties[columnName] = {
        type: this.mapDatabaseTypeToOpenAPI(columnDef.type),
        description: columnDef.comment || `${columnName} field`
      };

      if (columnDef.nullable === false) {
        required.push(columnName);
      }
    }

    return {
      type: 'object',
      properties,
      required
    };
  }

  private generateUpdateRequestSchema(table: any): any {
    const createSchema = this.generateCreateRequestSchema(table);
    return {
      ...createSchema,
      required: [] // All fields optional for updates
    };
  }

  private generateCRUDPaths(spec: OpenAPISpec, basePath: string, entityName: string, apiConfig: any): void {
    const entitySchema = this.toPascalCase(entityName);
    
    // GET /entities - List all
    spec.paths[basePath] = {
      get: {
        summary: `List all ${entityName}`,
        tags: [entityName],
        parameters: [
          {
            name: 'page',
            in: 'query',
            schema: { type: 'integer', default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            schema: { type: 'integer', default: 10 }
          }
        ],
        responses: {
          '200': {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    data: {
                      type: 'array',
                      items: { $ref: `#/components/schemas/${entitySchema}` }
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        page: { type: 'integer' },
                        limit: { type: 'integer' },
                        total: { type: 'integer' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      post: {
        summary: `Create new ${entityName}`,
        tags: [entityName],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: `#/components/schemas/Create${entitySchema}Request` }
            }
          }
        },
        responses: {
          '201': {
            description: 'Created successfully',
            content: {
              'application/json': {
                schema: { $ref: `#/components/schemas/${entitySchema}` }
              }
            }
          }
        }
      }
    };

    // GET /entities/:id - Get by ID
    spec.paths[`${basePath}/{id}`] = {
      get: {
        summary: `Get ${entityName} by ID`,
        tags: [entityName],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' }
          }
        ],
        responses: {
          '200': {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: { $ref: `#/components/schemas/${entitySchema}` }
              }
            }
          },
          '404': {
            description: 'Not found'
          }
        }
      },
      put: {
        summary: `Update ${entityName}`,
        tags: [entityName],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: `#/components/schemas/Update${entitySchema}Request` }
            }
          }
        },
        responses: {
          '200': {
            description: 'Updated successfully',
            content: {
              'application/json': {
                schema: { $ref: `#/components/schemas/${entitySchema}` }
              }
            }
          }
        }
      },
      delete: {
        summary: `Delete ${entityName}`,
        tags: [entityName],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' }
          }
        ],
        responses: {
          '204': {
            description: 'Deleted successfully'
          }
        }
      }
    };
  }

  private convertSchemaToInterface(name: string, schema: any): TypeScriptInterface {
    const properties: Record<string, any> = {};
    const required = schema.required || [];

    for (const [propName, propSchema] of Object.entries(schema.properties || {})) {
      const prop = propSchema as any;
      properties[propName] = {
        type: this.mapOpenAPITypeToTypeScript(prop.type, prop.format),
        required: required.includes(propName),
        description: prop.description
      };
    }

    return {
      name,
      properties,
      imports: []
    };
  }

  private generateTypeScriptClient(spec: OpenAPISpec): string {
    // Generate TypeScript API client code
    return `// Auto-generated TypeScript API client
export class APIClient {
  constructor(private baseURL: string, private token?: string) {}
  
  // Implementation will be generated based on OpenAPI spec
}`;
  }

  private generateReactClient(spec: OpenAPISpec): string {
    // Generate React-specific API client
    return `// Auto-generated React API client
import { APIClient } from './api-client';

export const useAPIClient = () => {
  // React hooks implementation
};`;
  }

  private generateReactHooks(spec: OpenAPISpec): string {
    // Generate React hooks for API calls
    return `// Auto-generated React hooks
export const useQuery = () => {
  // Query hooks implementation
};`;
  }

  private mapDatabaseTypeToOpenAPI(dbType: string): string {
    const typeMap: Record<string, string> = {
      'varchar': 'string',
      'text': 'string',
      'integer': 'integer',
      'int': 'integer',
      'bigint': 'integer',
      'decimal': 'number',
      'float': 'number',
      'boolean': 'boolean',
      'timestamp': 'string',
      'date': 'string',
      'json': 'object'
    };

    return typeMap[dbType.toLowerCase()] || 'string';
  }

  private mapOpenAPITypeToTypeScript(type: string, format?: string): string {
    if (type === 'integer') return 'number';
    if (type === 'number') return 'number';
    if (type === 'boolean') return 'boolean';
    if (type === 'string' && format === 'date-time') return 'Date';
    if (type === 'object') return 'Record<string, any>';
    return 'string';
  }

  private toPascalCase(str: string): string {
    return str.replace(/(^\w|_\w)/g, (match) => 
      match.replace('_', '').toUpperCase()
    );
  }
}
