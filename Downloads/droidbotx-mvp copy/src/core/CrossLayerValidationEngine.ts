/**
 * Cross-Layer Validation Engine
 * 
 * Ensures consistency and integration between database, backend, and frontend layers
 * through comprehensive validation and contract compliance checking.
 */

import { Logger } from './Logger';
import { LLMProviderSystem } from './LLMProviderSystem';
import { 
  GeneratedArtifact, 
  OpenAPISpecification, 
  DatabaseSchema,
  ValidationStatus,
  ContractCompliance,
  ValidationError,
  ValidationWarning,
  ContractViolation
} from './ContractFirstLLMGenerator';

export interface LayerValidationResult {
  layerName: string;
  isValid: boolean;
  contractCompliant: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  violations: ContractViolation[];
  complianceScore: number;
}

export interface CrossLayerValidationResult {
  overallValid: boolean;
  overallComplianceScore: number;
  layerResults: LayerValidationResult[];
  crossLayerIssues: CrossLayerIssue[];
  recommendations: ValidationRecommendation[];
}

export interface CrossLayerIssue {
  type: 'type-mismatch' | 'missing-endpoint' | 'schema-inconsistency' | 'security-gap' | 'integration-failure';
  severity: 'critical' | 'major' | 'minor';
  description: string;
  affectedLayers: string[];
  expectedValue: any;
  actualValues: Record<string, any>;
  autoFixable: boolean;
  fixSuggestion?: string;
}

export interface ValidationRecommendation {
  type: 'performance' | 'security' | 'maintainability' | 'integration';
  priority: 'high' | 'medium' | 'low';
  description: string;
  implementation: string;
  estimatedEffort: 'low' | 'medium' | 'high';
}

export class CrossLayerValidationEngine {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Validate database-contract consistency
   */
  public async validateDatabaseContractConsistency(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact
  ): Promise<LayerValidationResult> {
    this.logger.info('Validating database-contract consistency');

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);
      const databaseData = JSON.parse(databaseArtifact.content);
      const databaseSchema: DatabaseSchema = databaseData.schema;

      // Validate schema compliance using LLM
      const validationResult = await this.performLLMValidation(
        'database-contract',
        openApiSpec,
        databaseSchema,
        'Validate that the database schema exactly matches the OpenAPI contract specifications'
      );

      // Perform structural validation
      const structuralIssues = await this.validateDatabaseStructure(openApiSpec, databaseSchema);

      // Combine results
      const allErrors = [...validationResult.errors, ...structuralIssues.errors];
      const allWarnings = [...validationResult.warnings, ...structuralIssues.warnings];

      return {
        layerName: 'database',
        isValid: allErrors.length === 0,
        contractCompliant: validationResult.contractCompliant,
        errors: allErrors,
        warnings: allWarnings,
        violations: validationResult.violations,
        complianceScore: this.calculateComplianceScore(allErrors, allWarnings)
      };

    } catch (error) {
      this.logger.error('Database-contract validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        layerName: 'database',
        isValid: false,
        contractCompliant: false,
        errors: [{
          type: 'runtime',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
          location: 'validation-engine',
          severity: 'error'
        }],
        warnings: [],
        violations: [],
        complianceScore: 0
      };
    }
  }

  /**
   * Validate backend-contract consistency
   */
  public async validateBackendContractConsistency(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact,
    backendArtifacts: GeneratedArtifact[]
  ): Promise<LayerValidationResult> {
    this.logger.info('Validating backend-contract consistency');

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);
      const databaseData = JSON.parse(databaseArtifact.content);
      const databaseSchema: DatabaseSchema = databaseData.schema;

      // Combine all backend code
      const backendCode = backendArtifacts.map(artifact => ({
        filePath: artifact.filePath,
        content: artifact.content,
        type: artifact.type
      }));

      // Validate backend compliance using LLM
      const validationResult = await this.performLLMValidation(
        'backend-contract',
        { openApiSpec, databaseSchema, backendCode },
        null,
        'Validate that the backend code exactly implements the OpenAPI contract and matches the database schema'
      );

      // Perform endpoint validation
      const endpointIssues = await this.validateBackendEndpoints(openApiSpec, backendArtifacts);

      // Perform model validation
      const modelIssues = await this.validateBackendModels(databaseSchema, backendArtifacts);

      const allErrors = [...validationResult.errors, ...endpointIssues.errors, ...modelIssues.errors];
      const allWarnings = [...validationResult.warnings, ...endpointIssues.warnings, ...modelIssues.warnings];

      return {
        layerName: 'backend',
        isValid: allErrors.length === 0,
        contractCompliant: validationResult.contractCompliant,
        errors: allErrors,
        warnings: allWarnings,
        violations: validationResult.violations,
        complianceScore: this.calculateComplianceScore(allErrors, allWarnings)
      };

    } catch (error) {
      this.logger.error('Backend-contract validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        layerName: 'backend',
        isValid: false,
        contractCompliant: false,
        errors: [{
          type: 'runtime',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
          location: 'validation-engine',
          severity: 'error'
        }],
        warnings: [],
        violations: [],
        complianceScore: 0
      };
    }
  }

  /**
   * Validate frontend-backend consistency
   */
  public async validateFrontendBackendConsistency(
    contractArtifact: GeneratedArtifact,
    backendArtifacts: GeneratedArtifact[],
    frontendArtifacts: GeneratedArtifact[]
  ): Promise<LayerValidationResult> {
    this.logger.info('Validating frontend-backend consistency');

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);

      // Combine all code
      const backendCode = backendArtifacts.map(artifact => ({
        filePath: artifact.filePath,
        content: artifact.content
      }));

      const frontendCode = frontendArtifacts.map(artifact => ({
        filePath: artifact.filePath,
        content: artifact.content
      }));

      // Validate frontend-backend integration using LLM
      const validationResult = await this.performLLMValidation(
        'frontend-backend',
        { openApiSpec, backendCode, frontendCode },
        null,
        'Validate that the frontend code correctly integrates with the backend APIs and matches the OpenAPI contract'
      );

      // Perform API integration validation
      const apiIssues = await this.validateAPIIntegration(openApiSpec, frontendArtifacts, backendArtifacts);

      // Perform type consistency validation
      const typeIssues = await this.validateTypeConsistency(frontendArtifacts, backendArtifacts);

      const allErrors = [...validationResult.errors, ...apiIssues.errors, ...typeIssues.errors];
      const allWarnings = [...validationResult.warnings, ...apiIssues.warnings, ...typeIssues.warnings];

      return {
        layerName: 'frontend',
        isValid: allErrors.length === 0,
        contractCompliant: validationResult.contractCompliant,
        errors: allErrors,
        warnings: allWarnings,
        violations: validationResult.violations,
        complianceScore: this.calculateComplianceScore(allErrors, allWarnings)
      };

    } catch (error) {
      this.logger.error('Frontend-backend validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        layerName: 'frontend',
        isValid: false,
        contractCompliant: false,
        errors: [{
          type: 'runtime',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
          location: 'validation-engine',
          severity: 'error'
        }],
        warnings: [],
        violations: [],
        complianceScore: 0
      };
    }
  }

  /**
   * Perform comprehensive cross-layer validation
   */
  public async performCrossLayerValidation(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact,
    backendArtifacts: GeneratedArtifact[],
    frontendArtifacts: GeneratedArtifact[]
  ): Promise<CrossLayerValidationResult> {
    this.logger.info('Performing comprehensive cross-layer validation');

    try {
      // Validate each layer
      const databaseResult = await this.validateDatabaseContractConsistency(contractArtifact, databaseArtifact);
      const backendResult = await this.validateBackendContractConsistency(contractArtifact, databaseArtifact, backendArtifacts);
      const frontendResult = await this.validateFrontendBackendConsistency(contractArtifact, backendArtifacts, frontendArtifacts);

      const layerResults = [databaseResult, backendResult, frontendResult];

      // Identify cross-layer issues
      const crossLayerIssues = await this.identifyCrossLayerIssues(
        contractArtifact,
        databaseArtifact,
        backendArtifacts,
        frontendArtifacts,
        layerResults
      );

      // Generate recommendations
      const recommendations = await this.generateValidationRecommendations(layerResults, crossLayerIssues);

      const overallValid = layerResults.every(result => result.isValid) && crossLayerIssues.length === 0;
      const overallComplianceScore = layerResults.reduce((sum, result) => sum + result.complianceScore, 0) / layerResults.length;

      return {
        overallValid,
        overallComplianceScore,
        layerResults,
        crossLayerIssues,
        recommendations
      };

    } catch (error) {
      this.logger.error('Cross-layer validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Perform LLM-based validation
   */
  private async performLLMValidation(
    validationType: string,
    primaryData: any,
    secondaryData: any,
    instruction: string
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[]; contractCompliant: boolean; violations: ContractViolation[] }> {
    const prompt = `You are an expert code validation specialist. ${instruction}

Primary Data:
${JSON.stringify(primaryData, null, 2)}

${secondaryData ? `Secondary Data:\n${JSON.stringify(secondaryData, null, 2)}` : ''}

Perform comprehensive validation and identify:

1. **Critical Errors**: Issues that prevent the code from working
2. **Contract Violations**: Mismatches between layers
3. **Type Mismatches**: Inconsistent data types
4. **Missing Implementations**: Required functionality not implemented
5. **Security Issues**: Security vulnerabilities or gaps
6. **Performance Issues**: Potential performance problems
7. **Best Practice Violations**: Code that doesn't follow best practices

Return your analysis in this exact JSON format:
{
  "contractCompliant": true|false,
  "errors": [
    {
      "type": "syntax|type|contract|security|runtime",
      "message": "Error description",
      "location": "file:line or component",
      "severity": "error|warning",
      "suggestion": "How to fix this issue"
    }
  ],
  "warnings": [
    {
      "type": "performance|maintainability|security|best-practice",
      "message": "Warning description",
      "location": "file:line or component",
      "suggestion": "Improvement suggestion"
    }
  ],
  "violations": [
    {
      "type": "missing-endpoint|type-mismatch|schema-mismatch|security-violation",
      "description": "Violation description",
      "expectedValue": "what was expected",
      "actualValue": "what was found",
      "severity": "critical|major|minor",
      "autoFixable": true|false
    }
  ]
}

Be thorough and identify all issues. Focus on correctness and production readiness.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    return this.parseJSONResponse(response, `${validationType} validation`);
  }

  /**
   * Validate database structure
   */
  private async validateDatabaseStructure(
    openApiSpec: OpenAPISpecification,
    databaseSchema: DatabaseSchema
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check that all API schemas have corresponding tables
    for (const [schemaName, schemaDefinition] of Object.entries(openApiSpec.components.schemas)) {
      const expectedTableName = this.convertSchemaNameToTableName(schemaName);
      const table = databaseSchema.tables.find(t => t.name === expectedTableName);

      if (!table) {
        errors.push({
          type: 'contract',
          message: `Missing database table for API schema: ${schemaName}`,
          location: `database.tables.${expectedTableName}`,
          severity: 'error',
          suggestion: `Create table ${expectedTableName} with columns matching ${schemaName} schema`
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate backend endpoints
   */
  private async validateBackendEndpoints(
    openApiSpec: OpenAPISpecification,
    backendArtifacts: GeneratedArtifact[]
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // This would be implemented with more sophisticated parsing
    // For now, return empty arrays
    return { errors, warnings };
  }

  /**
   * Validate backend models
   */
  private async validateBackendModels(
    databaseSchema: DatabaseSchema,
    backendArtifacts: GeneratedArtifact[]
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // This would be implemented with more sophisticated parsing
    // For now, return empty arrays
    return { errors, warnings };
  }

  /**
   * Validate API integration
   */
  private async validateAPIIntegration(
    openApiSpec: OpenAPISpecification,
    frontendArtifacts: GeneratedArtifact[],
    backendArtifacts: GeneratedArtifact[]
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // This would be implemented with more sophisticated parsing
    // For now, return empty arrays
    return { errors, warnings };
  }

  /**
   * Validate type consistency
   */
  private async validateTypeConsistency(
    frontendArtifacts: GeneratedArtifact[],
    backendArtifacts: GeneratedArtifact[]
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // This would be implemented with more sophisticated parsing
    // For now, return empty arrays
    return { errors, warnings };
  }

  /**
   * Identify cross-layer issues
   */
  private async identifyCrossLayerIssues(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact,
    backendArtifacts: GeneratedArtifact[],
    frontendArtifacts: GeneratedArtifact[],
    layerResults: LayerValidationResult[]
  ): Promise<CrossLayerIssue[]> {
    // This would analyze issues that span multiple layers
    return [];
  }

  /**
   * Generate validation recommendations
   */
  private async generateValidationRecommendations(
    layerResults: LayerValidationResult[],
    crossLayerIssues: CrossLayerIssue[]
  ): Promise<ValidationRecommendation[]> {
    // This would generate actionable recommendations
    return [];
  }

  /**
   * Calculate compliance score
   */
  private calculateComplianceScore(errors: ValidationError[], warnings: ValidationWarning[]): number {
    const errorWeight = 0.8;
    const warningWeight = 0.2;
    
    const errorPenalty = errors.length * errorWeight;
    const warningPenalty = warnings.length * warningWeight;
    
    const totalPenalty = errorPenalty + warningPenalty;
    
    return Math.max(0, 1 - (totalPenalty / 10));
  }

  /**
   * Convert schema name to table name
   */
  private convertSchemaNameToTableName(schemaName: string): string {
    return schemaName
      .replace(/([A-Z])/g, '_$1')
      .toLowerCase()
      .replace(/^_/, '') + 's';
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(response: string, context: string): any {
    try {
      let cleaned = response.trim();
      
      // Remove markdown code blocks
      cleaned = cleaned.replace(/^```json\s*/gm, '');
      cleaned = cleaned.replace(/^```\s*/gm, '');
      cleaned = cleaned.replace(/\s*```$/gm, '');
      
      // Extract JSON
      const jsonStart = cleaned.indexOf('{');
      const jsonEnd = cleaned.lastIndexOf('}');
      
      if (jsonStart !== -1 && jsonEnd !== -1) {
        cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
      }

      return JSON.parse(cleaned);
    } catch (error) {
      this.logger.error(`Failed to parse JSON response for ${context}`, {
        error: error instanceof Error ? error.message : String(error),
        responsePreview: response.substring(0, 200)
      });
      
      // Return default structure on parse failure
      return {
        contractCompliant: false,
        errors: [{
          type: 'runtime',
          message: `Failed to parse validation response: ${error instanceof Error ? error.message : String(error)}`,
          location: 'validation-parser',
          severity: 'error'
        }],
        warnings: [],
        violations: []
      };
    }
  }
}
