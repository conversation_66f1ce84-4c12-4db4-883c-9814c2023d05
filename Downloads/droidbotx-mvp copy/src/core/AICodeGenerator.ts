import { BaseAgent } from './BaseAgent';
import { BusinessDomainAnalysis, BusinessEntity } from './SemanticAnalyzer';
import { TemplateEngine, ProjectContext } from './TemplateEngine';
import { PostProcessingPipeline } from './PostProcessingPipeline';

export interface CodeGenerationRequest {
  type: 'frontend-component' | 'backend-route' | 'database-schema' | 'service-class' | 'model-class' | 'configuration';
  entity?: BusinessEntity;
  context: CodeGenerationContext;
  specifications: CodeSpecifications;
}

export interface CodeGenerationContext {
  projectName: string;
  businessDomain: BusinessDomainAnalysis;
  targetFramework: string;
  architecturalPatterns: string[];
  existingEntities: string[];
  relatedEntities: BusinessEntity[];
}

export interface CodeSpecifications {
  fileName: string;
  purpose: string;
  requirements: string[];
  dependencies: string[];
  businessRules: string[];
  validationRules: string[];
  securityRequirements: string[];
}

export interface GeneratedCode {
  fileName: string;
  content: string;
  dependencies: string[];
  imports: string[];
  exports: string[];
  tests?: string;
}

export class AICodeGenerator extends BaseAgent {
  private templateEngine: TemplateEngine;
  private postProcessingPipeline: PostProcessingPipeline;
  private currentProjectContext: ProjectContext | null = null;

  constructor() {
    super(
      'AICodeGenerator',
      'Generates domain-specific code using AI based on semantic analysis',
      `You are an expert software developer specializing in generating high-quality, domain-specific code.

Your role is to generate production-ready code that:
1. Accurately reflects the business domain and requirements
2. Follows best practices and architectural patterns
3. Includes proper error handling and validation
4. Implements security requirements
5. Maintains consistency with existing codebase

You generate code for various components:
- React components with TypeScript for frontend
- Express.js routes and controllers for backend
- PostgreSQL database schemas and migrations
- Service classes with business logic
- Data models with validation
- Configuration files

CRITICAL FORMATTING REQUIREMENTS:
- Return ONLY raw, executable code without markdown blocks
- DO NOT wrap code in \`\`\`typescript, \`\`\`javascript, or any \`\`\` markers
- Generate complete, functional code that can be used immediately
- Include ALL necessary imports at the top of the file using ACTUAL import statements
- Use proper TypeScript syntax throughout
- DO NOT use template placeholders like { Router } or { Pool } - use actual imports
- ALWAYS write real import statements like: import { Router } from 'express';

IMPORT REQUIREMENTS:
- Write actual import statements, NOT placeholders
- Example: import { Router, Request, Response } from 'express'; NOT { Router, Request, Response }
- Example: import { Pool } from 'pg'; NOT { Pool }
- Example: import jwt from 'jsonwebtoken'; NOT { jwt }

CODE STRUCTURE REQUIREMENTS:
- Generate ONLY ONE class/interface/function per file
- DO NOT create duplicate class definitions
- Use consistent variable names throughout
- Use string type for all ID fields for consistency
- Use consistent status enums (pending, in_progress, completed, cancelled)
- Add proper file extensions to import paths (.js for compiled TypeScript)

Always generate complete, functional code that can be used immediately without modification.`
    );
    this.templateEngine = TemplateEngine.getInstance();
    this.postProcessingPipeline = new PostProcessingPipeline();
  }

  /**
   * Set project context for template substitution
   */
  public setProjectContext(context: ProjectContext): void {
    this.currentProjectContext = context;
    this.logDebug('Project context set for template substitution', {
      projectName: context.projectName,
      domain: context.domain
    });
  }

  public canHandle(task: any): boolean {
    return task.type === 'ai_code_generation';
  }

  public async execute(task: any): Promise<any> {
    return { success: true, data: {} };
  }

  /**
   * Generate a React component based on business entity
   */
  public async generateReactComponent(request: CodeGenerationRequest): Promise<GeneratedCode> {
    const prompt = this.buildReactComponentPrompt(request);

    try {
      const codeResponse = await this.generateSingleLLMResponse(prompt, {
        temperature: 0.2,
        maxTokens: 4000
      });

      const generatedCode = await this.parseCodeResponse(codeResponse, request.specifications.fileName);

      this.logInfo('React component generated', {
        fileName: generatedCode.fileName,
        linesOfCode: generatedCode.content.split('\n').length
      });

      return generatedCode;
    } catch (error) {
      this.logError('Failed to generate React component', {
        error: error instanceof Error ? error.message : String(error),
        fileName: request.specifications.fileName
      });
      throw new Error(`React component generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate Express.js route based on business entity
   */
  public async generateExpressRoute(request: CodeGenerationRequest): Promise<GeneratedCode> {
    const prompt = this.buildExpressRoutePrompt(request);

    try {
      const codeResponse = await this.generateSingleLLMResponse(prompt, {
        temperature: 0.2,
        maxTokens: 4000
      });

      const generatedCode = await this.parseCodeResponse(codeResponse, request.specifications.fileName);

      this.logInfo('Express route generated', {
        fileName: generatedCode.fileName,
        linesOfCode: generatedCode.content.split('\n').length
      });

      return generatedCode;
    } catch (error) {
      this.logError('Failed to generate Express route', {
        error: error instanceof Error ? error.message : String(error),
        fileName: request.specifications.fileName
      });
      throw new Error(`Express route generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate database schema based on business entities
   */
  public async generateDatabaseSchema(entities: BusinessEntity[], context: CodeGenerationContext): Promise<GeneratedCode> {
    const prompt = this.buildDatabaseSchemaPrompt(entities, context);

    try {
      const codeResponse = await this.generateSingleLLMResponse(prompt, {
        temperature: 0.1,
        maxTokens: 6000
      });

      const generatedCode = await this.parseCodeResponse(codeResponse, 'init.sql');

      this.logInfo('Database schema generated', {
        entitiesCount: entities.length,
        linesOfCode: generatedCode.content.split('\n').length
      });

      return generatedCode;
    } catch (error) {
      this.logError('Failed to generate database schema', {
        error: error instanceof Error ? error.message : String(error),
        entitiesCount: entities.length
      });
      throw new Error(`Database schema generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate service class based on business entity
   */
  public async generateServiceClass(request: CodeGenerationRequest): Promise<GeneratedCode> {
    const prompt = this.buildServiceClassPrompt(request);

    try {
      const codeResponse = await this.generateSingleLLMResponse(prompt, {
        temperature: 0.2,
        maxTokens: 4000
      });

      const generatedCode = await this.parseCodeResponse(codeResponse, request.specifications.fileName);

      this.logInfo('Service class generated', {
        fileName: generatedCode.fileName,
        linesOfCode: generatedCode.content.split('\n').length
      });

      return generatedCode;
    } catch (error) {
      this.logError('Failed to generate service class', {
        error: error instanceof Error ? error.message : String(error),
        fileName: request.specifications.fileName
      });
      throw new Error(`Service class generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildReactComponentPrompt(request: CodeGenerationRequest): string {
    const { entity, context, specifications } = request;

    return `Generate a React TypeScript component for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

COMPONENT SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Fields: ${(entity.fields || []).map(f => `${f.name}: ${f.type}${f.required ? ' (required)' : ''}`).join(', ')}
- Operations: ${(entity.operations || []).map(op => {
      if (typeof op === 'string') return op;
      if (op && typeof op === 'object' && op.name) return op.name;
      return 'operation';
    }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Framework: React with TypeScript
- Styling: Modern CSS-in-JS or Tailwind CSS
- State Management: React hooks
- Form Handling: Controlled components with validation
- Error Handling: Comprehensive error boundaries
- Accessibility: WCAG 2.1 AA compliance

BUSINESS RULES:
${specifications.businessRules.map(rule => `- ${rule}`).join('\n')}

VALIDATION RULES:
${specifications.validationRules.map(rule => `- ${rule}`).join('\n')}

Generate a complete, production-ready React component that:
1. Implements all specified functionality
2. Includes proper TypeScript interfaces
3. Handles loading, error, and success states
4. Implements form validation if applicable
5. Follows React best practices
6. Includes proper accessibility attributes
7. Is specific to the ${context.businessDomain.domain} domain

CRITICAL IMPORT REQUIREMENTS:
- ALWAYS include ALL necessary React imports at the top
- Import React hooks explicitly: import { useState, useEffect, useCallback } from 'react'
- Import React if using JSX: import React from 'react'
- Include all TypeScript type imports
- Import any external libraries used

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

STRUCTURE REQUIREMENTS:
- Start with all import statements (React imports first)
- Define interfaces before the component
- Use functional components with TypeScript
- Include proper error handling
- Add loading states where appropriate
- Use string type for all ID fields
- Use consistent status values: 'pending', 'in_progress', 'completed', 'cancelled'

FORMATTING REQUIREMENTS:
- DO NOT wrap code in markdown blocks
- DO NOT include \`\`\`typescript or any backticks
- Return clean, executable TypeScript code only

Return ONLY the complete TypeScript code with all imports, no markdown formatting, no additional text or explanations.`;
  }

  private buildExpressRoutePrompt(request: CodeGenerationRequest): string {
    const { entity, context, specifications } = request;

    return `Generate an Express.js TypeScript route for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

ROUTE SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Fields: ${(entity.fields || []).map(f => `${f.name}: ${f.type}${f.required ? ' (required)' : ''}`).join(', ')}
- Operations: ${(entity.operations || []).map(op => {
      if (typeof op === 'string') return op;
      if (op && typeof op === 'object' && op.name && op.type) return `${op.name} (${op.type})`;
      return 'operation';
    }).join(', ')}
- Business Rules: ${(entity.operations || []).flatMap(op => {
      if (typeof op === 'string') return [];
      if (op && typeof op === 'object' && Array.isArray(op.businessRules)) return op.businessRules;
      return [];
    }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Framework: Express.js with TypeScript
- Database: PostgreSQL with connection pooling
- Authentication: JWT-based authentication
- Validation: Input validation with proper error messages
- Error Handling: Comprehensive error handling with logging
- Security: SQL injection prevention, rate limiting

BUSINESS RULES:
${specifications.businessRules.map(rule => `- ${rule}`).join('\n')}

SECURITY REQUIREMENTS:
${specifications.securityRequirements.map(req => `- ${req}`).join('\n')}

Generate a complete Express.js route file that:
1. Implements all CRUD operations for the entity
2. Includes proper TypeScript interfaces
3. Implements authentication and authorization
4. Includes comprehensive input validation
5. Handles all error cases with appropriate HTTP status codes
6. Includes proper logging for debugging
7. Follows Express.js best practices
8. Is specific to the ${context.businessDomain.domain} domain

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

Return ONLY the TypeScript code, no additional text or explanations.`;
  }

  private buildDatabaseSchemaPrompt(entities: BusinessEntity[], context: CodeGenerationContext): string {
    const domain = context.businessDomain?.domain || 'application';
    const projectName = context.projectName || 'project';

    return `Generate a PostgreSQL database schema for the ${domain} domain.

BUSINESS CONTEXT:
- Domain: ${domain}
- Project: ${projectName}

ENTITIES:
${entities.map(entity => `
- ${entity.name}: ${entity.description || 'No description'}
  Fields: ${(entity.fields || []).map(f => `${f.name} (${f.type}${f.required ? ', required' : ''})`).join(', ') || 'No fields defined'}
  Relationships: ${(entity.relationships || []).map(r => `${r.type} with ${r.target}`).join(', ') || 'No relationships defined'}
`).join('\n')}

TECHNICAL REQUIREMENTS:
- Database: PostgreSQL 14+
- Extensions: uuid-ossp, pgcrypto
- Indexing: Proper indexes for performance
- Constraints: Foreign keys, unique constraints, check constraints
- Security: Row-level security where applicable

Generate a complete SQL schema that:
1. Creates all necessary tables with proper data types
2. Implements all relationships with foreign keys
3. Includes proper indexes for performance
4. Adds check constraints for data validation
5. Includes sample data for testing
6. Follows PostgreSQL best practices
7. Is optimized for the ${context.businessDomain.domain} domain

Return ONLY the SQL code, no additional text or explanations.`;
  }

  private buildServiceClassPrompt(request: CodeGenerationRequest): string {
    const { entity, context, specifications } = request;

    return `Generate a TypeScript service class for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

SERVICE SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Operations: ${(entity.operations || []).map(op => {
      if (typeof op === 'string') return op;
      if (op && typeof op === 'object' && op.name && op.type) return `${op.name} (${op.type}): ${op.description || 'No description'}`;
      return 'operation';
    }).join('\n  ')}
- Business Rules: ${(entity.operations || []).flatMap(op => {
      if (typeof op === 'string') return [];
      if (op && typeof op === 'object' && Array.isArray(op.businessRules)) return op.businessRules;
      return [];
    }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Language: TypeScript
- Database: PostgreSQL with connection pooling
- Patterns: Repository pattern, dependency injection
- Error Handling: Custom business exceptions
- Logging: Comprehensive logging for debugging
- Validation: Business rule validation

Generate a complete service class that:
1. Implements all business operations for the entity
2. Includes proper TypeScript interfaces and types
3. Implements business rule validation
4. Handles database operations with proper error handling
5. Includes comprehensive logging
6. Follows SOLID principles
7. Is specific to the ${context.businessDomain.domain} domain

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

Return ONLY the TypeScript code, no additional text or explanations.`;
  }

  private async parseCodeResponse(response: string, fileName: string): Promise<GeneratedCode> {
    try {
      // Extract code from response (remove any markdown formatting)
      let code = response.trim();

      // ENHANCED: More robust markdown code block patterns
      const codeBlockPatterns = [
        // Language-specific patterns with proper capture groups (case insensitive)
        /```(?:typescript|ts)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:javascript|js)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:tsx|jsx)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:sql)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:yaml|yml)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:json)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:dockerfile)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:bash|sh)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:html)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:css)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:python|py)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:java)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:c\+\+|cpp|c)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:go)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:rust|rs)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:php)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:ruby|rb)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:swift)\s*\n?([\s\S]*?)\n?```/gi,
        /```(?:kotlin|kt)\s*\n?([\s\S]*?)\n?```/gi,
        // Generic patterns as fallback (more aggressive)
        /```\w*\s*\n?([\s\S]*?)\n?```/g,
        /```\s*\n?([\s\S]*?)\n?```/g,
        /`{3,}\w*\s*\n?([\s\S]*?)\n?`{3,}/g,
        // Handle cases where markdown blocks are malformed
        /```[\s\S]*?\n([\s\S]*?)(?:\n```|$)/g
      ];

      // FIXED: Proper regex matching logic with correct capture group handling
      let codeExtracted = false;
      for (const pattern of codeBlockPatterns) {
        const matches = [...code.matchAll(pattern)];
        if (matches.length > 0) {
          // Use the first match and prioritize captured content
          const match = matches[0];
          if (match[1] && match[1].trim()) {
            // Use captured group content (inside markdown blocks)
            code = match[1].trim();
            codeExtracted = true;
            this.logDebug(`Extracted code using pattern: ${pattern.source}`, { fileName });
            break;
          } else if (match[0]) {
            // Fallback: manually strip markdown from full match
            code = match[0]
              .replace(/^```\w*\n?/, '')
              .replace(/\n?```$/, '')
              .trim();
            codeExtracted = true;
            this.logDebug(`Extracted code using fallback for pattern: ${pattern.source}`, { fileName });
            break;
          }
        }
      }

      if (!codeExtracted) {
        this.logDebug('No markdown blocks detected, using raw response', { fileName });
      }

      // Apply comprehensive post-processing pipeline
      if (this.currentProjectContext) {
        const postProcessingResult = await this.postProcessingPipeline.processGeneratedCode(
          code,
          fileName,
          this.currentProjectContext
        );

        code = postProcessingResult.processedCode;

        if (!postProcessingResult.success) {
          this.logWarn('Post-processing issues detected', {
            fileName,
            issues: postProcessingResult.issues,
            warnings: postProcessingResult.warnings
          });
        } else {
          this.logDebug('Post-processing completed successfully', {
            fileName,
            fixesApplied: postProcessingResult.appliedFixes
          });
        }
      } else {
        // Fallback to basic processing if no context available
        code = this.cleanCodeArtifacts(code);
        code = this.substituteTemplateVariables(code, fileName);

        const validationResult = this.validateExtractedCode(code, fileName);
        if (!validationResult.isValid) {
          this.logWarn('Code validation issues detected', {
            fileName,
            issues: validationResult.issues
          });
        }
      }

      // Extract imports and exports
      const imports = this.extractImports(code);
      const exports = this.extractExports(code);
      const dependencies = this.extractDependencies(code);

      return {
        fileName,
        content: code,
        dependencies,
        imports,
        exports
      };
    } catch (error) {
      this.logError('Failed to parse code response', {
        error: error instanceof Error ? error.message : String(error),
        fileName,
        responseLength: response.length
      });
      throw new Error(`Failed to parse generated code: ${error instanceof Error ? error.message : 'Invalid response'}`);
    }
  }

  private extractImports(code: string): string[] {
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
    const imports: string[] = [];
    let match;

    while ((match = importRegex.exec(code)) !== null) {
      imports.push(match[1]);
    }

    return imports;
  }

  private extractExports(code: string): string[] {
    const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var|interface|type)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
    const exports: string[] = [];
    let match;

    while ((match = exportRegex.exec(code)) !== null) {
      exports.push(match[1]);
    }

    return exports;
  }

  private extractDependencies(code: string): string[] {
    const dependencies: string[] = [];

    // Extract npm package dependencies from imports
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"./][^'"]*)['"]/g;
    let match;

    while ((match = importRegex.exec(code)) !== null) {
      const packageName = match[1].split('/')[0];
      if (!dependencies.includes(packageName)) {
        dependencies.push(packageName);
      }
    }

    return dependencies;
  }

  /**
   * Clean common AI response artifacts from generated code
   */
  private cleanCodeArtifacts(code: string): string {
    let cleaned = code;

    // Remove multi-line markdown code blocks (enhanced patterns)
    const markdownPatterns = [
      /^```(?:typescript|ts|javascript|js|tsx|jsx|sql|yaml|yml|json|dockerfile|bash|sh|html|css)\s*\n/gm,
      /^```\s*\n/gm,
      /\n```\s*$/gm,
      /^```\s*$/gm
    ];

    for (const pattern of markdownPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }

    // Remove common AI response prefixes/suffixes
    const artifactPatterns = [
      /^Here's the.*?:\s*/i,
      /^Here is the.*?:\s*/i,
      /^This is the.*?:\s*/i,
      /^The following.*?:\s*/i,
      /^Below is the.*?:\s*/i,
      /^I'll create.*?:\s*/i,
      /^Let me create.*?:\s*/i,
      /^\s*```\w*\s*/,
      /\s*```\s*$/,
      /^typescript\s*/i,
      /^javascript\s*/i,
      /^react\s*/i,
      /^tsx\s*/i,
      /^jsx\s*/i,
      /^yaml\s*/i,
      /^sql\s*/i,
      /^json\s*/i
    ];

    for (const pattern of artifactPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }

    // Remove standalone artifacts and excessive whitespace
    cleaned = cleaned.replace(/^\s*x\s*$/gm, '');
    cleaned = cleaned.replace(/\n{3,}/g, '\n\n');
    cleaned = cleaned.split('\n').map(line => line.trimEnd()).join('\n');
    cleaned = cleaned.trim();

    return cleaned;
  }

  /**
   * Substitute template variables with actual project context using TemplateEngine
   */
  private substituteTemplateVariables(code: string, fileName: string): string {
    const projectContext = this.currentProjectContext || this.getProjectContext();

    if (!projectContext) {
      this.logWarn('No project context available for template substitution', { fileName });
      return code;
    }

    // Use the centralized template engine for substitution
    const substituted = this.templateEngine.substituteVariables(code, projectContext);

    // Validate that substitution was successful
    const validation = this.templateEngine.validateSubstitution(substituted);
    if (!validation.isValid) {
      this.logWarn('Template substitution incomplete', {
        fileName,
        unsubstituted: validation.unsubstituted
      });
    }

    this.logDebug('Applied template variable substitutions using TemplateEngine', {
      fileName,
      projectName: projectContext.projectName,
      validationPassed: validation.isValid
    });

    return substituted;
  }

  /**
   * Validate extracted code for common issues
   */
  private validateExtractedCode(code: string, fileName: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check for remaining markdown artifacts
    if (code.includes('```')) {
      issues.push('Markdown artifacts still present in code');
    }

    // Check for template variables that weren't substituted
    const unsubstitutedVariables = [
      /undefined Application/g,
      /undefined management/g,
      /\{[^}]+\}/g
    ];

    for (const pattern of unsubstitutedVariables) {
      if (pattern.test(code)) {
        issues.push(`Unsubstituted template variables found: ${pattern.source}`);
      }
    }

    // Check for incomplete code blocks
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
      const openBraces = (code.match(/\{/g) || []).length;
      const closeBraces = (code.match(/\}/g) || []).length;
      if (openBraces !== closeBraces) {
        issues.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
      }
    }

    // Check for incomplete function calls
    if (/\(\s*;/.test(code)) {
      issues.push('Incomplete function calls detected');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get current project context for template substitution
   */
  private getProjectContext(): any {
    // This will be enhanced to get actual project context
    // For now, return a basic context structure
    return {
      projectName: 'E-commerce Platform', // This should come from actual context
      description: 'e-commerce management solution',
      domain: 'e-commerce',
      dbName: 'ecommerce_db',
      apiPrefix: '/api/v1'
    };
  }

  /**
   * Extract component name from file name
   */
  private extractComponentName(fileName: string): string {
    const baseName = fileName.replace(/\.(ts|tsx|js|jsx)$/, '');
    return baseName.charAt(0).toUpperCase() + baseName.slice(1);
  }

  /**
   * Extract service name from file name
   */
  private extractServiceName(fileName: string): string {
    const baseName = fileName.replace(/\.(ts|js)$/, '').replace(/Service$/, '');
    return baseName.charAt(0).toUpperCase() + baseName.slice(1) + 'Service';
  }
}
