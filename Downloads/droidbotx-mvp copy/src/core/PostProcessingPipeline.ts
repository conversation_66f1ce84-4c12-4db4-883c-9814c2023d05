import { Logger } from './Logger';
import { TemplateEngine, ProjectContext } from './TemplateEngine';

export interface PostProcessingResult {
  success: boolean;
  processedCode: string;
  issues: string[];
  warnings: string[];
  appliedFixes: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number;
}

/**
 * Comprehensive post-processing pipeline for generated code
 */
export class PostProcessingPipeline {
  private logger: Logger;
  private templateEngine: TemplateEngine;

  constructor() {
    this.logger = Logger.getInstance();
    this.templateEngine = TemplateEngine.getInstance();
  }

  /**
   * Process generated code through comprehensive pipeline
   */
  public async processGeneratedCode(
    code: string,
    fileName: string,
    context: ProjectContext
  ): Promise<PostProcessingResult> {
    const result: PostProcessingResult = {
      success: true,
      processedCode: code,
      issues: [],
      warnings: [],
      appliedFixes: []
    };

    try {
      this.logger.debug('Starting post-processing pipeline', { fileName });

      // Phase 1: Remove markdown artifacts
      result.processedCode = this.removeMarkdownArtifacts(result.processedCode);
      result.appliedFixes.push('Markdown artifact removal');

      // Phase 2: Template variable substitution
      result.processedCode = this.templateEngine.substituteVariables(result.processedCode, context);
      result.appliedFixes.push('Template variable substitution');

      // Phase 3: Fix React imports (for React components)
      if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
        const reactResult = this.fixReactImports(result.processedCode);
        result.processedCode = reactResult.code;
        result.appliedFixes.push(...reactResult.fixes);
      }

      // Phase 4: Convert template placeholders to actual imports
      const importResult = this.convertTemplatePlaceholdersToImports(result.processedCode, fileName);
      result.processedCode = importResult.code;
      result.appliedFixes.push(...importResult.fixes);

      // Phase 5: Fix common syntax issues
      const syntaxResult = this.fixCommonSyntaxIssues(result.processedCode, fileName);
      result.processedCode = syntaxResult.code;
      result.appliedFixes.push(...syntaxResult.fixes);

      // Phase 4: Validate and repair
      const validationResult = await this.validateAndRepair(result.processedCode, fileName);
      result.processedCode = validationResult.code;
      result.issues.push(...validationResult.issues);
      result.warnings.push(...validationResult.warnings);
      result.appliedFixes.push(...validationResult.fixes);

      // Phase 5: Final validation
      const finalValidation = await this.performFinalValidation(result.processedCode, fileName);
      result.issues.push(...finalValidation.errors);
      result.warnings.push(...finalValidation.warnings);

      result.success = result.issues.length === 0;

      this.logger.debug('Post-processing pipeline completed', {
        fileName,
        success: result.success,
        issuesCount: result.issues.length,
        warningsCount: result.warnings.length,
        fixesApplied: result.appliedFixes.length
      });

    } catch (error) {
      result.success = false;
      result.issues.push(`Post-processing failed: ${error instanceof Error ? error.message : String(error)}`);
      this.logger.error('Post-processing pipeline failed', { fileName, error });
    }

    return result;
  }

  /**
   * Remove markdown artifacts with comprehensive patterns
   */
  private removeMarkdownArtifacts(code: string): string {
    let cleaned = code;

    // ENHANCED: More comprehensive markdown removal patterns
    const markdownPatterns = [
      // Remove opening markdown blocks (more aggressive)
      /^```[a-zA-Z]*\s*\n?/gm,
      /```[a-zA-Z]*\s*\n?/g,
      // Remove closing markdown blocks
      /\n?```\s*$/gm,
      /\n?```/g,
      // Remove any remaining backticks (3 or more)
      /`{3,}/g,
      // Remove markdown blocks that span multiple lines
      /```[\s\S]*?```/g,
      // Remove specific language markers
      /```(?:typescript|ts|javascript|js|tsx|jsx|sql|yaml|yml|json|dockerfile|bash|sh|html|css|python|java|c\+\+|c|go|rust|php|ruby|swift|kotlin)\s*\n?/gi,
      // Remove generic code blocks
      /```\w*\s*\n?/g,
      /```\s*$/gm,
      /^```\s*$/gm
    ];

    // Apply patterns multiple times to catch nested cases
    for (let i = 0; i < 3; i++) {
      for (const pattern of markdownPatterns) {
        const before = cleaned;
        cleaned = cleaned.replace(pattern, '');
        if (before !== cleaned) {
          this.logger.debug(`Markdown removal iteration ${i + 1}: Applied pattern ${pattern.source}`);
        }
      }
    }

    // Remove common AI response artifacts
    const artifactPatterns = [
      /^Here's the.*?:\s*/i,
      /^Here is the.*?:\s*/i,
      /^I'll create.*?:\s*/i,
      /^Let me create.*?:\s*/i,
      /^This is the.*?:\s*/i,
      /^The following.*?:\s*/i,
      /^Below is the.*?:\s*/i,
      /^This code.*?:\s*/i,
      /^The code.*?:\s*/i
    ];

    for (const pattern of artifactPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }

    // Remove leading/trailing whitespace and empty lines
    cleaned = cleaned.trim();

    // Remove excessive empty lines (more than 2 consecutive)
    cleaned = cleaned.replace(/\n{3,}/g, '\n\n');

    return cleaned;
  }

  /**
   * Fix React imports by detecting used hooks and components
   */
  private fixReactImports(code: string): { code: string; fixes: string[] } {
    const fixes: string[] = [];
    let fixed = code;

    // Detect React hooks and features used in the code
    const reactFeatures: Record<string, boolean> = {
      useState: /useState\s*[<(]/g.test(code),
      useEffect: /useEffect\s*\(/g.test(code),
      useContext: /useContext\s*\(/g.test(code),
      useReducer: /useReducer\s*\(/g.test(code),
      useCallback: /useCallback\s*\(/g.test(code),
      useMemo: /useMemo\s*\(/g.test(code),
      useRef: /useRef\s*[<(]/g.test(code),
      useImperativeHandle: /useImperativeHandle\s*\(/g.test(code),
      useLayoutEffect: /useLayoutEffect\s*\(/g.test(code),
      useDebugValue: /useDebugValue\s*\(/g.test(code),
      Component: /extends\s+Component/g.test(code),
      PureComponent: /extends\s+PureComponent/g.test(code),
      Fragment: /<Fragment|<React\.Fragment/g.test(code),
      createContext: /createContext\s*\(/g.test(code),
      forwardRef: /forwardRef\s*\(/g.test(code),
      memo: /memo\s*\(/g.test(code)
    };

    // Check current imports
    const importMatch = fixed.match(/^import\s+.*?from\s+['"]react['"];?\s*$/m);
    const hasReactImport = !!importMatch;

    if (hasReactImport) {
      const currentImport = importMatch[0];
      const usedFeatures = Object.keys(reactFeatures).filter(feature => reactFeatures[feature]);

      if (usedFeatures.length > 0) {
        // Extract current imports
        const defaultImportMatch = currentImport.match(/import\s+(\w+)/);
        const namedImportsMatch = currentImport.match(/import\s+(?:\w+,\s*)?\{\s*([^}]+)\s*\}/);

        const hasDefaultImport = defaultImportMatch && defaultImportMatch[1] === 'React';
        const currentNamedImports = namedImportsMatch ?
          namedImportsMatch[1].split(',').map(imp => imp.trim()) : [];

        // Determine what needs to be imported
        const neededImports = usedFeatures.filter(feature =>
          !currentNamedImports.includes(feature)
        );

        if (neededImports.length > 0) {
          const allNamedImports = [...new Set([...currentNamedImports, ...neededImports])];
          const newImport = hasDefaultImport ?
            `import React, { ${allNamedImports.join(', ')} } from 'react';` :
            `import { ${allNamedImports.join(', ')} } from 'react';`;

          fixed = fixed.replace(currentImport, newImport);
          fixes.push(`Added missing React imports: ${neededImports.join(', ')}`);
        }
      }
    } else {
      // No React import exists, add one if React features are used
      const usedFeatures = Object.keys(reactFeatures).filter(feature => reactFeatures[feature]);
      const needsReactDefault = /React\./g.test(code) || /<[A-Z]/g.test(code);

      if (usedFeatures.length > 0 || needsReactDefault) {
        const newImport = needsReactDefault && usedFeatures.length > 0 ?
          `import React, { ${usedFeatures.join(', ')} } from 'react';\n` :
          needsReactDefault ?
          `import React from 'react';\n` :
          `import { ${usedFeatures.join(', ')} } from 'react';\n`;

        // Insert import at the top, after any existing imports
        const lines = fixed.split('\n');
        let insertIndex = 0;

        // Find the last import statement
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].trim().startsWith('import ') || lines[i].trim().startsWith('//') || lines[i].trim() === '') {
            insertIndex = i + 1;
          } else {
            break;
          }
        }

        lines.splice(insertIndex, 0, newImport.trim());
        fixed = lines.join('\n');
        fixes.push(`Added React import with: ${usedFeatures.join(', ')}`);
      }
    }

    return { code: fixed, fixes };
  }

  /**
   * Convert template placeholders to actual imports
   */
  private convertTemplatePlaceholdersToImports(code: string, fileName: string): { code: string; fixes: string[] } {
    const fixes: string[] = [];
    let fixed = code;

    // Define common template placeholder to import mappings
    const importMappings = [
      // Express imports
      {
        placeholder: /\{\s*Router,\s*Request,\s*Response,?\s*NextFunction\s*\}/g,
        replacement: "import { Router, Request, Response, NextFunction } from 'express';",
        description: 'Convert Express imports placeholder'
      },
      {
        placeholder: /\{\s*Router,\s*Request,\s*Response\s*\}/g,
        replacement: "import { Router, Request, Response } from 'express';",
        description: 'Convert Express Router imports placeholder'
      },
      {
        placeholder: /\{\s*Request,\s*Response\s*\}/g,
        replacement: "import { Request, Response } from 'express';",
        description: 'Convert Express Request/Response imports placeholder'
      },

      // Database imports
      {
        placeholder: /\{\s*Pool,?\s*QueryResult\s*\}/g,
        replacement: "import { Pool, QueryResult } from 'pg';",
        description: 'Convert PostgreSQL imports placeholder'
      },
      {
        placeholder: /\{\s*Pool\s*\}/g,
        replacement: "import { Pool } from 'pg';",
        description: 'Convert PostgreSQL Pool import placeholder'
      },

      // JWT imports
      {
        placeholder: /\{\s*jwt\s*\}/g,
        replacement: "import jwt from 'jsonwebtoken';",
        description: 'Convert JWT import placeholder'
      },

      // Validation imports
      {
        placeholder: /\{\s*body,\s*validationResult\s*\}/g,
        replacement: "import { body, validationResult } from 'express-validator';",
        description: 'Convert express-validator imports placeholder'
      },

      // Logger imports
      {
        placeholder: /\{\s*Logger\s*\}/g,
        replacement: "import { Logger } from '../utils/Logger.js';",
        description: 'Convert Logger import placeholder'
      },
      {
        placeholder: /\{\s*createLogger,\s*Logger\s*\}/g,
        replacement: "import { createLogger, Logger } from 'winston';",
        description: 'Convert Winston Logger imports placeholder'
      },

      // Error imports
      {
        placeholder: /\{\s*DatabaseError\s*\}/g,
        replacement: "import { DatabaseError } from '../errors/DatabaseError.js';",
        description: 'Convert DatabaseError import placeholder'
      },
      {
        placeholder: /\{\s*ValidationError\s*\}/g,
        replacement: "import { ValidationError } from '../errors/ValidationError.js';",
        description: 'Convert ValidationError import placeholder'
      },
      {
        placeholder: /\{\s*UserNotFoundError\s*\}/g,
        replacement: "import { UserNotFoundError } from '../errors/UserNotFoundError.js';",
        description: 'Convert UserNotFoundError import placeholder'
      },
      {
        placeholder: /\{\s*InvalidCredentialsError\s*\}/g,
        replacement: "import { InvalidCredentialsError } from '../errors/InvalidCredentialsError.js';",
        description: 'Convert InvalidCredentialsError import placeholder'
      },

      // Model imports (dynamic based on filename)
      {
        placeholder: /\{\s*Product\s*\}/g,
        replacement: "import { Product } from '../models/Product.js';",
        description: 'Convert Product model import placeholder'
      },
      {
        placeholder: /\{\s*User\s*\}/g,
        replacement: "import { User } from '../models/User.js';",
        description: 'Convert User model import placeholder'
      },
      {
        placeholder: /\{\s*Category\s*\}/g,
        replacement: "import { Category } from '../models/Category.js';",
        description: 'Convert Category model import placeholder'
      },

      // Repository imports
      {
        placeholder: /\{\s*ProductRepository\s*\}/g,
        replacement: "import { ProductRepository } from '../repositories/ProductRepository.js';",
        description: 'Convert ProductRepository import placeholder'
      },
      {
        placeholder: /\{\s*UserRepository\s*\}/g,
        replacement: "import { UserRepository } from '../repositories/UserRepository.js';",
        description: 'Convert UserRepository import placeholder'
      },
      {
        placeholder: /\{\s*CategoryRepository\s*\}/g,
        replacement: "import { CategoryRepository } from '../repositories/CategoryRepository.js';",
        description: 'Convert CategoryRepository import placeholder'
      },

      // Utility imports
      {
        placeholder: /\{\s*hashPassword,\s*comparePasswords\s*\}/g,
        replacement: "import { hashPassword, comparePasswords } from '../utils/auth.js';",
        description: 'Convert auth utilities import placeholder'
      }
    ];

    // Apply import mappings
    for (const mapping of importMappings) {
      const originalCode = fixed;
      fixed = fixed.replace(mapping.placeholder, mapping.replacement);

      if (originalCode !== fixed) {
        fixes.push(mapping.description);
        this.logger.debug(`Applied import conversion: ${mapping.description}`);
      }
    }

    // Fix object literal placeholders that should be actual code
    const objectLiteralFixes = [
      // Fix incomplete object literals and function calls
      {
        pattern: /\{\s*connectionString:\s*process\.env\.DATABASE_URL,?\s*\}/g,
        replacement: `{
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
}`,
        description: 'Fix database connection config'
      },
      {
        pattern: /\{\s*windowMs:\s*15\s*\*\s*60\s*\*\s*1000[^}]*\}/g,
        replacement: `{
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}`,
        description: 'Fix rate limiting config'
      }
    ];

    for (const fix of objectLiteralFixes) {
      const originalCode = fixed;
      fixed = fixed.replace(fix.pattern, fix.replacement);

      if (originalCode !== fixed) {
        fixes.push(fix.description);
        this.logger.debug(`Applied object literal fix: ${fix.description}`);
      }
    }

    return { code: fixed, fixes };
  }

  /**
   * Fix common syntax issues
   */
  private fixCommonSyntaxIssues(code: string, fileName: string): { code: string; fixes: string[] } {
    let fixed = code;
    const fixes: string[] = [];

    // Fix broken SQL template literals
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
      const sqlFixes = this.fixSQLTemplateLiterals(fixed);
      fixed = sqlFixes.code;
      fixes.push(...sqlFixes.fixes);

      // Fix incomplete function calls
      const functionFixes = this.fixIncompleteFunctionCalls(fixed);
      fixed = functionFixes.code;
      fixes.push(...functionFixes.fixes);

      // Fix unterminated template literals
      const templateFixes = this.fixUnterminatedTemplateLiterals(fixed);
      fixed = templateFixes.code;
      fixes.push(...templateFixes.fixes);
    }

    // Fix excessive whitespace
    fixed = fixed.replace(/\n{3,}/g, '\n\n');
    fixed = fixed.split('\n').map(line => line.trimEnd()).join('\n');

    return { code: fixed, fixes };
  }

  /**
   * Fix broken SQL template literals
   */
  private fixSQLTemplateLiterals(code: string): { code: string; fixes: string[] } {
    const fixes: string[] = [];
    let fixed = code;

    // Pattern: const query = `;` followed by SQL
    const brokenSQLPattern = /const\s+(\w+)\s*=\s*`;?\s*\n(\s*(?:INSERT|SELECT|UPDATE|DELETE|CREATE|DROP|ALTER)[\s\S]*?)(?=\n\s*const|\n\s*\}|\n\s*return|\n\s*\/\/|$)/g;

    fixed = fixed.replace(brokenSQLPattern, (match, varName, sqlContent) => {
      fixes.push(`Fixed broken SQL template literal for ${varName}`);
      return `const ${varName} = \`\n${sqlContent.trim()}\n\`;`;
    });

    return { code: fixed, fixes };
  }

  /**
   * Fix incomplete function calls
   */
  private fixIncompleteFunctionCalls(code: string): { code: string; fixes: string[] } {
    const fixes: string[] = [];
    let fixed = code;

    // Pattern: function(; or method(;
    const incompleteCallPattern = /(\w+)\(\s*;/g;
    fixed = fixed.replace(incompleteCallPattern, (match, functionName) => {
      fixes.push(`Fixed incomplete function call: ${functionName}`);
      return `${functionName}()`;
    });

    return { code: fixed, fixes };
  }

  /**
   * Fix unterminated template literals
   */
  private fixUnterminatedTemplateLiterals(code: string): { code: string; fixes: string[] } {
    const fixes: string[] = [];
    let fixed = code;

    // Count backticks and fix if odd number
    const backtickCount = (fixed.match(/`/g) || []).length;
    if (backtickCount % 2 !== 0) {
      // Find the last incomplete template literal and close it
      const lines = fixed.split('\n');
      for (let i = lines.length - 1; i >= 0; i--) {
        if (lines[i].includes('`') && !lines[i].match(/`.*`/)) {
          lines[i] += '`';
          fixes.push('Fixed unterminated template literal');
          break;
        }
      }
      fixed = lines.join('\n');
    }

    return { code: fixed, fixes };
  }

  /**
   * Validate and repair code
   */
  private async validateAndRepair(code: string, fileName: string): Promise<{ code: string; issues: string[]; warnings: string[]; fixes: string[] }> {
    const result = {
      code,
      issues: [] as string[],
      warnings: [] as string[],
      fixes: [] as string[]
    };

    // Validate template substitution
    const templateValidation = this.templateEngine.validateSubstitution(code);
    if (!templateValidation.isValid) {
      result.warnings.push(`Unsubstituted template variables: ${templateValidation.unsubstituted.join(', ')}`);
    }

    // Validate syntax for TypeScript files
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
      const syntaxValidation = await this.validateTypeScriptSyntax(code);
      result.issues.push(...syntaxValidation.errors);
      result.warnings.push(...syntaxValidation.warnings);
    }

    return result;
  }

  /**
   * Validate TypeScript syntax
   */
  private async validateTypeScriptSyntax(code: string): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const ts = require('typescript');
      const result = ts.transpileModule(code, {
        compilerOptions: {
          target: ts.ScriptTarget.ES2020,
          module: ts.ModuleKind.CommonJS,
          strict: false, // Less strict for generated code
          skipLibCheck: true
        },
        reportDiagnostics: true
      });

      if (result.diagnostics && result.diagnostics.length > 0) {
        for (const diagnostic of result.diagnostics) {
          const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
          if (diagnostic.category === ts.DiagnosticCategory.Error) {
            errors.push(message);
          } else {
            warnings.push(message);
          }
        }
      }
    } catch (error) {
      // TypeScript not available, skip validation
      warnings.push('TypeScript validation skipped (compiler not available)');
    }

    return { errors, warnings };
  }

  /**
   * Perform final validation
   */
  private async performFinalValidation(code: string, fileName: string): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for remaining issues
    if (code.includes('```')) {
      errors.push('Markdown artifacts still present');
    }

    if (code.includes('undefined Application')) {
      errors.push('Unsubstituted template variables found');
    }

    // Basic syntax checks
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
      const openBraces = (code.match(/\{/g) || []).length;
      const closeBraces = (code.match(/\}/g) || []).length;
      if (openBraces !== closeBraces) {
        errors.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
      }
    }

    const score = Math.max(0, 100 - (errors.length * 20) - (warnings.length * 5));

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score
    };
  }
}

export default PostProcessingPipeline;
