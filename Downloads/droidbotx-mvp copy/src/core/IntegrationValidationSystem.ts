/**
 * Integration and Runtime Validation System
 * 
 * Implements compilation checks, runtime validation, and integration testing
 * for generated applications to ensure production readiness.
 */

import { Logger } from './Logger';
import { LLMProviderSystem } from './LLMProviderSystem';
import { 
  ApplicationRequirements, 
  GeneratedArtifact, 
  ValidationStatus,
  ValidationError,
  ValidationWarning
} from './ContractFirstLLMGenerator';
import * as fs from 'fs';
import * as path from 'path';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface CompilationResult {
  success: boolean;
  errors: CompilationError[];
  warnings: CompilationWarning[];
  outputPath?: string;
}

export interface CompilationError {
  file: string;
  line: number;
  column: number;
  message: string;
  code: string;
}

export interface CompilationWarning {
  file: string;
  line: number;
  column: number;
  message: string;
  code: string;
}

export interface RuntimeValidationResult {
  success: boolean;
  databaseConnection: boolean;
  apiEndpoints: EndpointValidationResult[];
  dependencies: DependencyValidationResult[];
  security: SecurityValidationResult;
  performance: PerformanceValidationResult;
}

export interface EndpointValidationResult {
  endpoint: string;
  method: string;
  status: 'success' | 'error' | 'timeout';
  responseTime: number;
  statusCode?: number;
  error?: string;
}

export interface DependencyValidationResult {
  name: string;
  version: string;
  status: 'installed' | 'missing' | 'version-mismatch';
  requiredVersion?: string;
}

export interface SecurityValidationResult {
  vulnerabilities: SecurityVulnerability[];
  score: number;
  recommendations: string[];
}

export interface SecurityVulnerability {
  severity: 'low' | 'medium' | 'high' | 'critical';
  package: string;
  description: string;
  recommendation: string;
}

export interface PerformanceValidationResult {
  bundleSize: number;
  loadTime: number;
  memoryUsage: number;
  recommendations: string[];
}

export interface IntegrationTestResult {
  success: boolean;
  testResults: TestCaseResult[];
  coverage: number;
  duration: number;
}

export interface TestCaseResult {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
}

export class IntegrationValidationSystem {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Perform comprehensive validation of generated application
   */
  public async validateGeneratedApplication(
    artifacts: GeneratedArtifact[],
    requirements: ApplicationRequirements,
    projectPath: string
  ): Promise<{
    compilation: CompilationResult;
    runtime: RuntimeValidationResult;
    integration: IntegrationTestResult;
    overallValid: boolean;
  }> {
    this.logger.info('Starting comprehensive application validation', {
      projectPath,
      artifactCount: artifacts.length
    });

    try {
      // Step 1: Write all artifacts to disk
      await this.writeArtifactsToDisk(artifacts, projectPath);

      // Step 2: Install dependencies
      await this.installDependencies(projectPath, requirements);

      // Step 3: Perform compilation validation
      const compilation = await this.performCompilationValidation(projectPath, requirements);

      // Step 4: Perform runtime validation
      const runtime = await this.performRuntimeValidation(projectPath, requirements);

      // Step 5: Perform integration testing
      const integration = await this.performIntegrationTesting(projectPath, requirements);

      const overallValid = compilation.success && runtime.success && integration.success;

      this.logger.info('Application validation completed', {
        projectPath,
        overallValid,
        compilationSuccess: compilation.success,
        runtimeSuccess: runtime.success,
        integrationSuccess: integration.success
      });

      return {
        compilation,
        runtime,
        integration,
        overallValid
      };

    } catch (error) {
      this.logger.error('Application validation failed', {
        projectPath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Write all artifacts to disk
   */
  private async writeArtifactsToDisk(artifacts: GeneratedArtifact[], projectPath: string): Promise<void> {
    this.logger.info('Writing artifacts to disk', {
      projectPath,
      artifactCount: artifacts.length
    });

    for (const artifact of artifacts) {
      const fullPath = path.join(projectPath, artifact.filePath);
      const dir = path.dirname(fullPath);

      // Create directory if it doesn't exist
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write file content
      fs.writeFileSync(fullPath, artifact.content, 'utf8');

      this.logger.debug('Artifact written to disk', {
        filePath: artifact.filePath,
        size: artifact.content.length
      });
    }
  }

  /**
   * Install dependencies
   */
  private async installDependencies(projectPath: string, requirements: ApplicationRequirements): Promise<void> {
    this.logger.info('Installing dependencies', { projectPath });

    try {
      // Install backend dependencies
      const backendPath = path.join(projectPath, 'backend');
      if (fs.existsSync(path.join(backendPath, 'package.json'))) {
        await execAsync('npm install', { cwd: backendPath });
        this.logger.info('Backend dependencies installed');
      }

      // Install frontend dependencies
      const frontendPath = path.join(projectPath, 'frontend');
      if (fs.existsSync(path.join(frontendPath, 'package.json'))) {
        await execAsync('npm install', { cwd: frontendPath });
        this.logger.info('Frontend dependencies installed');
      }

    } catch (error) {
      this.logger.error('Dependency installation failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Perform compilation validation
   */
  private async performCompilationValidation(
    projectPath: string, 
    requirements: ApplicationRequirements
  ): Promise<CompilationResult> {
    this.logger.info('Performing compilation validation', { projectPath });

    try {
      const errors: CompilationError[] = [];
      const warnings: CompilationWarning[] = [];

      // Compile backend
      const backendResult = await this.compileTypeScript(path.join(projectPath, 'backend'));
      errors.push(...backendResult.errors);
      warnings.push(...backendResult.warnings);

      // Compile frontend
      const frontendResult = await this.compileTypeScript(path.join(projectPath, 'frontend'));
      errors.push(...frontendResult.errors);
      warnings.push(...frontendResult.warnings);

      const success = errors.length === 0;

      this.logger.info('Compilation validation completed', {
        success,
        errorCount: errors.length,
        warningCount: warnings.length
      });

      return {
        success,
        errors,
        warnings,
        outputPath: success ? path.join(projectPath, 'dist') : undefined
      };

    } catch (error) {
      this.logger.error('Compilation validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        errors: [{
          file: 'compilation',
          line: 0,
          column: 0,
          message: error instanceof Error ? error.message : String(error),
          code: 'COMPILATION_ERROR'
        }],
        warnings: []
      };
    }
  }

  /**
   * Compile TypeScript code
   */
  private async compileTypeScript(projectPath: string): Promise<{ errors: CompilationError[]; warnings: CompilationWarning[] }> {
    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];

    if (!fs.existsSync(path.join(projectPath, 'tsconfig.json'))) {
      return { errors, warnings };
    }

    try {
      const { stdout, stderr } = await execAsync('npx tsc --noEmit', { cwd: projectPath });

      // Parse TypeScript compiler output
      if (stderr) {
        const lines = stderr.split('\n');
        for (const line of lines) {
          if (line.includes('error TS')) {
            const match = line.match(/(.+)\((\d+),(\d+)\): error TS(\d+): (.+)/);
            if (match) {
              errors.push({
                file: match[1],
                line: parseInt(match[2]),
                column: parseInt(match[3]),
                message: match[5],
                code: `TS${match[4]}`
              });
            }
          } else if (line.includes('warning TS')) {
            const match = line.match(/(.+)\((\d+),(\d+)\): warning TS(\d+): (.+)/);
            if (match) {
              warnings.push({
                file: match[1],
                line: parseInt(match[2]),
                column: parseInt(match[3]),
                message: match[5],
                code: `TS${match[4]}`
              });
            }
          }
        }
      }

    } catch (error: any) {
      // TypeScript compilation errors are returned as exit code 1
      if (error.stderr) {
        const lines = error.stderr.split('\n');
        for (const line of lines) {
          if (line.includes('error TS')) {
            const match = line.match(/(.+)\((\d+),(\d+)\): error TS(\d+): (.+)/);
            if (match) {
              errors.push({
                file: match[1],
                line: parseInt(match[2]),
                column: parseInt(match[3]),
                message: match[5],
                code: `TS${match[4]}`
              });
            }
          }
        }
      }
    }

    return { errors, warnings };
  }

  /**
   * Perform runtime validation
   */
  private async performRuntimeValidation(
    projectPath: string, 
    requirements: ApplicationRequirements
  ): Promise<RuntimeValidationResult> {
    this.logger.info('Performing runtime validation', { projectPath });

    try {
      // Start the application
      const serverProcess = await this.startApplication(projectPath, requirements);

      // Wait for application to start
      await this.waitForApplicationStart(3000);

      // Validate database connection
      const databaseConnection = await this.validateDatabaseConnection(projectPath);

      // Validate API endpoints
      const apiEndpoints = await this.validateAPIEndpoints('http://localhost:3000');

      // Validate dependencies
      const dependencies = await this.validateDependencies(projectPath);

      // Validate security
      const security = await this.validateSecurity(projectPath);

      // Validate performance
      const performance = await this.validatePerformance('http://localhost:3000');

      // Stop the application
      if (serverProcess) {
        serverProcess.kill();
      }

      const success = databaseConnection && 
                     apiEndpoints.every(ep => ep.status === 'success') &&
                     dependencies.every(dep => dep.status === 'installed') &&
                     security.vulnerabilities.filter(v => v.severity === 'critical').length === 0;

      return {
        success,
        databaseConnection,
        apiEndpoints,
        dependencies,
        security,
        performance
      };

    } catch (error) {
      this.logger.error('Runtime validation failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        databaseConnection: false,
        apiEndpoints: [],
        dependencies: [],
        security: {
          vulnerabilities: [],
          score: 0,
          recommendations: []
        },
        performance: {
          bundleSize: 0,
          loadTime: 0,
          memoryUsage: 0,
          recommendations: []
        }
      };
    }
  }

  /**
   * Start the application
   */
  private async startApplication(projectPath: string, requirements: ApplicationRequirements): Promise<any> {
    const backendPath = path.join(projectPath, 'backend');
    
    if (fs.existsSync(path.join(backendPath, 'package.json'))) {
      const serverProcess = spawn('npm', ['run', 'dev'], {
        cwd: backendPath,
        detached: false,
        stdio: 'pipe'
      });

      return serverProcess;
    }

    return null;
  }

  /**
   * Wait for application to start
   */
  private async waitForApplicationStart(timeout: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, timeout);
    });
  }

  /**
   * Validate database connection
   */
  private async validateDatabaseConnection(projectPath: string): Promise<boolean> {
    // This would implement actual database connection testing
    // For now, return true as a placeholder
    return true;
  }

  /**
   * Validate API endpoints
   */
  private async validateAPIEndpoints(baseUrl: string): Promise<EndpointValidationResult[]> {
    // This would implement actual API endpoint testing
    // For now, return empty array as a placeholder
    return [];
  }

  /**
   * Validate dependencies
   */
  private async validateDependencies(projectPath: string): Promise<DependencyValidationResult[]> {
    // This would implement actual dependency validation
    // For now, return empty array as a placeholder
    return [];
  }

  /**
   * Validate security
   */
  private async validateSecurity(projectPath: string): Promise<SecurityValidationResult> {
    // This would implement actual security validation
    // For now, return default result as a placeholder
    return {
      vulnerabilities: [],
      score: 100,
      recommendations: []
    };
  }

  /**
   * Validate performance
   */
  private async validatePerformance(baseUrl: string): Promise<PerformanceValidationResult> {
    // This would implement actual performance validation
    // For now, return default result as a placeholder
    return {
      bundleSize: 0,
      loadTime: 0,
      memoryUsage: 0,
      recommendations: []
    };
  }

  /**
   * Perform integration testing
   */
  private async performIntegrationTesting(
    projectPath: string, 
    requirements: ApplicationRequirements
  ): Promise<IntegrationTestResult> {
    this.logger.info('Performing integration testing', { projectPath });

    try {
      // This would implement actual integration testing
      // For now, return success as a placeholder
      return {
        success: true,
        testResults: [],
        coverage: 100,
        duration: 0
      };

    } catch (error) {
      this.logger.error('Integration testing failed', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        testResults: [],
        coverage: 0,
        duration: 0
      };
    }
  }
}
