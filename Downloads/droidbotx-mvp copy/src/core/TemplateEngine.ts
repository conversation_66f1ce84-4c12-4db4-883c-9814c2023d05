import { Logger } from './Logger';

export interface ProjectContext {
  projectName: string;
  description: string;
  domain: string;
  businessDomain?: string;
  dbName?: string;
  apiPrefix?: string;
  entities?: Array<{
    name: string;
    description?: string;
    fields?: Array<{ name: string; type: string; required?: boolean }>;
  }>;
  userRoles?: string[];
  features?: string[];
}

export interface TemplateVariable {
  pattern: RegExp;
  replacement: string | ((context: ProjectContext) => string);
  description: string;
}

/**
 * Centralized template engine for consistent variable substitution across DroidBotX
 */
export class TemplateEngine {
  private logger: Logger;
  private templates: Map<string, string> = new Map();
  private globalVariables: TemplateVariable[] = [];

  constructor() {
    this.logger = Logger.getInstance();
    this.initializeGlobalVariables();
  }

  /**
   * Initialize global template variables
   */
  private initializeGlobalVariables(): void {
    this.globalVariables = [
      // Project name variations
      {
        pattern: /undefined Application/g,
        replacement: (context) => context.projectName || 'Application',
        description: 'Replace undefined Application with project name'
      },
      {
        pattern: /undefined management solution/g,
        replacement: (context) => context.description || `${context.domain} management solution`,
        description: 'Replace undefined management solution with project description'
      },
      {
        pattern: /\{projectName\}/g,
        replacement: (context) => context.projectName || 'Project',
        description: 'Replace {projectName} template variable'
      },
      {
        pattern: /\{projectDescription\}/g,
        replacement: (context) => context.description || 'Application',
        description: 'Replace {projectDescription} template variable'
      },
      {
        pattern: /\{domain\}/g,
        replacement: (context) => context.domain || 'business',
        description: 'Replace {domain} template variable'
      },
      {
        pattern: /\{businessDomain\}/g,
        replacement: (context) => context.businessDomain || context.domain || 'business',
        description: 'Replace {businessDomain} template variable'
      },

      // Welcome messages and titles
      {
        pattern: /Welcome to undefined/g,
        replacement: (context) => `Welcome to ${context.projectName || 'Application'}`,
        description: 'Fix welcome message with undefined'
      },
      {
        pattern: /Your comprehensive undefined/g,
        replacement: (context) => `Your comprehensive ${context.description || 'application'}`,
        description: 'Fix comprehensive description with undefined'
      },

      // Database and API context
      {
        pattern: /\{dbName\}/g,
        replacement: (context) => context.dbName || `${context.projectName?.toLowerCase().replace(/\s+/g, '_')}_db` || 'app_db',
        description: 'Replace {dbName} template variable'
      },
      {
        pattern: /\{apiPrefix\}/g,
        replacement: (context) => context.apiPrefix || '/api/v1',
        description: 'Replace {apiPrefix} template variable'
      },

      // Entity-related variables
      {
        pattern: /\{entityNames\}/g,
        replacement: (context) => context.entities?.map(e => e.name).join(', ') || 'entities',
        description: 'Replace {entityNames} with comma-separated entity names'
      },
      {
        pattern: /\{entityCount\}/g,
        replacement: (context) => String(context.entities?.length || 0),
        description: 'Replace {entityCount} with number of entities'
      }
    ];
  }

  /**
   * Register a template for reuse
   */
  public registerTemplate(name: string, template: string): void {
    this.templates.set(name, template);
    this.logger.debug(`Template registered: ${name}`);
  }

  /**
   * Render a registered template with context
   */
  public renderTemplate(templateName: string, context: ProjectContext): string {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template ${templateName} not found`);
    }

    return this.substituteVariables(template, context);
  }

  /**
   * Substitute template variables in any string content
   */
  public substituteVariables(content: string, context: ProjectContext): string {
    let substituted = content;
    let substitutionsApplied = 0;

    // Apply global variable substitutions
    for (const variable of this.globalVariables) {
      const originalContent = substituted;
      
      if (typeof variable.replacement === 'string') {
        substituted = substituted.replace(variable.pattern, variable.replacement);
      } else {
        substituted = substituted.replace(variable.pattern, () => (variable.replacement as Function)(context));
      }

      if (originalContent !== substituted) {
        substitutionsApplied++;
        this.logger.debug(`Applied substitution: ${variable.description}`);
      }
    }

    // Apply custom Handlebars-style variables
    substituted = this.applyHandlebarsVariables(substituted, context);

    this.logger.debug(`Template substitution completed`, {
      substitutionsApplied,
      originalLength: content.length,
      finalLength: substituted.length
    });

    return substituted;
  }

  /**
   * Apply Handlebars-style template variables {{variable}}
   */
  private applyHandlebarsVariables(content: string, context: ProjectContext): string {
    return content.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedProperty(context, path);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Get nested property from context object
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Validate that all template variables have been substituted
   */
  public validateSubstitution(content: string): { isValid: boolean; unsubstituted: string[] } {
    const unsubstituted: string[] = [];

    // Check for remaining template variables
    const patterns = [
      /undefined Application/g,
      /undefined management/g,
      /\{[^}]+\}/g,
      /\{\{[^}]+\}\}/g
    ];

    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches) {
        unsubstituted.push(...matches);
      }
    }

    return {
      isValid: unsubstituted.length === 0,
      unsubstituted: [...new Set(unsubstituted)] // Remove duplicates
    };
  }

  /**
   * Create project context from technical specification
   */
  public createProjectContext(technicalSpec: any, semanticAnalysis?: any): ProjectContext {
    return {
      projectName: technicalSpec.projectName || 'Application',
      description: technicalSpec.description || `${semanticAnalysis?.domain || 'business'} management application`,
      domain: semanticAnalysis?.domain || technicalSpec.businessDomain?.name || 'business',
      businessDomain: technicalSpec.businessDomain?.name,
      dbName: `${(technicalSpec.projectName || 'app').toLowerCase().replace(/\s+/g, '_')}_db`,
      apiPrefix: '/api/v1',
      entities: semanticAnalysis?.entities || [],
      userRoles: semanticAnalysis?.userRoles || ['admin', 'user'],
      features: technicalSpec.features || []
    };
  }

  /**
   * Get template engine instance (singleton pattern)
   */
  private static instance: TemplateEngine;
  
  public static getInstance(): TemplateEngine {
    if (!TemplateEngine.instance) {
      TemplateEngine.instance = new TemplateEngine();
    }
    return TemplateEngine.instance;
  }
}

export default TemplateEngine;
