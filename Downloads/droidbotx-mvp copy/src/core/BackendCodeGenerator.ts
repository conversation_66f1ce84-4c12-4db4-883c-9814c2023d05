/**
 * Backend Code Generator with Contract Validation
 * 
 * Generates backend code that exactly matches database schema and OpenAPI contracts
 * with comprehensive validation and production-ready implementation.
 */

import { Logger } from './Logger';
import { LLMProviderSystem } from './LLMProviderSystem';
import { 
  ApplicationRequirements, 
  GeneratedArtifact, 
  OpenAPISpecification, 
  DatabaseSchema,
  ValidationStatus,
  ContractCompliance
} from './ContractFirstLLMGenerator';

export class BackendCodeGenerator {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Generate complete backend code from contracts
   */
  public async generateBackendCode(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    this.logger.info('Generating backend code from contracts', {
      sessionId,
      framework: requirements.targetFramework
    });

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);
      const databaseData = JSON.parse(databaseArtifact.content);
      const databaseSchema: DatabaseSchema = databaseData.schema;

      const artifacts: GeneratedArtifact[] = [];

      // Step 1: Generate data models/entities
      const modelArtifacts = await this.generateDataModels(openApiSpec, databaseSchema, requirements, sessionId);
      artifacts.push(...modelArtifacts);

      // Step 2: Generate database connection and configuration
      const dbConfigArtifacts = await this.generateDatabaseConfiguration(databaseSchema, requirements, sessionId);
      artifacts.push(...dbConfigArtifacts);

      // Step 3: Generate repository/data access layer
      const repositoryArtifacts = await this.generateRepositoryLayer(openApiSpec, databaseSchema, requirements, sessionId);
      artifacts.push(...repositoryArtifacts);

      // Step 4: Generate service layer
      const serviceArtifacts = await this.generateServiceLayer(openApiSpec, databaseSchema, requirements, sessionId);
      artifacts.push(...serviceArtifacts);

      // Step 5: Generate controller/route handlers
      const controllerArtifacts = await this.generateControllerLayer(openApiSpec, requirements, sessionId);
      artifacts.push(...controllerArtifacts);

      // Step 6: Generate middleware (auth, validation, error handling)
      const middlewareArtifacts = await this.generateMiddleware(openApiSpec, requirements, sessionId);
      artifacts.push(...middlewareArtifacts);

      // Step 7: Generate main application file
      const appArtifact = await this.generateMainApplication(openApiSpec, requirements, sessionId);
      artifacts.push(appArtifact);

      // Step 8: Generate package.json and configuration files
      const configArtifacts = await this.generateConfigurationFiles(openApiSpec, requirements, sessionId);
      artifacts.push(...configArtifacts);

      // Step 9: Validate all generated code
      await this.validateGeneratedCode(artifacts, openApiSpec, databaseSchema);

      this.logger.info('Backend code generation completed', {
        sessionId,
        artifactCount: artifacts.length,
        totalLines: artifacts.reduce((sum, a) => sum + a.content.split('\n').length, 0)
      });

      return artifacts;

    } catch (error) {
      this.logger.error('Backend code generation failed', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Generate data models/entities
   */
  private async generateDataModels(
    openApiSpec: OpenAPISpecification,
    databaseSchema: DatabaseSchema,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a senior backend developer. Generate comprehensive data models that exactly match both the OpenAPI specification and database schema.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

Framework: ${requirements.targetFramework}
Database: ${requirements.databaseType}

Generate complete data models including:

1. **Entity Models**: Classes that represent database tables
2. **DTO Models**: Data transfer objects for API requests/responses
3. **Validation Models**: Input validation schemas
4. **Type Definitions**: TypeScript interfaces and types
5. **Enum Definitions**: All enumeration types
6. **Relationship Mappings**: Proper entity relationships

Requirements:
- Use TypeScript with strict typing
- Include comprehensive validation decorators
- Add proper database decorators (TypeORM/Prisma)
- Ensure exact field name and type matching
- Include proper relationships (OneToMany, ManyToOne, etc.)
- Add audit fields (createdAt, updatedAt, etc.)
- Include soft delete support where needed
- Add comprehensive JSDoc documentation

For ${requirements.targetFramework}, use appropriate:
- ORM decorators and configurations
- Validation libraries (class-validator, joi, zod)
- Type definitions and interfaces
- Export/import patterns

Generate separate files for each entity and return as an array of file objects:
[
  {
    "fileName": "src/models/EntityName.ts",
    "content": "// Complete TypeScript model code"
  }
]

Ensure all models are production-ready with proper error handling, validation, and type safety.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const modelFiles = this.parseFileArrayResponse(response, 'Data Models');
    
    return modelFiles.map(file => ({
      type: 'backend' as const,
      name: `model-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate database configuration
   */
  private async generateDatabaseConfiguration(
    databaseSchema: DatabaseSchema,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a database configuration expert. Generate comprehensive database configuration for ${requirements.databaseType}.

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

Framework: ${requirements.targetFramework}
Database Type: ${requirements.databaseType}

Generate complete database configuration including:

1. **Connection Configuration**: Database connection setup
2. **ORM Configuration**: TypeORM/Prisma configuration
3. **Migration Configuration**: Database migration setup
4. **Connection Pool**: Production-ready connection pooling
5. **Environment Configuration**: Environment-specific settings
6. **Health Checks**: Database health monitoring
7. **Logging Configuration**: Query logging and monitoring
8. **Security Configuration**: SSL, encryption, access control

Requirements:
- Use environment variables for all sensitive data
- Include production-ready connection pooling
- Add comprehensive error handling
- Include health check endpoints
- Add query logging and monitoring
- Use SSL/TLS for production
- Include connection retry logic
- Add graceful shutdown handling

Generate configuration files:
[
  {
    "fileName": "src/config/database.ts",
    "content": "// Database configuration"
  },
  {
    "fileName": "src/config/orm.ts", 
    "content": "// ORM configuration"
  }
]

Ensure all configurations are production-ready and secure.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    const configFiles = this.parseFileArrayResponse(response, 'Database Configuration');
    
    return configFiles.map(file => ({
      type: 'backend' as const,
      name: `config-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate repository layer
   */
  private async generateRepositoryLayer(
    openApiSpec: OpenAPISpecification,
    databaseSchema: DatabaseSchema,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a data access layer expert. Generate comprehensive repository classes for all entities.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

Framework: ${requirements.targetFramework}
Database: ${requirements.databaseType}

Generate complete repository layer including:

1. **Base Repository**: Generic repository with common operations
2. **Entity Repositories**: Specific repositories for each entity
3. **Query Builders**: Complex query building methods
4. **Transaction Support**: Database transaction handling
5. **Caching Layer**: Query result caching
6. **Pagination**: Efficient pagination implementation
7. **Filtering**: Dynamic filtering capabilities
8. **Sorting**: Multi-column sorting support
9. **Bulk Operations**: Efficient bulk insert/update/delete
10. **Error Handling**: Comprehensive error handling

Requirements:
- Use TypeScript with strict typing
- Include comprehensive error handling
- Add query optimization
- Include transaction support
- Add caching where appropriate
- Use proper ORM patterns
- Include comprehensive logging
- Add performance monitoring

Generate repository files for each entity:
[
  {
    "fileName": "src/repositories/BaseRepository.ts",
    "content": "// Base repository implementation"
  },
  {
    "fileName": "src/repositories/EntityNameRepository.ts",
    "content": "// Entity-specific repository"
  }
]

Ensure all repositories are production-ready with proper error handling and performance optimization.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const repositoryFiles = this.parseFileArrayResponse(response, 'Repository Layer');
    
    return repositoryFiles.map(file => ({
      type: 'backend' as const,
      name: `repository-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate service layer
   */
  private async generateServiceLayer(
    openApiSpec: OpenAPISpecification,
    databaseSchema: DatabaseSchema,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a business logic expert. Generate comprehensive service classes that implement business logic.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

Framework: ${requirements.targetFramework}

Generate complete service layer including:

1. **Business Logic Services**: Core business operations
2. **Validation Services**: Input validation and business rules
3. **Authentication Service**: User authentication and authorization
4. **Email/Notification Services**: Communication services
5. **File Upload Services**: File handling and storage
6. **Cache Services**: Caching and performance optimization
7. **External API Services**: Third-party integrations
8. **Audit Services**: Activity logging and tracking

Requirements:
- Implement all OpenAPI operations
- Include comprehensive error handling
- Add input validation and sanitization
- Include business rule enforcement
- Add transaction management
- Include logging and monitoring
- Add security checks and authorization
- Use dependency injection patterns

Generate service files:
[
  {
    "fileName": "src/services/EntityNameService.ts",
    "content": "// Entity service implementation"
  }
]

Ensure all services are production-ready with proper error handling and security.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const serviceFiles = this.parseFileArrayResponse(response, 'Service Layer');

    return serviceFiles.map(file => ({
      type: 'backend' as const,
      name: `service-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate controller layer
   */
  private async generateControllerLayer(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are an API controller expert. Generate comprehensive controllers that exactly implement the OpenAPI specification.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Framework: ${requirements.targetFramework}

Generate complete controller layer including:

1. **REST Controllers**: All API endpoints from OpenAPI spec
2. **Request Validation**: Input validation middleware
3. **Response Formatting**: Consistent response structures
4. **Error Handling**: Comprehensive error responses
5. **Authentication**: JWT token validation
6. **Authorization**: Role-based access control
7. **Rate Limiting**: API rate limiting
8. **Logging**: Request/response logging
9. **Documentation**: Swagger/OpenAPI integration
10. **Health Checks**: System health endpoints

Requirements:
- Implement every endpoint from OpenAPI specification
- Use exact path patterns and HTTP methods
- Include proper status codes and responses
- Add comprehensive input validation
- Include authentication and authorization
- Add rate limiting and security headers
- Use proper error handling and logging
- Include API documentation integration

Generate controller files:
[
  {
    "fileName": "src/controllers/EntityNameController.ts",
    "content": "// Controller implementation"
  }
]

Ensure all controllers exactly match the OpenAPI specification.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.05,
      maxTokens: 8000
    });

    const controllerFiles = this.parseFileArrayResponse(response, 'Controller Layer');

    return controllerFiles.map(file => ({
      type: 'backend' as const,
      name: `controller-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate middleware
   */
  private async generateMiddleware(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a middleware expert. Generate comprehensive middleware for security, validation, and request processing.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Framework: ${requirements.targetFramework}

Generate complete middleware including:

1. **Authentication Middleware**: JWT token validation
2. **Authorization Middleware**: Role and permission checking
3. **Validation Middleware**: Request validation against OpenAPI schemas
4. **Error Handling Middleware**: Global error handling
5. **Logging Middleware**: Request/response logging
6. **Rate Limiting Middleware**: API rate limiting
7. **CORS Middleware**: Cross-origin resource sharing
8. **Security Headers Middleware**: Security headers
9. **Request Parsing Middleware**: Body parsing and sanitization
10. **Response Formatting Middleware**: Consistent response format

Requirements:
- Include comprehensive security measures
- Add proper error handling and logging
- Use OpenAPI schemas for validation
- Include rate limiting and CORS
- Add security headers and sanitization
- Include audit logging
- Use proper TypeScript typing
- Add comprehensive documentation

Generate middleware files:
[
  {
    "fileName": "src/middleware/auth.ts",
    "content": "// Authentication middleware"
  },
  {
    "fileName": "src/middleware/validation.ts",
    "content": "// Validation middleware"
  }
]

Ensure all middleware is production-ready and secure.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const middlewareFiles = this.parseFileArrayResponse(response, 'Middleware');

    return middlewareFiles.map(file => ({
      type: 'backend' as const,
      name: `middleware-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  private async generateMainApplication(openApiSpec: OpenAPISpecification, requirements: ApplicationRequirements, sessionId: string): Promise<GeneratedArtifact> {
    throw new Error('Method not implemented');
  }

  private async generateConfigurationFiles(openApiSpec: OpenAPISpecification, requirements: ApplicationRequirements, sessionId: string): Promise<GeneratedArtifact[]> {
    throw new Error('Method not implemented');
  }

  private async validateGeneratedCode(artifacts: GeneratedArtifact[], openApiSpec: OpenAPISpecification, databaseSchema: DatabaseSchema): Promise<void> {
    throw new Error('Method not implemented');
  }

  /**
   * Parse file array response
   */
  private parseFileArrayResponse(response: string, context: string): Array<{ fileName: string; content: string }> {
    try {
      let cleaned = response.trim();
      
      // Remove markdown code blocks
      cleaned = cleaned.replace(/^```json\s*/gm, '');
      cleaned = cleaned.replace(/^```typescript\s*/gm, '');
      cleaned = cleaned.replace(/^```\s*/gm, '');
      cleaned = cleaned.replace(/\s*```$/gm, '');
      
      // Extract JSON array
      const arrayStart = cleaned.indexOf('[');
      const arrayEnd = cleaned.lastIndexOf(']');
      
      if (arrayStart !== -1 && arrayEnd !== -1) {
        cleaned = cleaned.substring(arrayStart, arrayEnd + 1);
      }

      return JSON.parse(cleaned);
    } catch (error) {
      this.logger.error(`Failed to parse file array response for ${context}`, {
        error: error instanceof Error ? error.message : String(error),
        responsePreview: response.substring(0, 200)
      });
      throw new Error(`Invalid file array response for ${context}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Extract dependencies from code
   */
  private extractDependencies(content: string): string[] {
    const dependencies: string[] = [];
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const dep = match[1];
      if (!dep.startsWith('.') && !dep.startsWith('/')) {
        dependencies.push(dep);
      }
    }
    
    return [...new Set(dependencies)];
  }

  /**
   * Extract exports from code
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(?:class|interface|function|const|let|var)\s+(\w+)/g;
    let match;
    
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    
    return exports;
  }

  /**
   * Extract imports from code
   */
  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        // Named imports
        const namedImports = match[1].split(',').map(imp => imp.trim());
        imports.push(...namedImports);
      } else if (match[2]) {
        // Namespace import
        imports.push(match[2]);
      } else if (match[3]) {
        // Default import
        imports.push(match[3]);
      }
    }
    
    return imports;
  }
}
