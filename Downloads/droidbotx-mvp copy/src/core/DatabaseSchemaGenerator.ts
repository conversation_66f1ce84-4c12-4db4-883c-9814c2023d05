/**
 * Database Schema Generator with Contract Validation
 * 
 * Generates database schemas that exactly conform to OpenAPI contracts
 * with proper relationships, constraints, and data integrity rules.
 */

import { Logger } from './Logger';
import { LLMProviderSystem } from './LLMProviderSystem';
import { 
  ApplicationRequirements, 
  GeneratedArtifact, 
  OpenAPISpecification, 
  DatabaseSchema,
  DatabaseTable,
  DatabaseColumn,
  DatabaseForeignKey,
  DatabaseRelationship,
  DatabaseIndex,
  DatabaseConstraint,
  ValidationStatus,
  ContractCompliance,
  ValidationError
} from './ContractFirstLLMGenerator';

export class DatabaseSchemaGenerator {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Generate database schema from OpenAPI contract
   */
  public async generateDatabaseSchema(
    contractArtifact: GeneratedArtifact, 
    requirements: ApplicationRequirements, 
    sessionId: string
  ): Promise<GeneratedArtifact> {
    this.logger.info('Generating database schema from OpenAPI contract', {
      sessionId,
      databaseType: requirements.databaseType
    });

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);

      // Step 1: Extract entities and relationships from OpenAPI schemas
      const entityAnalysis = await this.analyzeOpenAPIEntities(openApiSpec, requirements);

      // Step 2: Generate database schema structure
      const schemaStructure = await this.generateSchemaStructure(entityAnalysis, requirements);

      // Step 3: Add relationships and foreign keys
      const schemaWithRelationships = await this.addRelationshipsAndConstraints(schemaStructure, entityAnalysis, requirements);

      // Step 4: Add indexes and performance optimizations
      const optimizedSchema = await this.addIndexesAndOptimizations(schemaWithRelationships, requirements);

      // Step 5: Generate SQL DDL statements
      const sqlDDL = await this.generateSQLDDL(optimizedSchema, requirements);

      // Step 6: Validate schema against OpenAPI contract
      const validationResult = await this.validateSchemaContractCompliance(optimizedSchema, openApiSpec);

      // Step 7: Generate migration scripts
      const migrationScripts = await this.generateMigrationScripts(optimizedSchema, requirements);

      const artifact: GeneratedArtifact = {
        type: 'database',
        name: 'database-schema',
        content: JSON.stringify({
          schema: optimizedSchema,
          ddl: sqlDDL,
          migrations: migrationScripts
        }, null, 2),
        filePath: 'database/schema.json',
        dependencies: [],
        exports: ['DatabaseSchema'],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'qwen/qwen3-coder',
          validationStatus: validationResult.validationStatus,
          contractCompliance: validationResult.contractCompliance
        }
      };

      this.logger.info('Database schema generated successfully', {
        sessionId,
        tablesCount: optimizedSchema.tables.length,
        relationshipsCount: optimizedSchema.relationships.length,
        indexesCount: optimizedSchema.indexes.length
      });

      return artifact;

    } catch (error) {
      this.logger.error('Database schema generation failed', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Analyze OpenAPI entities to extract database requirements
   */
  private async analyzeOpenAPIEntities(openApiSpec: OpenAPISpecification, requirements: ApplicationRequirements): Promise<any> {
    const prompt = `You are a database architect expert. Analyze the OpenAPI specification to extract comprehensive database requirements.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Type: ${requirements.databaseType}
Application Domain: ${requirements.businessDomain}

Analyze the OpenAPI schemas and extract:

1. **Entities**: All business entities that need database tables
2. **Properties**: All properties with their types, constraints, and validation rules
3. **Relationships**: Identify relationships between entities (one-to-one, one-to-many, many-to-many)
4. **Constraints**: Extract validation rules that should become database constraints
5. **Indexes**: Identify fields that need indexing for performance
6. **Business Rules**: Extract business logic that requires database-level enforcement
7. **Security Requirements**: Identify fields that need encryption or special handling
8. **Audit Requirements**: Identify entities that need audit trails

Return a comprehensive analysis in this JSON format:
{
  "entities": [
    {
      "name": "EntityName",
      "tableName": "table_name",
      "description": "Entity description",
      "properties": [
        {
          "name": "propertyName",
          "columnName": "column_name",
          "type": "string|integer|decimal|boolean|date|timestamp|text|json",
          "nullable": true|false,
          "primaryKey": true|false,
          "unique": true|false,
          "defaultValue": "default value or null",
          "constraints": ["constraint descriptions"],
          "validation": {
            "minLength": 0,
            "maxLength": 255,
            "pattern": "regex pattern",
            "enum": ["value1", "value2"],
            "minimum": 0,
            "maximum": 100
          },
          "indexed": true|false,
          "encrypted": true|false
        }
      ],
      "relationships": [
        {
          "type": "one-to-one|one-to-many|many-to-many",
          "targetEntity": "TargetEntity",
          "foreignKey": "foreign_key_column",
          "referencedKey": "referenced_column",
          "onDelete": "CASCADE|SET NULL|RESTRICT",
          "onUpdate": "CASCADE|SET NULL|RESTRICT",
          "junctionTable": "junction_table_name (for many-to-many)"
        }
      ],
      "indexes": [
        {
          "name": "index_name",
          "columns": ["column1", "column2"],
          "unique": true|false,
          "type": "btree|hash|gin|gist"
        }
      ],
      "constraints": [
        {
          "name": "constraint_name",
          "type": "check|unique|foreign_key",
          "definition": "constraint definition"
        }
      ],
      "auditEnabled": true|false,
      "softDelete": true|false
    }
  ],
  "globalConstraints": [
    {
      "name": "constraint_name",
      "description": "Global constraint description",
      "definition": "SQL constraint definition"
    }
  ],
  "performanceConsiderations": [
    {
      "table": "table_name",
      "recommendation": "Performance recommendation",
      "reasoning": "Why this optimization is needed"
    }
  ]
}

Ensure the analysis is comprehensive and covers all aspects needed for a production-ready database schema.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Entity Analysis');
  }

  /**
   * Generate database schema structure
   */
  private async generateSchemaStructure(entityAnalysis: any, requirements: ApplicationRequirements): Promise<DatabaseSchema> {
    const prompt = `You are a senior database architect. Generate a comprehensive database schema structure based on the entity analysis.

Entity Analysis:
${JSON.stringify(entityAnalysis, null, 2)}

Database Type: ${requirements.databaseType}
Application: ${requirements.name}

Generate a complete database schema with:

1. **Tables**: All required tables with proper naming conventions
2. **Columns**: All columns with appropriate data types for ${requirements.databaseType}
3. **Primary Keys**: Proper primary key definitions
4. **Data Types**: Use appropriate ${requirements.databaseType} data types
5. **Constraints**: NOT NULL, UNIQUE, CHECK constraints
6. **Default Values**: Appropriate default values
7. **Audit Columns**: created_at, updated_at, created_by, updated_by where needed
8. **Soft Delete**: deleted_at columns where specified
9. **Versioning**: version columns for optimistic locking where needed

Database-specific considerations for ${requirements.databaseType}:
- Use appropriate data types (VARCHAR, TEXT, INTEGER, DECIMAL, TIMESTAMP, etc.)
- Follow naming conventions (snake_case for PostgreSQL, camelCase for MongoDB)
- Use proper constraints and indexes
- Consider performance implications

Return the schema in this exact JSON format:
{
  "tables": [
    {
      "name": "table_name",
      "columns": [
        {
          "name": "column_name",
          "type": "data_type",
          "nullable": true|false,
          "defaultValue": "default_value",
          "constraints": ["constraint1", "constraint2"]
        }
      ],
      "primaryKey": ["column1", "column2"],
      "foreignKeys": []
    }
  ],
  "relationships": [],
  "indexes": [],
  "constraints": []
}`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.05,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Database Schema Structure');
  }

  /**
   * Add relationships and constraints
   */
  private async addRelationshipsAndConstraints(
    schema: DatabaseSchema, 
    entityAnalysis: any, 
    requirements: ApplicationRequirements
  ): Promise<DatabaseSchema> {
    const prompt = `You are a database relationship expert. Enhance the database schema with comprehensive relationships and constraints.

Current Schema:
${JSON.stringify(schema, null, 2)}

Entity Analysis:
${JSON.stringify(entityAnalysis, null, 2)}

Database Type: ${requirements.databaseType}

Add comprehensive relationships and constraints:

1. **Foreign Key Relationships**: Add all foreign key constraints with proper referential integrity
2. **Junction Tables**: Create junction tables for many-to-many relationships
3. **Check Constraints**: Add business rule constraints
4. **Unique Constraints**: Add unique constraints for business keys
5. **Cascade Rules**: Define proper ON DELETE and ON UPDATE behaviors
6. **Cross-Table Constraints**: Add constraints that span multiple tables
7. **Data Integrity**: Ensure data consistency rules are enforced
8. **Business Logic**: Implement business rules at database level where appropriate

For ${requirements.databaseType}, ensure:
- Proper foreign key syntax and naming
- Appropriate cascade behaviors
- Performance-optimized constraint definitions
- Proper constraint naming conventions

Return the enhanced schema with all relationships and constraints properly defined.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Schema with Relationships');
  }

  /**
   * Add indexes and performance optimizations
   */
  private async addIndexesAndOptimizations(schema: DatabaseSchema, requirements: ApplicationRequirements): Promise<DatabaseSchema> {
    const prompt = `You are a database performance expert. Add comprehensive indexes and optimizations to the database schema.

Current Schema:
${JSON.stringify(schema, null, 2)}

Database Type: ${requirements.databaseType}
Application Domain: ${requirements.businessDomain}

Add performance optimizations:

1. **Primary Indexes**: Ensure all primary keys have proper indexes
2. **Foreign Key Indexes**: Add indexes on all foreign key columns
3. **Query Optimization**: Add indexes for common query patterns
4. **Composite Indexes**: Create multi-column indexes for complex queries
5. **Unique Indexes**: Add unique indexes for business keys
6. **Partial Indexes**: Use partial indexes where appropriate
7. **Text Search**: Add full-text search indexes where needed
8. **Performance Monitoring**: Add indexes for common filtering and sorting

For ${requirements.databaseType}, consider:
- B-tree indexes for range queries
- Hash indexes for equality lookups
- GIN/GiST indexes for complex data types
- Partial indexes for filtered queries
- Index maintenance overhead

Return the optimized schema with comprehensive indexing strategy.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    return this.parseJSONResponse(response, 'Optimized Schema');
  }

  /**
   * Generate SQL DDL statements
   */
  private async generateSQLDDL(schema: DatabaseSchema, requirements: ApplicationRequirements): Promise<string> {
    const prompt = `You are a ${requirements.databaseType} expert. Generate complete SQL DDL statements for the database schema.

Database Schema:
${JSON.stringify(schema, null, 2)}

Database Type: ${requirements.databaseType}

Generate comprehensive SQL DDL including:

1. **CREATE TABLE statements**: All tables with proper syntax
2. **ALTER TABLE statements**: Foreign key constraints
3. **CREATE INDEX statements**: All indexes
4. **CREATE CONSTRAINT statements**: Check constraints
5. **CREATE SEQUENCE statements**: Auto-increment sequences (if needed)
6. **CREATE TRIGGER statements**: Audit triggers (if needed)
7. **CREATE FUNCTION statements**: Custom functions (if needed)
8. **GRANT statements**: Basic permissions

Requirements:
- Use proper ${requirements.databaseType} syntax
- Include proper error handling
- Add comments for documentation
- Use transaction blocks where appropriate
- Include rollback statements for safety
- Follow best practices for ${requirements.databaseType}

Return only the SQL DDL statements, properly formatted and commented.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.05,
      maxTokens: 8000
    });

    return response.trim();
  }

  /**
   * Generate migration scripts
   */
  private async generateMigrationScripts(schema: DatabaseSchema, requirements: ApplicationRequirements): Promise<string[]> {
    const prompt = `You are a database migration expert. Generate migration scripts for the database schema.

Database Schema:
${JSON.stringify(schema, null, 2)}

Database Type: ${requirements.databaseType}

Generate migration scripts in proper order:

1. **001_create_tables.sql**: Create all tables
2. **002_add_foreign_keys.sql**: Add foreign key constraints
3. **003_create_indexes.sql**: Create all indexes
4. **004_add_constraints.sql**: Add check constraints
5. **005_create_triggers.sql**: Create audit triggers (if needed)
6. **006_insert_seed_data.sql**: Insert initial/seed data

Each migration should:
- Be idempotent (can be run multiple times safely)
- Include proper error handling
- Have rollback instructions
- Be properly commented
- Follow ${requirements.databaseType} best practices

Return an array of migration scripts with proper naming and content.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Migration Scripts');
  }

  /**
   * Validate schema against OpenAPI contract
   */
  private async validateSchemaContractCompliance(
    schema: DatabaseSchema, 
    openApiSpec: OpenAPISpecification
  ): Promise<{ validationStatus: ValidationStatus; contractCompliance: ContractCompliance }> {
    // Extract schemas from OpenAPI
    const apiSchemas = openApiSpec.components.schemas;
    const errors: ValidationError[] = [];
    const violations = [];

    // Validate that all API schemas have corresponding database tables
    for (const [schemaName, schemaDefinition] of Object.entries(apiSchemas)) {
      const tableName = this.convertSchemaNameToTableName(schemaName);
      const table = schema.tables.find(t => t.name === tableName);

      if (!table) {
        errors.push({
          type: 'contract',
          message: `Missing database table for API schema: ${schemaName}`,
          location: `tables.${tableName}`,
          severity: 'error'
        });
        violations.push({
          type: 'missing-endpoint',
          description: `Database table ${tableName} missing for API schema ${schemaName}`,
          expectedValue: tableName,
          actualValue: null,
          severity: 'critical',
          autoFixable: true
        });
      }
    }

    const validationStatus: ValidationStatus = {
      syntaxValid: true,
      typeValid: true,
      contractCompliant: errors.length === 0,
      securityCompliant: true,
      errors,
      warnings: []
    };

    const contractCompliance: ContractCompliance = {
      openApiCompliant: true,
      databaseSchemaCompliant: true,
      typeDefinitionsCompliant: errors.length === 0,
      securityCompliant: true,
      complianceScore: errors.length === 0 ? 1.0 : 0.8,
      violations
    };

    return { validationStatus, contractCompliance };
  }

  /**
   * Convert schema name to table name
   */
  private convertSchemaNameToTableName(schemaName: string): string {
    // Convert PascalCase to snake_case and pluralize
    return schemaName
      .replace(/([A-Z])/g, '_$1')
      .toLowerCase()
      .replace(/^_/, '') + 's';
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(response: string, context: string): any {
    try {
      let cleaned = response.trim();
      
      // Remove markdown code blocks
      cleaned = cleaned.replace(/^```json\s*/gm, '');
      cleaned = cleaned.replace(/^```sql\s*/gm, '');
      cleaned = cleaned.replace(/^```\s*/gm, '');
      cleaned = cleaned.replace(/\s*```$/gm, '');
      
      // Extract JSON if present
      const jsonStart = cleaned.indexOf('{');
      const jsonEnd = cleaned.lastIndexOf('}');
      
      if (jsonStart !== -1 && jsonEnd !== -1) {
        cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
      }

      return JSON.parse(cleaned);
    } catch (error) {
      this.logger.error(`Failed to parse JSON response for ${context}`, {
        error: error instanceof Error ? error.message : String(error),
        responsePreview: response.substring(0, 200)
      });
      throw new Error(`Invalid JSON response for ${context}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
