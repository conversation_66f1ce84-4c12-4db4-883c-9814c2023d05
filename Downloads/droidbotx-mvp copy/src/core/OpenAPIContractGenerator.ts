/**
 * OpenAPI Contract Generator
 * 
 * Generates comprehensive OpenAPI specifications using pure LLM generation
 * that serve as the contract for all subsequent code generation phases.
 */

import { Logger } from './Logger';
import { LLMProviderSystem, LLMMessage } from './LLMProviderSystem';
import { ApplicationRequirements, GeneratedArtifact, OpenAPISpecification, ValidationStatus, ContractCompliance, ValidationError, ValidationWarning } from './ContractFirstLLMGenerator';

export class OpenAPIContractGenerator {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Generate comprehensive OpenAPI specification from requirements
   */
  public async generateOpenAPIContract(requirements: ApplicationRequirements, sessionId: string): Promise<GeneratedArtifact> {
    this.logger.info('Generating OpenAPI contract', {
      sessionId,
      applicationName: requirements.name,
      features: requirements.features.length
    });

    try {
      // Step 1: Analyze requirements and extract API structure
      const apiAnalysis = await this.analyzeAPIRequirements(requirements);

      // Step 2: Generate comprehensive OpenAPI specification
      const openApiSpec = await this.generateOpenAPISpecification(requirements, apiAnalysis);

      // Step 3: Validate and enhance the specification
      const validatedSpec = await this.validateAndEnhanceSpecification(openApiSpec, requirements);

      // Step 4: Generate security definitions
      const securityEnhancedSpec = await this.addSecurityDefinitions(validatedSpec, requirements);

      // Step 5: Add comprehensive examples and documentation
      const finalSpec = await this.addExamplesAndDocumentation(securityEnhancedSpec, requirements);

      // Step 6: Perform final validation
      const validationStatus = await this.validateOpenAPISpecification(finalSpec);

      const artifact: GeneratedArtifact = {
        type: 'contract',
        name: 'openapi-specification',
        content: JSON.stringify(finalSpec, null, 2),
        filePath: 'api/openapi.json',
        dependencies: [],
        exports: ['OpenAPISpecification'],
        imports: [],
        metadata: {
          generatedAt: new Date(),
          llmModel: 'qwen/qwen3-coder',
          validationStatus,
          contractCompliance: {
            openApiCompliant: true,
            databaseSchemaCompliant: true,
            typeDefinitionsCompliant: true,
            securityCompliant: true,
            complianceScore: 1.0,
            violations: []
          }
        }
      };

      this.logger.info('OpenAPI contract generated successfully', {
        sessionId,
        pathsCount: Object.keys(finalSpec.paths).length,
        schemasCount: Object.keys(finalSpec.components.schemas).length,
        validationStatus: validationStatus.syntaxValid
      });

      return artifact;

    } catch (error) {
      this.logger.error('OpenAPI contract generation failed', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Analyze requirements to extract API structure
   */
  private async analyzeAPIRequirements(requirements: ApplicationRequirements): Promise<any> {
    const prompt = `You are a senior API architect. Analyze the following application requirements and extract a comprehensive API structure.

Application: ${requirements.name}
Description: ${requirements.description}
Business Domain: ${requirements.businessDomain}
Features: ${requirements.features.join(', ')}
Target Framework: ${requirements.targetFramework}
Database: ${requirements.databaseType}

Based on these requirements, provide a detailed API analysis including:

1. **Core Entities**: Identify the main business entities and their relationships
2. **API Endpoints**: List all required REST endpoints with HTTP methods
3. **Data Models**: Define the data structures for requests and responses
4. **Authentication**: Specify authentication and authorization requirements
5. **Business Logic**: Identify key business rules and validation requirements
6. **Error Handling**: Define error scenarios and response structures
7. **Performance Considerations**: Identify pagination, filtering, and caching needs

Return a comprehensive JSON structure with the following format:
{
  "entities": [
    {
      "name": "EntityName",
      "description": "Entity description",
      "properties": [
        {
          "name": "propertyName",
          "type": "string|number|boolean|date|array|object",
          "required": true|false,
          "description": "Property description",
          "validation": ["validation rules"]
        }
      ],
      "relationships": [
        {
          "type": "one-to-one|one-to-many|many-to-many",
          "target": "TargetEntity",
          "description": "Relationship description"
        }
      ]
    }
  ],
  "endpoints": [
    {
      "path": "/api/resource",
      "method": "GET|POST|PUT|DELETE|PATCH",
      "summary": "Endpoint summary",
      "description": "Detailed description",
      "parameters": [],
      "requestBody": {},
      "responses": {},
      "security": [],
      "tags": ["tag1", "tag2"]
    }
  ],
  "authentication": {
    "type": "jwt|oauth2|apikey",
    "description": "Authentication description",
    "flows": []
  },
  "businessRules": [
    {
      "name": "Rule name",
      "description": "Rule description",
      "validation": "Validation logic"
    }
  ]
}

Ensure the analysis is comprehensive and covers all aspects needed for a production-ready API.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    return this.parseJSONResponse(response, 'API Requirements Analysis');
  }

  /**
   * Generate comprehensive OpenAPI specification
   */
  private async generateOpenAPISpecification(requirements: ApplicationRequirements, apiAnalysis: any): Promise<OpenAPISpecification> {
    const prompt = `You are an expert OpenAPI specification architect. Generate a complete, production-ready OpenAPI 3.0.3 specification based on the requirements and API analysis.

Application Requirements:
${JSON.stringify(requirements, null, 2)}

API Analysis:
${JSON.stringify(apiAnalysis, null, 2)}

Generate a comprehensive OpenAPI specification that includes:

1. **Complete Info Section**: Version, title, description, contact, license
2. **Server Definitions**: Development, staging, and production servers
3. **All API Paths**: Complete CRUD operations for all entities
4. **Comprehensive Schemas**: All data models with proper types and validation
5. **Security Schemes**: JWT authentication with proper scopes
6. **Response Definitions**: Success and error responses for all endpoints
7. **Parameter Definitions**: Query, path, and header parameters
8. **Request Body Schemas**: For POST, PUT, and PATCH operations
9. **Examples**: Realistic examples for all requests and responses
10. **Tags and Organization**: Proper grouping and documentation

Requirements for the specification:
- Use OpenAPI 3.0.3 format
- Include comprehensive validation rules (min, max, pattern, enum)
- Define proper HTTP status codes for all scenarios
- Include pagination for list endpoints
- Add filtering and sorting parameters where appropriate
- Define proper error response schemas
- Include rate limiting headers
- Add comprehensive descriptions and summaries
- Use consistent naming conventions
- Include all necessary security definitions

Return ONLY the complete OpenAPI specification as valid JSON. No additional text or explanations.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.05,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'OpenAPI Specification');
  }

  /**
   * Validate and enhance the OpenAPI specification
   */
  private async validateAndEnhanceSpecification(spec: OpenAPISpecification, requirements: ApplicationRequirements): Promise<OpenAPISpecification> {
    const prompt = `You are an OpenAPI validation expert. Review and enhance the following OpenAPI specification to ensure it's production-ready and complete.

Current Specification:
${JSON.stringify(spec, null, 2)}

Application Requirements:
${JSON.stringify(requirements, null, 2)}

Validation and Enhancement Tasks:
1. **Syntax Validation**: Ensure valid OpenAPI 3.0.3 syntax
2. **Completeness Check**: Verify all required endpoints are present
3. **Schema Validation**: Ensure all schemas are properly defined with types
4. **Response Completeness**: Verify all endpoints have proper response definitions
5. **Error Handling**: Ensure comprehensive error responses (400, 401, 403, 404, 422, 500)
6. **Parameter Validation**: Add missing query parameters, path parameters
7. **Security Enhancement**: Ensure proper security scheme definitions
8. **Documentation Enhancement**: Improve descriptions and examples
9. **Consistency Check**: Ensure consistent naming and structure
10. **Production Readiness**: Add necessary headers, pagination, filtering

Fix any issues found and enhance the specification. Return the complete, enhanced OpenAPI specification as valid JSON.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Enhanced OpenAPI Specification');
  }

  /**
   * Add comprehensive security definitions
   */
  private async addSecurityDefinitions(spec: OpenAPISpecification, requirements: ApplicationRequirements): Promise<OpenAPISpecification> {
    const prompt = `You are a security architect. Enhance the OpenAPI specification with comprehensive security definitions.

Current Specification:
${JSON.stringify(spec, null, 2)}

Add comprehensive security including:
1. **JWT Authentication**: Bearer token authentication with proper scopes
2. **API Key Authentication**: For service-to-service communication
3. **OAuth2 Flows**: If applicable for the application type
4. **Security Requirements**: Apply security to all protected endpoints
5. **Rate Limiting**: Define rate limiting headers and responses
6. **CORS Headers**: Define proper CORS response headers
7. **Security Headers**: Add security-related response headers
8. **Input Validation**: Enhance schema validation for security
9. **Error Responses**: Security-specific error responses (401, 403)
10. **Audit Logging**: Define audit-related headers and responses

Ensure all endpoints have appropriate security requirements based on their functionality.
Public endpoints (like login, register) should not require authentication.
Protected endpoints should require appropriate authentication and authorization.

Return the complete OpenAPI specification with enhanced security as valid JSON.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Security Enhanced OpenAPI Specification');
  }

  /**
   * Add comprehensive examples and documentation
   */
  private async addExamplesAndDocumentation(spec: OpenAPISpecification, requirements: ApplicationRequirements): Promise<OpenAPISpecification> {
    const prompt = `You are a technical documentation expert. Enhance the OpenAPI specification with comprehensive examples and documentation.

Current Specification:
${JSON.stringify(spec, null, 2)}

Enhancement Requirements:
1. **Request Examples**: Add realistic examples for all request bodies
2. **Response Examples**: Add comprehensive response examples for all status codes
3. **Parameter Examples**: Add examples for all query and path parameters
4. **Schema Examples**: Add examples to all schema definitions
5. **Error Examples**: Add realistic error response examples
6. **Documentation**: Enhance descriptions with clear, detailed explanations
7. **Use Cases**: Add operation summaries that explain when to use each endpoint
8. **Business Context**: Add descriptions that explain the business purpose
9. **Integration Guide**: Add descriptions for how endpoints work together
10. **Best Practices**: Add notes about proper usage and common patterns

Ensure all examples are:
- Realistic and consistent with the business domain
- Properly formatted and valid
- Comprehensive covering all scenarios
- Helpful for developers implementing the API

Return the complete, fully documented OpenAPI specification as valid JSON.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    return this.parseJSONResponse(response, 'Fully Documented OpenAPI Specification');
  }

  /**
   * Validate the final OpenAPI specification
   */
  private async validateOpenAPISpecification(spec: OpenAPISpecification): Promise<ValidationStatus> {
    try {
      // Basic JSON validation
      const jsonString = JSON.stringify(spec);
      JSON.parse(jsonString);

      // OpenAPI structure validation
      const hasRequiredFields = spec.openapi && spec.info && spec.paths && spec.components;
      const hasSchemas = spec.components.schemas && Object.keys(spec.components.schemas).length > 0;
      const hasPaths = Object.keys(spec.paths).length > 0;

      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      if (!hasRequiredFields) {
        errors.push({
          type: 'syntax' as const,
          message: 'Missing required OpenAPI fields',
          location: 'root',
          severity: 'error' as const
        });
      }

      if (!hasSchemas) {
        errors.push({
          type: 'contract' as const,
          message: 'No schemas defined in components',
          location: 'components.schemas',
          severity: 'error' as const
        });
      }

      if (!hasPaths) {
        errors.push({
          type: 'contract' as const,
          message: 'No API paths defined',
          location: 'paths',
          severity: 'error' as const
        });
      }

      return {
        syntaxValid: errors.length === 0,
        typeValid: true,
        contractCompliant: errors.length === 0,
        securityCompliant: true,
        errors,
        warnings
      };

    } catch (error) {
      return {
        syntaxValid: false,
        typeValid: false,
        contractCompliant: false,
        securityCompliant: false,
        errors: [{
          type: 'syntax',
          message: `JSON parsing error: ${error instanceof Error ? error.message : String(error)}`,
          location: 'root',
          severity: 'error'
        }],
        warnings: []
      };
    }
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(response: string, context: string): any {
    try {
      // Clean the response
      let cleaned = response.trim();

      // Remove markdown code blocks if present
      cleaned = cleaned.replace(/^```json\s*/gm, '');
      cleaned = cleaned.replace(/^```\s*/gm, '');
      cleaned = cleaned.replace(/\s*```$/gm, '');

      // Remove any leading/trailing text that's not JSON
      const jsonStart = cleaned.indexOf('{');
      const jsonEnd = cleaned.lastIndexOf('}');

      if (jsonStart !== -1 && jsonEnd !== -1) {
        cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
      }

      // Try to fix common JSON issues
      cleaned = this.fixCommonJSONIssues(cleaned);

      return JSON.parse(cleaned);
    } catch (error) {
      this.logger.error(`Failed to parse JSON response for ${context}`, {
        error: error instanceof Error ? error.message : String(error),
        responseLength: response.length,
        responsePreview: response.substring(0, 200)
      });

      // Return a fallback structure for API Requirements Analysis
      if (context === 'API Requirements Analysis') {
        return this.getFallbackAPIAnalysis();
      }

      throw new Error(`Invalid JSON response for ${context}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fix common JSON formatting issues
   */
  private fixCommonJSONIssues(json: string): string {
    // Remove trailing commas
    json = json.replace(/,(\s*[}\]])/g, '$1');

    // Fix missing commas between objects
    json = json.replace(/}(\s*){/g, '},$1{');

    // Fix missing commas between array elements
    json = json.replace(/](\s*)\[/g, '],$1[');

    // Remove any incomplete trailing objects/arrays
    const openBraces = (json.match(/{/g) || []).length;
    const closeBraces = (json.match(/}/g) || []).length;

    if (openBraces > closeBraces) {
      // Add missing closing braces
      json += '}'.repeat(openBraces - closeBraces);
    }

    return json;
  }

  /**
   * Get fallback API analysis structure
   */
  private getFallbackAPIAnalysis(): any {
    return {
      entities: [
        {
          name: "User",
          description: "User entity for authentication and profile management",
          properties: [
            { name: "id", type: "string", required: true, description: "Unique identifier" },
            { name: "email", type: "string", required: true, description: "User email" },
            { name: "name", type: "string", required: true, description: "User name" }
          ],
          relationships: []
        },
        {
          name: "Product",
          description: "Product entity for catalog management",
          properties: [
            { name: "id", type: "string", required: true, description: "Unique identifier" },
            { name: "name", type: "string", required: true, description: "Product name" },
            { name: "price", type: "number", required: true, description: "Product price" }
          ],
          relationships: []
        }
      ],
      endpoints: [
        {
          path: "/api/auth/login",
          method: "POST",
          summary: "User login",
          description: "Authenticate user and return JWT token"
        },
        {
          path: "/api/products",
          method: "GET",
          summary: "Get products",
          description: "Retrieve product catalog"
        }
      ],
      authentication: {
        type: "jwt",
        description: "JWT-based authentication"
      },
      businessRules: []
    };
  }
}
