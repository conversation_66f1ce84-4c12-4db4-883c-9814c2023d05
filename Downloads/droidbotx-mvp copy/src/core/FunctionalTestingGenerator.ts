/**
 * Functional Testing Generator
 * 
 * Generates comprehensive end-to-end tests that validate actual functionality
 * and integration between database, backend, and frontend layers.
 */

import { Logger } from './Logger';
import { LLMProviderSystem } from './LLMProviderSystem';
import { 
  ApplicationRequirements, 
  GeneratedArtifact, 
  OpenAPISpecification, 
  DatabaseSchema,
  ValidationStatus,
  ContractCompliance
} from './ContractFirstLLMGenerator';

export interface TestSuite {
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'e2e' | 'api' | 'database' | 'security' | 'performance';
  tests: TestCase[];
  setup: string[];
  teardown: string[];
  dependencies: string[];
}

export interface TestCase {
  name: string;
  description: string;
  category: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  steps: TestStep[];
  assertions: TestAssertion[];
  expectedResult: string;
  timeout: number;
  retries: number;
}

export interface TestStep {
  action: string;
  description: string;
  data?: any;
  endpoint?: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  selector?: string;
  value?: any;
  waitFor?: string;
}

export interface TestAssertion {
  type: 'equals' | 'contains' | 'exists' | 'status' | 'type' | 'length' | 'custom';
  target: string;
  expected: any;
  message: string;
}

export interface TestConfiguration {
  framework: 'jest' | 'mocha' | 'cypress' | 'playwright' | 'supertest';
  environment: 'development' | 'testing' | 'staging' | 'production';
  baseUrl: string;
  databaseUrl: string;
  timeout: number;
  retries: number;
  parallel: boolean;
  coverage: boolean;
  reporters: string[];
}

export class FunctionalTestingGenerator {
  private logger: Logger;
  private llmProvider: LLMProviderSystem;

  constructor() {
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
  }

  /**
   * Generate comprehensive functional tests for the application
   */
  public async generateFunctionalTests(
    contractArtifact: GeneratedArtifact,
    databaseArtifact: GeneratedArtifact,
    backendArtifacts: GeneratedArtifact[],
    frontendArtifacts: GeneratedArtifact[],
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    this.logger.info('Generating comprehensive functional tests', {
      sessionId,
      applicationName: requirements.name
    });

    try {
      const openApiSpec: OpenAPISpecification = JSON.parse(contractArtifact.content);
      const databaseData = JSON.parse(databaseArtifact.content);
      const databaseSchema: DatabaseSchema = databaseData.schema;

      const artifacts: GeneratedArtifact[] = [];

      // Step 1: Generate API integration tests
      const apiTestArtifacts = await this.generateAPITests(openApiSpec, requirements, sessionId);
      artifacts.push(...apiTestArtifacts);

      // Step 2: Generate database tests
      const databaseTestArtifacts = await this.generateDatabaseTests(databaseSchema, openApiSpec, requirements, sessionId);
      artifacts.push(...databaseTestArtifacts);

      // Step 3: Generate end-to-end tests
      const e2eTestArtifacts = await this.generateE2ETests(openApiSpec, frontendArtifacts, requirements, sessionId);
      artifacts.push(...e2eTestArtifacts);

      // Step 4: Generate security tests
      const securityTestArtifacts = await this.generateSecurityTests(openApiSpec, requirements, sessionId);
      artifacts.push(...securityTestArtifacts);

      // Step 5: Generate performance tests
      const performanceTestArtifacts = await this.generatePerformanceTests(openApiSpec, requirements, sessionId);
      artifacts.push(...performanceTestArtifacts);

      // Step 6: Generate integration tests
      const integrationTestArtifacts = await this.generateIntegrationTests(
        openApiSpec, 
        databaseSchema, 
        backendArtifacts, 
        frontendArtifacts, 
        requirements, 
        sessionId
      );
      artifacts.push(...integrationTestArtifacts);

      // Step 7: Generate test configuration and setup
      const configArtifacts = await this.generateTestConfiguration(requirements, sessionId);
      artifacts.push(...configArtifacts);

      // Step 8: Generate test utilities and helpers
      const utilityArtifacts = await this.generateTestUtilities(openApiSpec, requirements, sessionId);
      artifacts.push(...utilityArtifacts);

      // Step 9: Generate test data and fixtures
      const dataArtifacts = await this.generateTestData(databaseSchema, openApiSpec, requirements, sessionId);
      artifacts.push(...dataArtifacts);

      // Step 10: Generate test documentation
      const docArtifacts = await this.generateTestDocumentation(artifacts, requirements, sessionId);
      artifacts.push(...docArtifacts);

      this.logger.info('Functional tests generation completed', {
        sessionId,
        artifactCount: artifacts.length,
        testSuites: artifacts.filter(a => a.name.includes('test')).length
      });

      return artifacts;

    } catch (error) {
      this.logger.error('Functional tests generation failed', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Generate API integration tests
   */
  private async generateAPITests(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a senior QA engineer specializing in API testing. Generate comprehensive API integration tests that validate all endpoints from the OpenAPI specification.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Framework: ${requirements.targetFramework}

Generate comprehensive API tests including:

1. **Endpoint Tests**: Test every endpoint defined in the OpenAPI spec
2. **CRUD Operations**: Complete create, read, update, delete workflows
3. **Authentication Tests**: Login, logout, token validation, refresh
4. **Authorization Tests**: Role-based access control validation
5. **Input Validation**: Test all validation rules and constraints
6. **Error Handling**: Test all error scenarios and status codes
7. **Data Integrity**: Verify data consistency across operations
8. **Business Logic**: Test business rules and workflows
9. **Edge Cases**: Boundary conditions and edge cases
10. **Performance**: Response time and load testing

Requirements:
- Use Jest and Supertest for API testing
- Include proper test setup and teardown
- Add comprehensive assertions for all responses
- Test both success and failure scenarios
- Include proper test data management
- Add detailed test descriptions and comments
- Use TypeScript with strict typing
- Include proper error handling and logging

Generate test files:
[
  {
    "fileName": "tests/api/auth.test.ts",
    "content": "// Authentication API tests"
  },
  {
    "fileName": "tests/api/entities.test.ts", 
    "content": "// Entity CRUD API tests"
  }
]

Ensure all tests validate actual functionality and integration with the database.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'API Tests');
    
    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `api-test-${file.fileName.split('/').pop()?.replace('.test.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate database tests
   */
  private async generateDatabaseTests(
    databaseSchema: DatabaseSchema,
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a database testing expert. Generate comprehensive database tests that validate schema, relationships, and data integrity.

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Type: ${requirements.databaseType}

Generate comprehensive database tests including:

1. **Schema Validation**: Verify all tables, columns, and constraints exist
2. **Relationship Tests**: Test foreign key relationships and cascades
3. **Data Integrity**: Test constraints, validations, and business rules
4. **CRUD Operations**: Test basic database operations
5. **Transaction Tests**: Test transaction handling and rollbacks
6. **Index Performance**: Verify indexes are working correctly
7. **Migration Tests**: Test database migrations and rollbacks
8. **Seed Data Tests**: Test initial data loading
9. **Backup/Restore**: Test backup and restore procedures
10. **Connection Tests**: Test connection pooling and timeouts

Requirements:
- Use Jest with database testing utilities
- Include proper database setup and cleanup
- Test with real database connections
- Add comprehensive assertions for all operations
- Include transaction testing
- Test both success and failure scenarios
- Use TypeScript with strict typing
- Include proper error handling

Generate database test files:
[
  {
    "fileName": "tests/database/schema.test.ts",
    "content": "// Database schema validation tests"
  },
  {
    "fileName": "tests/database/relationships.test.ts",
    "content": "// Database relationship tests"
  }
]

Ensure all tests validate actual database functionality and data integrity.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'Database Tests');
    
    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `database-test-${file.fileName.split('/').pop()?.replace('.test.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate end-to-end tests
   */
  private async generateE2ETests(
    openApiSpec: OpenAPISpecification,
    frontendArtifacts: GeneratedArtifact[],
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are an end-to-end testing expert. Generate comprehensive E2E tests that validate complete user workflows.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Features: ${requirements.features.join(', ')}
Framework: ${requirements.targetFramework}

Generate comprehensive E2E tests including:

1. **User Authentication**: Complete login/logout workflows
2. **User Registration**: Account creation and verification
3. **Core Workflows**: Main business process flows
4. **Form Interactions**: All form submissions and validations
5. **Navigation Tests**: Page routing and navigation
6. **Data Persistence**: Data saving and retrieval across sessions
7. **Error Scenarios**: Error handling and recovery
8. **Mobile Responsiveness**: Mobile device testing
9. **Cross-Browser**: Browser compatibility testing
10. **Accessibility**: WCAG compliance testing

Requirements:
- Use Playwright for E2E testing
- Include proper page object models
- Add comprehensive assertions for UI elements
- Test complete user journeys
- Include proper test data management
- Add screenshot and video capture on failures
- Use TypeScript with strict typing
- Include proper error handling and retries

Generate E2E test files:
[
  {
    "fileName": "tests/e2e/auth.spec.ts",
    "content": "// Authentication E2E tests"
  },
  {
    "fileName": "tests/e2e/workflows.spec.ts",
    "content": "// Core workflow E2E tests"
  }
]

Ensure all tests validate actual user interactions and complete workflows.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'E2E Tests');
    
    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `e2e-test-${file.fileName.split('/').pop()?.replace('.spec.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate security tests
   */
  private async generateSecurityTests(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a security testing expert. Generate comprehensive security tests that validate application security measures.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Framework: ${requirements.targetFramework}

Generate comprehensive security tests including:

1. **Authentication Security**: JWT token validation, expiration, refresh
2. **Authorization Tests**: Role-based access control, permission checks
3. **Input Validation**: SQL injection, XSS, CSRF protection
4. **API Security**: Rate limiting, CORS, security headers
5. **Data Protection**: Encryption, sensitive data handling
6. **Session Management**: Session security, timeout, hijacking
7. **Password Security**: Hashing, complexity, brute force protection
8. **File Upload Security**: File type validation, size limits
9. **Error Handling**: Information disclosure prevention
10. **Vulnerability Scanning**: Common security vulnerabilities

Requirements:
- Use Jest with security testing utilities
- Include OWASP Top 10 vulnerability tests
- Add penetration testing scenarios
- Test both authenticated and unauthenticated access
- Include proper security assertions
- Test security headers and configurations
- Use TypeScript with strict typing
- Include detailed security test documentation

Generate security test files:
[
  {
    "fileName": "tests/security/auth.test.ts",
    "content": "// Authentication security tests"
  },
  {
    "fileName": "tests/security/vulnerabilities.test.ts",
    "content": "// Vulnerability security tests"
  }
]

Ensure all tests validate actual security measures and protection mechanisms.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'Security Tests');

    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `security-test-${file.fileName.split('/').pop()?.replace('.test.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate performance tests
   */
  private async generatePerformanceTests(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a performance testing expert. Generate comprehensive performance tests that validate application performance and scalability.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Framework: ${requirements.targetFramework}

Generate comprehensive performance tests including:

1. **Load Testing**: Normal load capacity testing
2. **Stress Testing**: Breaking point and recovery testing
3. **Spike Testing**: Sudden load increase handling
4. **Volume Testing**: Large data set handling
5. **Endurance Testing**: Extended period performance
6. **API Performance**: Response time and throughput
7. **Database Performance**: Query optimization and indexing
8. **Memory Testing**: Memory usage and leak detection
9. **Concurrency Testing**: Concurrent user handling
10. **Resource Monitoring**: CPU, memory, disk usage

Requirements:
- Use Artillery or K6 for load testing
- Include realistic user scenarios
- Add comprehensive performance metrics
- Test all critical API endpoints
- Include database performance testing
- Add memory and resource monitoring
- Use TypeScript with strict typing
- Include performance benchmarks and thresholds

Generate performance test files:
[
  {
    "fileName": "tests/performance/load.test.ts",
    "content": "// Load testing scenarios"
  },
  {
    "fileName": "tests/performance/api.test.ts",
    "content": "// API performance tests"
  }
]

Ensure all tests validate actual performance characteristics and scalability.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'Performance Tests');

    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `performance-test-${file.fileName.split('/').pop()?.replace('.test.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate integration tests
   */
  private async generateIntegrationTests(
    openApiSpec: OpenAPISpecification,
    databaseSchema: DatabaseSchema,
    backendArtifacts: GeneratedArtifact[],
    frontendArtifacts: GeneratedArtifact[],
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are an integration testing expert. Generate comprehensive integration tests that validate component interactions.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

Application: ${requirements.name}
Framework: ${requirements.targetFramework}

Generate comprehensive integration tests including:

1. **API-Database Integration**: API operations with database persistence
2. **Frontend-Backend Integration**: UI interactions with API calls
3. **Service Integration**: Inter-service communication testing
4. **External API Integration**: Third-party service integration
5. **Authentication Integration**: Auth flow across all layers
6. **Data Flow Testing**: End-to-end data flow validation
7. **Error Propagation**: Error handling across layers
8. **Transaction Testing**: Multi-step transaction validation
9. **Cache Integration**: Caching layer validation
10. **Event Integration**: Event-driven architecture testing

Requirements:
- Use Jest with integration testing utilities
- Include real database and API interactions
- Add comprehensive data flow validation
- Test cross-layer error handling
- Include proper test isolation
- Add transaction rollback testing
- Use TypeScript with strict typing
- Include detailed integration scenarios

Generate integration test files:
[
  {
    "fileName": "tests/integration/api-database.test.ts",
    "content": "// API-Database integration tests"
  },
  {
    "fileName": "tests/integration/frontend-backend.test.ts",
    "content": "// Frontend-Backend integration tests"
  }
]

Ensure all tests validate actual component interactions and data flow.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const testFiles = this.parseFileArrayResponse(response, 'Integration Tests');

    return testFiles.map(file => ({
      type: 'tests' as const,
      name: `integration-test-${file.fileName.split('/').pop()?.replace('.test.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate test configuration
   */
  private async generateTestConfiguration(
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a test configuration expert. Generate comprehensive test configuration files for all testing frameworks.

Application: ${requirements.name}
Framework: ${requirements.targetFramework}
Database: ${requirements.databaseType}

Generate comprehensive test configuration including:

1. **Jest Configuration**: Unit and integration test setup
2. **Playwright Configuration**: E2E test configuration
3. **Test Environment**: Environment variables and setup
4. **Database Configuration**: Test database setup and cleanup
5. **Coverage Configuration**: Code coverage reporting
6. **CI/CD Configuration**: Automated testing pipeline
7. **Test Scripts**: NPM scripts for different test types
8. **Mock Configuration**: API and service mocking setup
9. **Reporting Configuration**: Test result reporting
10. **Performance Configuration**: Performance test thresholds

Requirements:
- Include all necessary testing dependencies
- Add proper test environment isolation
- Include comprehensive coverage reporting
- Add parallel test execution configuration
- Include proper test data management
- Add CI/CD pipeline integration
- Use TypeScript configuration
- Include detailed documentation

Generate configuration files:
[
  {
    "fileName": "jest.config.js",
    "content": "// Jest configuration"
  },
  {
    "fileName": "playwright.config.ts",
    "content": "// Playwright configuration"
  },
  {
    "fileName": "tests/setup.ts",
    "content": "// Test setup and teardown"
  }
]

Ensure all configurations support comprehensive testing scenarios.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    const configFiles = this.parseFileArrayResponse(response, 'Test Configuration');

    return configFiles.map(file => ({
      type: 'tests' as const,
      name: `test-config-${file.fileName.split('/').pop()?.replace(/\.(js|ts)$/, '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate test utilities
   */
  private async generateTestUtilities(
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a test utility expert. Generate comprehensive test utilities and helpers for all testing scenarios.

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Framework: ${requirements.targetFramework}

Generate comprehensive test utilities including:

1. **API Test Helpers**: HTTP request utilities and assertions
2. **Database Test Helpers**: Database setup, cleanup, and seeding
3. **Authentication Helpers**: Login, token management, user creation
4. **Mock Utilities**: Service mocking and stubbing helpers
5. **Data Generators**: Test data generation and factories
6. **Assertion Helpers**: Custom assertions and matchers
7. **Setup Utilities**: Test environment setup and teardown
8. **Page Object Models**: E2E testing page objects
9. **Test Fixtures**: Reusable test data and scenarios
10. **Validation Helpers**: Response and data validation utilities

Requirements:
- Use TypeScript with strict typing
- Include comprehensive error handling
- Add proper documentation and examples
- Include reusable utility functions
- Add type-safe test helpers
- Include proper cleanup mechanisms
- Add performance monitoring utilities
- Include debugging and logging helpers

Generate utility files:
[
  {
    "fileName": "tests/utils/api-helpers.ts",
    "content": "// API testing utilities"
  },
  {
    "fileName": "tests/utils/database-helpers.ts",
    "content": "// Database testing utilities"
  },
  {
    "fileName": "tests/utils/auth-helpers.ts",
    "content": "// Authentication testing utilities"
  }
]

Ensure all utilities are reusable and support comprehensive testing scenarios.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const utilityFiles = this.parseFileArrayResponse(response, 'Test Utilities');

    return utilityFiles.map(file => ({
      type: 'tests' as const,
      name: `test-util-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate test data
   */
  private async generateTestData(
    databaseSchema: DatabaseSchema,
    openApiSpec: OpenAPISpecification,
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const prompt = `You are a test data expert. Generate comprehensive test data and fixtures for all testing scenarios.

Database Schema:
${JSON.stringify(databaseSchema, null, 2)}

OpenAPI Specification:
${JSON.stringify(openApiSpec, null, 2)}

Application: ${requirements.name}
Business Domain: ${requirements.businessDomain}

Generate comprehensive test data including:

1. **Seed Data**: Initial database seeding for tests
2. **Test Fixtures**: Predefined test data sets
3. **Data Factories**: Dynamic test data generation
4. **Mock Data**: API response mocking data
5. **Edge Case Data**: Boundary and edge case scenarios
6. **Invalid Data**: Error testing data sets
7. **Performance Data**: Large data sets for performance testing
8. **User Scenarios**: Realistic user data and workflows
9. **Business Data**: Domain-specific test scenarios
10. **Cleanup Data**: Data cleanup and reset utilities

Requirements:
- Generate realistic and diverse test data
- Include both valid and invalid data scenarios
- Add proper data relationships and constraints
- Include edge cases and boundary conditions
- Use TypeScript with strict typing
- Add data generation utilities and factories
- Include proper data cleanup mechanisms
- Add documentation for data usage

Generate test data files:
[
  {
    "fileName": "tests/fixtures/seed-data.ts",
    "content": "// Database seed data for tests"
  },
  {
    "fileName": "tests/fixtures/mock-data.ts",
    "content": "// Mock API response data"
  },
  {
    "fileName": "tests/fixtures/factories.ts",
    "content": "// Test data factories"
  }
]

Ensure all test data supports comprehensive testing scenarios and edge cases.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 8000
    });

    const dataFiles = this.parseFileArrayResponse(response, 'Test Data');

    return dataFiles.map(file => ({
      type: 'tests' as const,
      name: `test-data-${file.fileName.split('/').pop()?.replace('.ts', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: this.extractDependencies(file.content),
      exports: this.extractExports(file.content),
      imports: this.extractImports(file.content),
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Generate test documentation
   */
  private async generateTestDocumentation(
    artifacts: GeneratedArtifact[],
    requirements: ApplicationRequirements,
    sessionId: string
  ): Promise<GeneratedArtifact[]> {
    const testArtifacts = artifacts.filter(a => a.type === 'tests');

    const prompt = `You are a technical documentation expert. Generate comprehensive test documentation for the testing suite.

Application: ${requirements.name}
Test Artifacts: ${testArtifacts.length} files generated
Test Types: API, Database, E2E, Security, Performance, Integration

Generate comprehensive test documentation including:

1. **Test Strategy**: Overall testing approach and methodology
2. **Test Plan**: Detailed test execution plan
3. **Test Cases**: Documentation of all test scenarios
4. **Setup Guide**: How to set up and run tests
5. **Test Data Guide**: Test data management and usage
6. **CI/CD Integration**: Automated testing pipeline documentation
7. **Troubleshooting**: Common issues and solutions
8. **Coverage Reports**: Test coverage analysis and reporting
9. **Performance Benchmarks**: Performance test thresholds and metrics
10. **Maintenance Guide**: Test maintenance and updates

Requirements:
- Include comprehensive setup instructions
- Add detailed test execution procedures
- Include troubleshooting and debugging guides
- Add performance benchmarks and thresholds
- Include CI/CD integration instructions
- Add test maintenance procedures
- Use clear and detailed documentation
- Include examples and code snippets

Generate documentation files:
[
  {
    "fileName": "tests/README.md",
    "content": "// Comprehensive test documentation"
  },
  {
    "fileName": "docs/TESTING_GUIDE.md",
    "content": "// Detailed testing guide"
  }
]

Ensure all documentation is comprehensive and supports effective testing.`;

    const response = await this.llmProvider.generateSingleResponse(prompt, {
      temperature: 0.1,
      maxTokens: 6000
    });

    const docFiles = this.parseFileArrayResponse(response, 'Test Documentation');

    return docFiles.map(file => ({
      type: 'tests' as const,
      name: `test-doc-${file.fileName.split('/').pop()?.replace('.md', '') || 'unknown'}`,
      content: file.content,
      filePath: file.fileName,
      dependencies: [],
      exports: [],
      imports: [],
      metadata: {
        generatedAt: new Date(),
        llmModel: 'qwen/qwen3-coder',
        validationStatus: {
          syntaxValid: true,
          typeValid: true,
          contractCompliant: true,
          securityCompliant: true,
          errors: [],
          warnings: []
        },
        contractCompliance: {
          openApiCompliant: true,
          databaseSchemaCompliant: true,
          typeDefinitionsCompliant: true,
          securityCompliant: true,
          complianceScore: 1.0,
          violations: []
        }
      }
    }));
  }

  /**
   * Parse file array response
   */
  private parseFileArrayResponse(response: string, context: string): Array<{ fileName: string; content: string }> {
    try {
      let cleaned = response.trim();
      
      // Remove markdown code blocks
      cleaned = cleaned.replace(/^```json\s*/gm, '');
      cleaned = cleaned.replace(/^```typescript\s*/gm, '');
      cleaned = cleaned.replace(/^```\s*/gm, '');
      cleaned = cleaned.replace(/\s*```$/gm, '');
      
      // Extract JSON array
      const arrayStart = cleaned.indexOf('[');
      const arrayEnd = cleaned.lastIndexOf(']');
      
      if (arrayStart !== -1 && arrayEnd !== -1) {
        cleaned = cleaned.substring(arrayStart, arrayEnd + 1);
      }

      return JSON.parse(cleaned);
    } catch (error) {
      this.logger.error(`Failed to parse file array response for ${context}`, {
        error: error instanceof Error ? error.message : String(error),
        responsePreview: response.substring(0, 200)
      });
      throw new Error(`Invalid file array response for ${context}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Extract dependencies from code
   */
  private extractDependencies(content: string): string[] {
    const dependencies: string[] = [];
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const dep = match[1];
      if (!dep.startsWith('.') && !dep.startsWith('/')) {
        dependencies.push(dep);
      }
    }
    
    return [...new Set(dependencies)];
  }

  /**
   * Extract exports from code
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(?:class|interface|function|const|let|var)\s+(\w+)/g;
    let match;
    
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    
    return exports;
  }

  /**
   * Extract imports from code
   */
  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        // Named imports
        const namedImports = match[1].split(',').map(imp => imp.trim());
        imports.push(...namedImports);
      } else if (match[2]) {
        // Namespace import
        imports.push(match[2]);
      } else if (match[3]) {
        // Default import
        imports.push(match[3]);
      }
    }
    
    return imports;
  }
}
