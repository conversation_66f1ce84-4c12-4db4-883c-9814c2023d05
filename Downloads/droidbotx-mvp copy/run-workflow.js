#!/usr/bin/env node

const { PlanningAgent } = require('./dist/agents/PlanningAgent');
const { BusinessLogicAgent } = require('./dist/agents/BusinessLogicAgent');
const { CodingAgent } = require('./dist/agents/CodingAgent');
const { DatabaseAgent } = require('./dist/agents/DatabaseAgent');
const { TestingAgent } = require('./dist/agents/TestingAgent');
const { DeploymentAgent } = require('./dist/agents/DeploymentAgent');
const { Logger } = require('./dist/core/Logger');
const fs = require('fs');
const path = require('path');

async function runWorkflow() {
  const logger = Logger.getInstance();
  const projectPath = path.join(__dirname, 'qrXcode');
  
  try {
    // Load minimal input
    const input = JSON.parse(fs.readFileSync(path.join(projectPath, 'input.json'), 'utf8'));
    
    logger.info('🚀 Starting DroidBotX workflow for qrXcode');
    
    // Phase 1: Planning Agent - Generate technical specification
    logger.info('📋 Phase 1: Planning Agent');
    const planningAgent = new PlanningAgent();
    const planningTask = {
      id: 'planning-001',
      type: 'planning',
      requirements: input,
      projectPath
    };
    
    const planningResult = await planningAgent.execute(planningTask);
    logger.info('✅ Planning completed', { 
      success: planningResult.success,
      components: Object.keys(planningResult.data?.components || {}).length 
    });
    
    // Phase 2: Business Logic Agent - Generate APIs and database schema
    logger.info('🏗️ Phase 2: Business Logic Agent');
    const businessLogicAgent = new BusinessLogicAgent();
    const businessTask = {
      id: 'business-001',
      type: 'business-logic',
      technicalSpec: planningResult.data,
      projectPath
    };
    
    const businessResult = await businessLogicAgent.execute(businessTask);
    logger.info('✅ Business logic completed', { 
      success: businessResult.success,
      apis: Object.keys(businessResult.data?.domainAPIs || {}).length,
      tables: businessResult.data?.databaseSchema?.tables?.length || 0
    });
    
    // Phase 3: Database Agent - Set up database with synchronization
    logger.info('🗄️ Phase 3: Database Agent');
    const databaseAgent = new DatabaseAgent();
    const databaseTask = {
      id: 'database-001',
      type: 'database',
      businessLogic: businessResult.data,
      projectPath
    };
    
    const databaseResult = await databaseAgent.execute(databaseTask);
    logger.info('✅ Database setup completed', { 
      success: databaseResult.success,
      migrations: databaseResult.data?.migrations?.length || 0
    });
    
    // Phase 4: Coding Agent - Generate full-stack code
    logger.info('💻 Phase 4: Coding Agent (AI-Driven Generation)');
    const codingAgent = new CodingAgent();
    const codingTask = {
      id: 'coding-001',
      type: 'coding',
      technicalSpec: planningResult.data,
      businessLogic: businessResult.data,
      databaseSetup: databaseResult.data,
      projectPath
    };
    
    const codingResult = await codingAgent.execute(codingTask);
    logger.info('✅ Code generation completed', { 
      success: codingResult.success,
      files: Object.keys(codingResult.data?.files || {}).length
    });
    
    // Phase 5: Testing Agent - Generate comprehensive tests
    logger.info('🧪 Phase 5: Testing Agent');
    const testingAgent = new TestingAgent();
    const testingTask = {
      id: 'testing-001',
      type: 'testing',
      generatedCode: codingResult.data,
      businessLogic: businessResult.data,
      projectPath
    };
    
    const testingResult = await testingAgent.execute(testingTask);
    logger.info('✅ Testing completed', { 
      success: testingResult.success,
      testSuites: testingResult.data?.testSuites?.length || 0,
      coverage: testingResult.data?.coverage || 0
    });
    
    // Phase 6: Deployment Agent - Create production infrastructure
    logger.info('🚀 Phase 6: Deployment Agent');
    const deploymentAgent = new DeploymentAgent();
    const deploymentTask = {
      id: 'deployment-001',
      type: 'deployment',
      generatedCode: codingResult.data,
      testResults: testingResult.data,
      projectPath
    };
    
    const deploymentResult = await deploymentAgent.execute(deploymentTask);
    logger.info('✅ Deployment setup completed', { 
      success: deploymentResult.success,
      containers: deploymentResult.data?.containers?.length || 0
    });
    
    // Generate final report
    const report = {
      project: input.projectName,
      timestamp: new Date().toISOString(),
      phases: {
        planning: planningResult.success,
        businessLogic: businessResult.success,
        database: databaseResult.success,
        coding: codingResult.success,
        testing: testingResult.success,
        deployment: deploymentResult.success
      },
      metrics: {
        components: Object.keys(planningResult.data?.components || {}).length,
        apis: Object.keys(businessResult.data?.domainAPIs || {}).length,
        tables: businessResult.data?.databaseSchema?.tables?.length || 0,
        files: Object.keys(codingResult.data?.files || {}).length,
        testSuites: testingResult.data?.testSuites?.length || 0,
        coverage: testingResult.data?.coverage || 0
      },
      success: [planningResult, businessResult, databaseResult, codingResult, testingResult, deploymentResult]
        .every(result => result.success)
    };
    
    fs.writeFileSync(path.join(projectPath, 'workflow-report.json'), JSON.stringify(report, null, 2));
    
    logger.info('🎉 DroidBotX workflow completed successfully!', {
      success: report.success,
      metrics: report.metrics
    });
    
    console.log('\n📊 Final Report:');
    console.log('================');
    console.log(`Project: ${report.project}`);
    console.log(`Success: ${report.success ? '✅' : '❌'}`);
    console.log(`Components: ${report.metrics.components}`);
    console.log(`APIs: ${report.metrics.apis}`);
    console.log(`Database Tables: ${report.metrics.tables}`);
    console.log(`Generated Files: ${report.metrics.files}`);
    console.log(`Test Suites: ${report.metrics.testSuites}`);
    console.log(`Test Coverage: ${report.metrics.coverage}%`);
    console.log(`\n📁 Project generated at: ${projectPath}`);
    
  } catch (error) {
    logger.error('❌ Workflow failed', { error: error.message });
    console.error('\n❌ Workflow failed:', error.message);
    process.exit(1);
  }
}

runWorkflow();
