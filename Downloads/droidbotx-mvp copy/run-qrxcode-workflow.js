#!/usr/bin/env node

const { Orchestrator } = require('./dist/orchestration/Orchestrator');
const { Logger } = require('./dist/core/Logger');
const fs = require('fs');
const path = require('path');

async function runQRXcodeWorkflow() {
  const logger = Logger.getInstance();
  const projectPath = path.join(__dirname, 'qrXcode');
  
  try {
    logger.info('🚀 Starting DroidBotX workflow for qrXcode application');
    
    // Initialize orchestrator
    const orchestrator = new Orchestrator();
    
    // Load minimal input
    const input = JSON.parse(fs.readFileSync(path.join(projectPath, 'input.json'), 'utf8'));
    
    // Create workflow request
    const workflowRequest = {
      id: 'qrxcode-workflow-001',
      description: input.description,
      requirements: input.requirements,
      projectName: input.projectName,
      projectPath: projectPath,
      timestamp: new Date().toISOString()
    };
    
    logger.info('📋 Starting complete workflow execution', {
      project: workflowRequest.projectName,
      requirements: workflowRequest.requirements.length
    });
    
    // Execute the complete workflow
    const workflowResult = await orchestrator.executeCompleteWorkflow(workflowRequest);
    
    logger.info('🎉 DroidBotX workflow completed!', {
      success: workflowResult.success,
      phases: Object.keys(workflowResult.phaseResults || {}).length,
      files: workflowResult.generatedFiles || 0
    });
    
    // Generate comprehensive report
    const report = {
      project: {
        name: workflowRequest.projectName,
        description: workflowRequest.description,
        generatedAt: new Date().toISOString()
      },
      workflow: {
        success: workflowResult.success,
        phases: workflowResult.phaseResults || {},
        duration: workflowResult.duration || 0
      },
      artifacts: {
        files: workflowResult.generatedFiles || 0,
        components: workflowResult.components || 0,
        tests: workflowResult.tests || 0
      },
      quality: {
        testPassRate: workflowResult.testPassRate || 0,
        codeQuality: workflowResult.codeQuality || 0,
        securityScore: workflowResult.securityScore || 0
      },
      deployment: {
        ready: workflowResult.deploymentReady || false,
        containers: workflowResult.containers || 0,
        manifests: workflowResult.manifests || 0
      }
    };
    
    // Save report
    const reportPath = path.join(projectPath, 'generation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Display results
    console.log('\n🎉 qrXcode Application Generated Successfully!');
    console.log('================================================');
    console.log(`📁 Project Location: ${projectPath}`);
    console.log(`✅ Workflow Success: ${report.workflow.success}`);
    console.log(`📊 Generated Files: ${report.artifacts.files}`);
    console.log(`🧪 Test Pass Rate: ${report.quality.testPassRate}%`);
    console.log(`🔒 Security Score: ${report.quality.securityScore}%`);
    console.log(`🚀 Deployment Ready: ${report.deployment.ready}`);
    console.log(`📋 Full Report: ${reportPath}`);
    
    if (workflowResult.success) {
      console.log('\n🎯 Next Steps:');
      console.log('1. Review generated code in the qrXcode directory');
      console.log('2. Run tests: npm test');
      console.log('3. Start development server: npm run dev');
      console.log('4. Deploy to production: npm run deploy');
    }
    
  } catch (error) {
    logger.error('❌ Workflow execution failed', { error: error.message });
    console.error('\n❌ Workflow failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

runQRXcodeWorkflow();
