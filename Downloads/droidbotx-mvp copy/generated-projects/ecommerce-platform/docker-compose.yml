```yaml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - app-network
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=ecommerce_db
      - DB_USER=postgres
      - DB_PASSWORD=postgres_password
      - JWT_SECRET=ecommerce_jwt_secret
      - JWT_EXPIRES_IN=24h
    depends_on:
      - database
    networks:
      - app-network
    volumes:
      - ./backend:/app
      - /app/node_modules

  database:
    image: postgres:13-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ecommerce_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres_password
    networks:
      - app-network
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
```