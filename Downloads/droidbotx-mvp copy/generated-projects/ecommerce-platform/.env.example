```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_platform
DB_USER=ecommerce_user
DB_PASSWORD=your_secure_password
DB_SSL=false

# JWT Configuration
JWT_ACCESS_SECRET=your_access_token_secret_here
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
SENDGRID_API_KEY=your_sendgrid_api_key

# Port Configuration
PORT=3000
CLIENT_PORT=3001

# Email Configuration
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASSWORD=your_sendgrid_api_key
EMAIL_FROM=<EMAIL>

# Payment Configuration
PAYMENT_PROCESSOR=stripe
TAX_RATE=0.08
SHIPPING_COST=5.99

# Inventory Settings
LOW_STOCK_THRESHOLD=10
MAX_CART_ITEMS=20

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your_session_secret_here
CORS_ORIGIN=http://localhost:3001

# Logging
LOG_LEVEL=info
```