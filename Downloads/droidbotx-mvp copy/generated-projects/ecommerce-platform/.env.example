```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_platform
DB_USER=postgres
DB_PASSWORD=your_password
DB_SSL=false

# JWT Configuration
JWT_ACCESS_SECRET=your_access_token_secret_here
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
STRIPE_SECRET_KEY=sk_test_your_stripe_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# Frontend URL
CLIENT_URL=http://localhost:3000

# Payment Configuration
PAYMENT_PROCESSOR=stripe
TAX_RATE=0.08

# Shipping Configuration
DEFAULT_SHIPPING_COST=5.99
FREE_SHIPPING_THRESHOLD=100.00

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_PROVIDER=sendgrid

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
```