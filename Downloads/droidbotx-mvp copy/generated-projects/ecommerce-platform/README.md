# E-Commerce Platform Documentation

## 1. Project Overview and Purpose

### 1.1 Project Description
The E-Commerce Platform is a comprehensive online shopping solution that enables businesses to create and manage online stores. This platform provides core e-commerce functionality including product management, shopping cart operations, user authentication, and order processing.

### 1.2 Purpose
This platform serves as the foundation for online retail businesses, allowing customers to:
- Browse and search products
- Add items to a shopping cart
- Manage cart contents
- Create user accounts
- Complete purchases

### 1.3 Key Features
- Product catalog management
- Real-time shopping cart functionality
- User authentication and profile management
- Secure payment processing integration
- Order history tracking
- Responsive design for all devices

## 2. Architecture Description

### 2.1 System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌──────────────────┐
│   Frontend      │    │   Backend API    │    │   Database       │
│   (React)       │◄──►│   (Express.js)   │◄──►│   (PostgreSQL)   │
└─────────────────┘    └──────────────────┘    └──────────────────┘
```

### 2.2 Technology Stack
- **Frontend**: React with TypeScript, Redux for state management
- **Backend**: Node.js with Express.js and TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: Swagger/OpenAPI
- **Testing**: Jest for unit tests, Cypress for E2E tests
- **Deployment**: Docker containers with Docker Compose

### 2.3 Core Entities

#### Product
Represents items available for purchase with attributes like name, description, price, and inventory.

#### User
Represents customers with authentication credentials and profile information.

#### ShoppingCart
Manages a collection of items a user intends to purchase.

#### CartItem
Represents a specific product and quantity within a shopping cart.

### 2.4 Folder Structure
```
ecommerce-platform/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API service calls
│   │   ├── store/          # Redux store and reducers
│   │   ├── types/          # TypeScript interfaces
│   │   └── utils/          # Utility functions
│   └── package.json
├── server/                 # Express backend
│   ├── src/
│   │   ├── controllers/    # Request handlers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── config/         # Configuration files
│   ├── tests/              # Unit and integration tests
│   └── package.json
├── database/               # Database schema and migrations
├── docker/                 # Docker configuration
└── docs/                   # Documentation
```

## 3. API Documentation

### 3.1 Authentication

#### POST `/api/auth/register`
Register a new user

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "number",
    "username": "string",
    "email": "string"
  },
  "token": "string"
}
```

#### POST `/api/auth/login`
Authenticate a user

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "number",
    "username": "string",
    "email": "string"
  },
  "token": "string"
}
```

### 3.2 Products

#### GET `/api/products`
Retrieve all products with optional filtering

**Query Parameters:**
- `page` (number): Page number for pagination
- `limit` (number): Number of items per page
- `category` (string): Filter by category
- `search` (string): Search term

**Response:**
```json
{
  "products": [
    {
      "id": "number",
      "name": "string",
      "description": "string",
      "price": "number",
      "imageUrl": "string",
      "category": "string",
      "inStock": "boolean"
    }
  ],
  "pagination": {
    "page": "number",
    "limit": "number",
    "total": "number"
  }
}
```

#### GET `/api/products/:id`
Retrieve a specific product by ID

**Response:**
```json
{
  "id": "number",
  "name": "string",
  "description": "string",
  "price": "number",
  "imageUrl": "string",
  "category": "string",
  "inStock": "boolean",
  "stockQuantity": "number"
}
```

### 3.3 Shopping Cart

#### GET `/api/cart`
Retrieve current user's shopping cart

**Headers:**
- Authorization: Bearer `<token>`

**Response:**
```json
{
  "id": "number",
  "userId": "number",
  "items": [
    {
      "id": "number",
      "productId": "number",
      "quantity": "number",
      "product": {
        "id": "number",
        "name": "string",
        "price": "number",
        "imageUrl": "string"
      }
    }
  ],
  "total": "number"
}
```

#### POST `/api/cart/items`
Add item to cart

**Headers:**
- Authorization: Bearer `<token>`

**Request Body:**
```json
{
  "productId": "number",
  "quantity": "number"
}
```

**Response:**
```json
{
  "message": "Item added to cart",
  "cartItem": {
    "id": "number",
    "productId": "number",
    "quantity": "number"
  }
}
```

#### PUT `/api/cart/items/:id`
Update item quantity in cart

**Headers:**
- Authorization: Bearer `<token>`

**Request Body:**
```json
{
  "quantity": "number"
}
```

**Response:**
```json
{
  "message": "Cart item updated",
  "cartItem": {
    "id": "number",
    "productId": "number",
    "quantity": "number"
  }
}
```

#### DELETE `/api/cart/items/:id`
Remove item from cart

**Headers:**
- Authorization: Bearer `<token>`

**Response:**
```json
{
  "message": "Item removed from cart"
}
```

## 4. Setup and Installation Instructions

### 4.1 Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- Docker (optional, for containerized deployment)
- npm or yarn package manager

### 4.2 Environment Setup

#### Backend Environment Variables
Create a `.env` file in the `server/` directory:
```env
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_db
DB_USER=your_username
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h
```

#### Frontend Environment Variables
Create a `.env` file in the `client/` directory:
```env
REACT_APP_API_URL=http://localhost:3000/api
```

### 4.3 Database Setup
1. Create a PostgreSQL database:
```sql
CREATE DATABASE ecommerce_db;
```

2. Run migrations:
```bash
cd server
npm run migrate
```

### 4.4 Installation Steps

#### Backend Setup
```bash
cd server
npm install
npm run build
npm start
```

#### Frontend Setup
```bash
cd client
npm install
npm start
```

### 4.5 Docker Deployment (Optional)
```bash
docker-compose up --build
```

## 5. Usage Examples

### 5.1 User Registration and Authentication
```javascript
// Register a new user
const registerUser = async (userData) => {
  try {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });
    
    const data = await response.json();
    localStorage.setItem('token', data.token);
    return data.user;
  } catch (error) {
    console.error('Registration failed:', error);
  }
};

// Login
const loginUser = async (credentials) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    localStorage.setItem('token', data.token);
    return data.user;
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### 5.2 Product Management
```javascript
// Fetch products
const fetchProducts = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams(filters).toString();
    const response = await fetch(`/api/products?${queryParams}`);
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch products:', error);
  }
};

// Get product details
const getProduct = async (productId) => {
  try {
    const response = await fetch(`/api/products/${productId}`);
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch product:', error);
  }
};
```

### 5.3 Shopping Cart Operations
```javascript
// Get user's cart
const getCart = async () => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/cart', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch cart:', error);
  }
};

// Add item to cart
const addToCart = async (productId, quantity) => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/cart/items', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ productId, quantity })
    });
    return await response.json();
  } catch (error) {
    console.error('Failed to add to cart:', error);
  }
};

// Update cart item
const updateCartItem = async (itemId, quantity) => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/cart/items/${itemId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ quantity })
    });
    return await response.json();
  } catch (error) {
    console.error('Failed to update cart item:', error);
  }
};

// Remove item from cart
const removeFromCart = async (itemId) => {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/cart/items/${itemId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return await response.json();
  } catch (error) {
    console.error('Failed to remove from cart:', error);
  }
};
```

## 6. Development Guidelines

### 6.1 Code Style and Standards
- Follow TypeScript strict mode
- Use ESLint and Prettier for code formatting
- Implement proper error handling with try/catch blocks
- Write meaningful commit messages following conventional commits
- Use meaningful variable and function names

### 6.2 API Design Principles
- Use RESTful API design patterns
- Implement proper HTTP status codes
- Version API endpoints (e.g., `/api/v1/`)
- Include comprehensive API documentation
- Implement rate limiting for public endpoints

### 6.3 Security Best Practices
- Never expose sensitive information in client-side code
- Use environment variables for configuration
- Implement proper input validation and sanitization
- Use HTTPS in production
- Implement proper authentication and authorization
- Regularly update dependencies to address security vulnerabilities

### 6.4 Testing Guidelines
- Write unit tests for all business logic
- Implement integration tests for API endpoints
- Use end-to-end tests for critical user flows
- Maintain test coverage above 80%
- Run tests in CI/CD pipeline

### 6.5 Performance Optimization
- Implement database indexing for frequently queried fields
- Use pagination for large data sets
- Implement caching for frequently accessed data
- Optimize database queries
- Use lazy loading for images and components

### 6.6 Deployment Considerations
- Use environment-specific configuration
- Implement proper logging and monitoring
- Set up automated deployment pipelines
- Implement database backup strategies
- Use load balancing for high-traffic scenarios

### 6.7 Database Design Principles
- Use proper data types for all fields
- Implement foreign key constraints
- Use database normalization where appropriate
- Create indexes for frequently queried columns
- Implement proper data validation at the database level

This documentation provides a comprehensive overview of the E-Commerce Platform. For detailed implementation specifics, refer to the codebase and inline documentation within each component.