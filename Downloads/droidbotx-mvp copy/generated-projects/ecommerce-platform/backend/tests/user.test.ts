









import { UserController } from '../src/controllers/UserController.js';
import { NotFoundError } from '../src/errors/NotFoundError.js';
import { UnauthorizedError } from '../src/errors/UnauthorizedError.js';
import { ValidationError } from '../src/errors/ValidationError.js';
import { AuthMiddleware } from '../src/middleware/auth.js';
import { User, UserRole } from '../src/models/User.js';
import { UserService } from '../src/services/UserService.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a User entity. Since the application type is undefined, I'll create a generic user management system with common operations and business rules.


// __tests__/user.test.ts









// Mock dependencies
jest.mock('../src/services/UserService');
jest.mock('../src/middleware/auth');

const mockUserService = UserService as jest.Mocked<typeof UserService>;
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;

describe('User Tests', () => {
  let app: express.Application;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    
    // Mock auth middleware
    mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => next());
    mockAuthMiddleware.authorize.mockImplementation((roles) => (req, res, next) => next());
    
    // Set up routes
    const userController = new UserController();
    app.get('/api/users', mockAuthMiddleware.authenticate, userController.getAllUsers);
    app.get('/api/users/:id', mockAuthMiddleware.authenticate, userController.getUserById);
    app.post('/api/users', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([UserRole.ADMIN]), userController.createUser);
    app.put('/api/users/:id', mockAuthMiddleware.authenticate, userController.updateUser);
    app.delete('/api/users/:id', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([UserRole.ADMIN]), userController.deleteUser);
    app.post('/api/users/:id/activate', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([UserRole.ADMIN]), userController.activateUser);
  });

  describe('User Model Validation', () => {
    it('should validate a valid user', () => {
      const userData = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const user = new User(userData);
      expect(user.validate()).toBe(true);
    });

    it('should reject invalid email', () => {
      const userData = {
        email: 'invalid-email',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER
      };
      
      const user = new User(userData);
      expect(() => user.validate()).toThrow(ValidationError);
    });

    it('should reject missing required fields', () => {
      const userData = {
        email: '<EMAIL>'
        // Missing firstName, lastName
      };
      
      const user = new User(userData);
      expect(() => user.validate()).toThrow(ValidationError);
    });

    it('should reject invalid role', () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'INVALID_ROLE' as UserRole
      };
      
      const user = new User(userData);
      expect(() => user.validate()).toThrow(ValidationError);
    });
  });

  describe('User Service Layer', () => {
    let userService: UserService;

    beforeEach(() => {
      userService = new UserService();
    });

    it('should create a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: UserRole.USER
      };
      
      const expectedUser = new User({
        id: 1,
        ...userData,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      mockUserService.prototype.createUser.mockResolvedValue(expectedUser);
      
      const result = await userService.createUser(userData);
      expect(result).toEqual(expectedUser);
      expect(mockUserService.prototype.createUser).toHaveBeenCalledWith(userData);
    });

    it('should get all users', async () => {
      const users = [;
        new User({
          id: 1,
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.USER,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }),
        new User({
          id: 2,
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.ADMIN,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        })
      ];
      
      mockUserService.prototype.getAllUsers.mockResolvedValue(users);
      
      const result = await userService.getAllUsers();
      expect(result).toEqual(users);
      expect(mockUserService.prototype.getAllUsers).toHaveBeenCalled();
    });

    it('should get user by ID', async () => {
      const user = new User({
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      mockUserService.prototype.getUserById.mockResolvedValue(user);
      
      const result = await userService.getUserById(1);
      expect(result).toEqual(user);
      expect(mockUserService.prototype.getUserById).toHaveBeenCalledWith(1);
    });

    it('should throw NotFoundError when user does not exist', async () => {
      mockUserService.prototype.getUserById.mockRejectedValue(new NotFoundError('User not found'));
      
      await expect(userService.getUserById(999)).rejects.toThrow(NotFoundError);
    });

    it('should update user', async () => {
      const updateData = {
        firstName: 'Updated Name',
        lastName: 'Updated Last Name'
      };
      
      const updatedUser = new User({
        id: 1,
        email: '<EMAIL>',
        firstName: 'Updated Name',
        lastName: 'Updated Last Name',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      mockUserService.prototype.updateUser.mockResolvedValue(updatedUser);
      
      const result = await userService.updateUser(1, updateData);
      expect(result).toEqual(updatedUser);
      expect(mockUserService.prototype.updateUser).toHaveBeenCalledWith(1, updateData);
    });

    it('should delete user', async () => {
      mockUserService.prototype.deleteUser.mockResolvedValue(true);
      
      const result = await userService.deleteUser(1);
      expect(result).toBe(true);
      expect(mockUserService.prototype.deleteUser).toHaveBeenCalledWith(1);
    });

    it('should activate user', async () => {
      const activatedUser = new User({
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      mockUserService.prototype.activateUser.mockResolvedValue(activatedUser);
      
      const result = await userService.activateUser(1);
      expect(result).toEqual(activatedUser);
      expect(result.isActive).toBe(true);
      expect(mockUserService.prototype.activateUser).toHaveBeenCalledWith(1);
    });
  });

  describe('User API Endpoints', () => {
    it('should get all users', async () => {
      const users = [;
        {
          id: 1,
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.USER,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 2,
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.ADMIN,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      
      mockUserService.prototype.getAllUsers.mockResolvedValue(users.map(u => new User(u)));
      
      const response = await request(app);
        .get('/api/users')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);
      
      expect(response.body).toEqual(users);
    });

    it('should get user by ID', async () => {
      const user = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockUserService.prototype.getUserById.mockResolvedValue(new User(user));
      
      const response = await request(app);
        .get('/api/users/1')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);
      
      expect(response.body).toEqual(user);
    });

    it('should return 404 when user not found', async () => {
      mockUserService.prototype.getUserById.mockRejectedValue(new NotFoundError('User not found'));
      
      const response = await request(app);
        .get('/api/users/999')
        .set('Authorization', 'Bearer valid-token')
        .expect(404);
      
      expect(response.body).toEqual({ error: 'User not found' });
    });

    it('should create a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: UserRole.USER
      };
      
      const createdUser = {
        id: 1,
        ...userData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockUserService.prototype.createUser.mockResolvedValue(new User(createdUser));
      
      const response = await request(app);
        .post('/api/users')
        .set('Authorization', 'Bearer valid-token')
        .send(userData)
        .expect(201);
      
      expect(response.body).toEqual(createdUser);
    });

    it('should return 400 for invalid user data', async () => {
      const invalidData = {
        email: 'invalid-email',
        firstName: 'Jane'
        // Missing lastName
      };
      
      const response = await request(app);
        .post('/api/users')
        .set('Authorization', 'Bearer valid-token')
        .send(invalidData)
        .expect(400);
      
      expect(response.body).toHaveProperty('error');
    });

    it('should update user', async () => {
      const updateData = {
        firstName: 'Updated Name'
      };
      
      const updatedUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'Updated Name',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockUserService.prototype.updateUser.mockResolvedValue(new User(updatedUser));
      
      const response = await request(app);
        .put('/api/users/1')
        .set('Authorization', 'Bearer valid-token')
        .send(updateData)
        .expect(200);
      
      expect(response.body).toEqual(updatedUser);
    });

    it('should delete user', async () => {
      mockUserService.prototype.deleteUser.mockResolvedValue(true);
      
      await request(app)
        .delete('/api/users/1')
        .set('Authorization', 'Bearer valid-token')
        .expect(204);
    });

    it('should activate user', async () => {
      const activatedUser = {
        id: 1,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockUserService.prototype.activateUser.mockResolvedValue(new User(activatedUser));
      
      const response = await request(app);
        .post('/api/users/1/activate')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);
      
      expect(response.body).toEqual(activatedUser);
      expect(response.body.isActive).toBe(true);
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject requests without authentication', async () => {
      mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
        return res.status(401).json({ error: 'Unauthorized' });
      });
      
      await request(app)
        .get('/api/users')
        .expect(401);
    });

    it('should reject unauthorized access to admin endpoints', async () => {
      mockAuthMiddleware.authorize.mockImplementation((roles) => (req, res, next) => {
        return res.status(403).json({ error: 'Forbidden' });
      });
      
      await request(app)
        .post('/api/users')
        .set('Authorization', 'Bearer valid-token')
        .send({
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.USER
        })
        .expect(403);
    });

    it('should allow admin users to access protected endpoints', async () => {
      mockAuthMiddleware.authorize.mockImplementation((roles) => (req, res, next) => {
        // Simulate admin user
        (req as any).user = { role: UserRole.ADMIN };
        next();
      });
      
      const userData = {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: UserRole.USER
      };
      
      const createdUser = {
        id: 1,
        ...userData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockUserService.prototype.createUser.mockResolvedValue(new User(createdUser));
      
      await request(app)
        .post('/api/users')
        .set('Authorization', 'Bearer valid-token')
        .send(userData)
        .expect(20