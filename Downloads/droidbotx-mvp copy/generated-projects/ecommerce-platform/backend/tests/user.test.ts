









import { AppDataSource } from '../src/config/database.js';
import { UserController } from '../src/controllers/UserController.js';
import { AuthMiddleware } from '../src/middleware/auth.js';
import { User, UserRole } from '../src/models/User.js';
import { UserService } from '../src/services/UserService.js';
import bcrypt from 'bcrypt';
import express from 'express';
import jwt from 'jsonwebtoken';
import request from 'supertest';

I'll create comprehensive Jest tests for a User entity. Since the application type is undefined, I'll design a generic user management system with common operations and business rules.


// tests/user.test.ts









// Mock dependencies
jest.mock('../src/config/database');
jest.mock('bcrypt');
jest.mock('jsonwebtoken');

const mockUserRepository = {
  findOne: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(),
};

(AppDataSource.getRepository as jest.Mock).mockImplementation((entity) => {
  if (entity === User) return mockUserRepository;
});

const app = express();
app.use(express.json());

// Mock auth middleware
const mockAuthMiddleware = (req: any, res: any, next: any) => {
  req.user = { id: 1, role: UserRole.ADMIN };
  next();
};

// Setup routes
const userController = new UserController();
app.get('/api/users', mockAuthMiddleware, userController.getAllUsers);
app.get('/api/users/:id', mockAuthMiddleware, userController.getUserById);
app.post('/api/users', mockAuthMiddleware, userController.createUser);
app.put('/api/users/:id', mockAuthMiddleware, userController.updateUser);
app.delete('/api/users/:id', mockAuthMiddleware, userController.deleteUser);

describe('User Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Endpoints', () => {
    describe('GET /api/users', () => {
      it('should return all users', async () => {
        const mockUsers = [;
          { id: 1, name: 'John Doe', email: '<EMAIL>', role: UserRole.USER },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: UserRole.ADMIN },
        ];
        
        mockUserRepository.find.mockResolvedValue(mockUsers);

        const response = await request(app).get('/api/users');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
          success: true,
          data: mockUsers
        });
        expect(mockUserRepository.find).toHaveBeenCalled();
      });

      it('should handle error when fetching users fails', async () => {
        mockUserRepository.find.mockRejectedValue(new Error('Database error'));

        const response = await request(app).get('/api/users');
        
        expect(response.status).toBe(500);
        expect(response.body).toEqual({
          success: false,
          message: 'Internal server error'
        });
      });
    });

    describe('GET /api/users/:id', () => {
      it('should return a user by ID', async () => {
        const mockUser = { 
          id: 1, 
          name: 'John Doe', 
          email: '<EMAIL>', 
          role: UserRole.USER 
        };
        
        mockUserRepository.findOne.mockResolvedValue(mockUser);

        const response = await request(app).get('/api/users/1');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
          success: true,
          data: mockUser
        });
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      });

      it('should return 404 when user not found', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        const response = await request(app).get('/api/users/999');
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({
          success: false,
          message: 'User not found'
        });
      });
    });

    describe('POST /api/users', () => {
      it('should create a new user', async () => {
        const userData = {
          name: 'New User',
          email: '<EMAIL>',
          password: 'password123',
          role: UserRole.USER
        };

        const createdUser = { id: 3, ...userData, password: 'hashedPassword' };
        delete createdUser.password;

        mockUserRepository.create.mockReturnValue(createdUser);
        mockUserRepository.save.mockResolvedValue(createdUser);
        (bcrypt.hash as jest.Mock).mockResolvedValue('hashedPassword');

        const response = await request(app);
          .post('/api/users')
          .send(userData);
        
        expect(response.status).toBe(201);
        expect(response.body).toEqual({
          success: true,
          data: { id: 3, name: 'New User', email: '<EMAIL>', role: UserRole.USER }
        });
        expect(bcrypt.hash).toHaveBeenCalledWith('password123', 10);
        expect(mockUserRepository.create).toHaveBeenCalled();
        expect(mockUserRepository.save).toHaveBeenCalled();
      });

      it('should return validation error for invalid data', async () => {
        const invalidData = {
          name: '',
          email: 'invalid-email',
          password: '123'
        };

        const response = await request(app);
          .post('/api/users')
          .send(invalidData);
        
        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
      });
    });

    describe('PUT /api/users/:id', () => {
      it('should update a user', async () => {
        const existingUser = { 
          id: 1, 
          name: 'John Doe', 
          email: '<EMAIL>', 
          role: UserRole.USER 
        };
        
        const updatedData = { name: 'John Smith' };
        const updatedUser = { ...existingUser, ...updatedData };

        mockUserRepository.findOne.mockResolvedValue(existingUser);
        mockUserRepository.save.mockResolvedValue(updatedUser);

        const response = await request(app);
          .put('/api/users/1')
          .send(updatedData);
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
          success: true,
          data: updatedUser
        });
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
        expect(mockUserRepository.save).toHaveBeenCalledWith(updatedUser);
      });

      it('should return 404 when trying to update non-existent user', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        const response = await request(app);
          .put('/api/users/999')
          .send({ name: 'Updated Name' });
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({
          success: false,
          message: 'User not found'
        });
      });
    });

    describe('DELETE /api/users/:id', () => {
      it('should delete a user', async () => {
        const mockUser = { 
          id: 1, 
          name: 'John Doe', 
          email: '<EMAIL>', 
          role: UserRole.USER 
        };
        
        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockUserRepository.delete.mockResolvedValue({ affected: 1 });

        const response = await request(app).delete('/api/users/1');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
          success: true,
          message: 'User deleted successfully'
        });
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
        expect(mockUserRepository.delete).toHaveBeenCalledWith(1);
      });

      it('should return 404 when trying to delete non-existent user', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        const response = await request(app).delete('/api/users/999');
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({
          success: false,
          message: 'User not found'
        });
      });
    });
  });

  describe('Service Layer', () => {
    let userService: UserService;

    beforeEach(() => {
      userService = new UserService();
    });

    describe('getAllUsers', () => {
      it('should return all users', async () => {
        const mockUsers = [;
          { id: 1, name: 'John Doe', email: '<EMAIL>' },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
        ];
        
        mockUserRepository.find.mockResolvedValue(mockUsers);

        const result = await userService.getAllUsers();
        
        expect(result).toEqual(mockUsers);
        expect(mockUserRepository.find).toHaveBeenCalled();
      });
    });

    describe('getUserById', () => {
      it('should return user by ID', async () => {
        const mockUser = { id: 1, name: 'John Doe', email: '<EMAIL>' };
        mockUserRepository.findOne.mockResolvedValue(mockUser);

        const result = await userService.getUserById(1);
        
        expect(result).toEqual(mockUser);
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      });

      it('should throw error when user not found', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        await expect(userService.getUserById(999)).rejects.toThrow('User not found');
      });
    });

    describe('createUser', () => {
      it('should create a new user', async () => {
        const userData = {
          name: 'New User',
          email: '<EMAIL>',
          password: 'password123',
          role: UserRole.USER
        };

        const createdUser = { id: 3, ...userData, password: 'hashedPassword' };
        mockUserRepository.create.mockReturnValue(createdUser);
        mockUserRepository.save.mockResolvedValue(createdUser);
        (bcrypt.hash as jest.Mock).mockResolvedValue('hashedPassword');

        const result = await userService.createUser(userData);
        
        expect(result).toEqual({ id: 3, name: 'New User', email: '<EMAIL>', role: UserRole.USER });
        expect(bcrypt.hash).toHaveBeenCalledWith('password123', 10);
        expect(mockUserRepository.create).toHaveBeenCalled();
        expect(mockUserRepository.save).toHaveBeenCalled();
      });

      it('should throw validation error for invalid email', async () => {
        const userData = {
          name: 'New User',
          email: 'invalid-email',
          password: 'password123'
        };

        await expect(userService.createUser(userData)).rejects.toThrow('Invalid email format');
      });

      it('should throw validation error for weak password', async () => {
        const userData = {
          name: 'New User',
          email: '<EMAIL>',
          password: '123'
        };

        await expect(userService.createUser(userData)).rejects.toThrow('Password must be at least 6 characters');
      });
    });

    describe('updateUser', () => {
      it('should update user data', async () => {
        const existingUser = { 
          id: 1, 
          name: 'John Doe', 
          email: '<EMAIL>', 
          role: UserRole.USER 
        };
        
        const updateData = { name: 'John Smith' };
        const updatedUser = { ...existingUser, ...updateData };

        mockUserRepository.findOne.mockResolvedValue(existingUser);
        mockUserRepository.save.mockResolvedValue(updatedUser);

        const result = await userService.updateUser(1, updateData);
        
        expect(result).toEqual(updatedUser);
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
        expect(mockUserRepository.save).toHaveBeenCalledWith(updatedUser);
      });

      it('should throw error when updating non-existent user', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        await expect(userService.updateUser(999, { name: 'Updated' })).rejects.toThrow('User not found');
      });
    });

    describe('deleteUser', () => {
      it('should delete a user', async () => {
        const mockUser = { id: 1, name: 'John Doe', email: '<EMAIL>' };
        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockUserRepository.delete.mockResolvedValue({ affected: 1 });

        await userService.deleteUser(1);
        
        expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
        expect(mockUserRepository.delete).toHaveBeenCalledWith(1);
      });

      it('should throw error when deleting non-existent user', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        await expect(userService.deleteUser(999)).rejects.toThrow('User not found');
      });
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', () => {
      const user = new User();
      const errors = user.validate();
      
      expect(errors).toContain('Name is required');
      expect(errors).toContain('Email is required');
      expect(errors).toContain('Password is required');
    });

    it('should validate email format', () => {
      const user = new User();
      user.name = 'John Doe';
      user.email = 'invalid-email';
      user.password = 'password123';
      
      const errors = user.validate();
      
      expect(errors).toContain('Invalid email format');
    });

    it('should validate password strength', () => {
      const user = new User();
      user.name = 'John Doe';
      user.email = '<EMAIL>';
      user.password = '123';
      
      const errors = user.validate();
      
      expect(errors).toContain('Password must be at least 6 characters');
    });

    it('should pass validation for valid user data', () => {
      const user = new User();
      user.name = 'John Doe';
      user.email = '<EMAIL>';
      user.password = 'password123';
      
      const errors = user.validate();
      
      expect(errors).toHaveLength(0;