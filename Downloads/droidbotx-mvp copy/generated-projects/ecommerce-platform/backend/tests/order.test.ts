







import { OrderController } from '../src/controllers/orderController.js';
import { AuthMiddleware } from '../src/middleware/authMiddleware.js';
import { OrderModel } from '../src/models/orderModel.js';
import { OrderService } from '../src/services/OrderService';
import { Role } from '../src/types/roles.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for an Order entity. Based on the undefined application type, I'll assume this is an e-commerce system with typical order operations.


// order.test.ts







// Mock dependencies
jest.mock('../src/services/orderService');
jest.mock('../src/middleware/authMiddleware');

const mockOrderService = new OrderService() as jest.Mocked<OrderService>;
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;

// Create Express app for testing
const app = express();
app.use(express.json());

// Mock authentication middleware
mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
  req.user = { id: 1, role: Role.Customer };
  next();
});

mockAuthMiddleware.authorize.mockImplementation((roles) => {
  return (req, res, next) => {
    if (roles.includes(req.user?.role)) {
      next();
    } else {
      res.status(403).json({ message: 'Forbidden' });
    }
  };
});

// Initialize controller with mocked service
const orderController = new OrderController(mockOrderService);

// Register routes
app.get('/orders', mockAuthMiddleware.authenticate, orderController.getAllOrders);
app.get('/orders/:id', mockAuthMiddleware.authenticate, orderController.getOrderById);
app.post('/orders', mockAuthMiddleware.authenticate, orderController.createOrder);
app.put('/orders/:id', mockAuthMiddleware.authenticate, orderController.updateOrder);
app.delete('/orders/:id', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([Role.Admin]), orderController.deleteOrder);
app.post('/orders/:id/complete', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([Role.Admin]), orderController.completeOrder);

describe('Order API Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /orders', () => {
    it('should return all orders for authenticated user', async () => {
      const mockOrders = [;
        { id: 1, userId: 1, status: 'pending', total: 100 },
        { id: 2, userId: 1, status: 'shipped', total: 200 }
      ];
      
      mockOrderService.getAllOrders.mockResolvedValue(mockOrders);

      const response = await request(app);
        .get('/orders')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(mockOrders);
      expect(mockOrderService.getAllOrders).toHaveBeenCalledWith(1);
    });

    it('should return 401 if user is not authenticated', async () => {
      mockAuthMiddleware.authenticate.mockImplementationOnce((req, res, next) => {
        res.status(401).json({ message: 'Unauthorized' });
      });

      await request(app)
        .get('/orders')
        .expect(401);
    });
  });

  describe('GET /orders/:id', () => {
    it('should return order by id for owner', async () => {
      const mockOrder = { id: 1, userId: 1, status: 'pending', total: 100 };
      mockOrderService.getOrderById.mockResolvedValue(mockOrder);

      const response = await request(app);
        .get('/orders/1')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(mockOrder);
      expect(mockOrderService.getOrderById).toHaveBeenCalledWith(1, 1);
    });

    it('should return 404 if order not found', async () => {
      mockOrderService.getOrderById.mockResolvedValue(null);

      await request(app)
        .get('/orders/999')
        .set('Authorization', 'Bearer token')
        .expect(404);
    });

    it('should return 403 if user tries to access another user\'s order', async () => {
      mockOrderService.getOrderById.mockRejectedValue(new Error('Forbidden'));

      await request(app)
        .get('/orders/2')
        .set('Authorization', 'Bearer token')
        .expect(403);
    });
  });

  describe('POST /orders', () => {
    it('should create a new order', async () => {
      const newOrderData = {
        items: [{ productId: 1, quantity: 2 }],
        shippingAddress: '123 Main St'
      };
      
      const createdOrder = {
        id: 1,
        userId: 1,
        status: 'pending',
        total: 50,
        items: newOrderData.items,
        shippingAddress: newOrderData.shippingAddress
      };

      mockOrderService.createOrder.mockResolvedValue(createdOrder);

      const response = await request(app);
        .post('/orders')
        .set('Authorization', 'Bearer token')
        .send(newOrderData)
        .expect(201);

      expect(response.body).toEqual(createdOrder);
      expect(mockOrderService.createOrder).toHaveBeenCalledWith(1, newOrderData);
    });

    it('should return 400 for invalid order data', async () => {
      const invalidOrderData = {
        items: [],
        shippingAddress: ''
      };

      await request(app)
        .post('/orders')
        .set('Authorization', 'Bearer token')
        .send(invalidOrderData)
        .expect(400);
    });
  });

  describe('PUT /orders/:id', () => {
    it('should update an existing order', async () => {
      const updateData = { status: 'shipped' };
      const updatedOrder = { id: 1, userId: 1, status: 'shipped', total: 100 };

      mockOrderService.updateOrder.mockResolvedValue(updatedOrder);

      const response = await request(app);
        .put('/orders/1')
        .set('Authorization', 'Bearer token')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual(updatedOrder);
      expect(mockOrderService.updateOrder).toHaveBeenCalledWith(1, 1, updateData);
    });

    it('should return 400 for invalid update data', async () => {
      const invalidUpdateData = { status: 'invalid-status' };

      await request(app)
        .put('/orders/1')
        .set('Authorization', 'Bearer token')
        .send(invalidUpdateData)
        .expect(400);
    });
  });

  describe('DELETE /orders/:id', () => {
    it('should delete order for admin user', async () => {
      mockAuthMiddleware.authenticate.mockImplementationOnce((req, res, next) => {
        req.user = { id: 1, role: Role.Admin };
        next();
      });
      
      mockOrderService.deleteOrder.mockResolvedValue(true);

      await request(app)
        .delete('/orders/1')
        .set('Authorization', 'Bearer token')
        .expect(204);

      expect(mockOrderService.deleteOrder).toHaveBeenCalledWith(1);
    });

    it('should return 403 for non-admin users', async () => {
      await request(app)
        .delete('/orders/1')
        .set('Authorization', 'Bearer token')
        .expect(403);
    });
  });

  describe('POST /orders/:id/complete', () => {
    it('should complete order for admin user', async () => {
      mockAuthMiddleware.authenticate.mockImplementationOnce((req, res, next) => {
        req.user = { id: 1, role: Role.Admin };
        next();
      });
      
      const completedOrder = { id: 1, userId: 1, status: 'completed', total: 100 };
      mockOrderService.completeOrder.mockResolvedValue(completedOrder);

      const response = await request(app);
        .post('/orders/1/complete')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(completedOrder);
      expect(mockOrderService.completeOrder).toHaveBeenCalledWith(1);
    });

    it('should return 403 for non-admin users', async () => {
      await request(app)
        .post('/orders/1/complete')
        .set('Authorization', 'Bearer token')
        .expect(403);
    });
  });
});

describe('Order Service Tests', () => {
  let orderService: OrderService;

  beforeEach(() => {
    orderService = new OrderService();
    jest.clearAllMocks();
  });

  describe('getAllOrders', () => {
    it('should return all orders for a user', async () => {
      const userId = 1;
      const mockOrders = [;
        { id: 1, userId, status: 'pending', total: 100 },
        { id: 2, userId, status: 'shipped', total: 200 }
      ];

      (OrderModel.findByUserId as jest.Mock).mockResolvedValue(mockOrders);

      const result = await orderService.getAllOrders(userId);
      expect(result).toEqual(mockOrders);
      expect(OrderModel.findByUserId).toHaveBeenCalledWith(userId);
    });
  });

  describe('getOrderById', () => {
    it('should return order if it belongs to user', async () => {
      const orderId = 1;
      const userId = 1;
      const mockOrder = { id: orderId, userId, status: 'pending', total: 100 };

      (OrderModel.findById as jest.Mock).mockResolvedValue(mockOrder);

      const result = await orderService.getOrderById(orderId, userId);
      expect(result).toEqual(mockOrder);
    });

    it('should throw error if order does not belong to user', async () => {
      const orderId = 1;
      const userId = 1;
      const otherUserId = 2;
      const mockOrder = { id: orderId, userId: otherUserId, status: 'pending', total: 100 };

      (OrderModel.findById as jest.Mock).mockResolvedValue(mockOrder);

      await expect(orderService.getOrderById(orderId, userId))
        .rejects
        .toThrow('Forbidden');
    });
  });

  describe('createOrder', () => {
    it('should create a valid order', async () => {
      const userId = 1;
      const orderData = {
        items: [{ productId: 1, quantity: 2 }],
        shippingAddress: '123 Main St'
      };
      
      const createdOrder = {
        id: 1,
        userId,
        status: 'pending',
        total: 50,
        ...orderData
      };

      (OrderModel.create as jest.Mock).mockResolvedValue(createdOrder);

      const result = await orderService.createOrder(userId, orderData);
      expect(result).toEqual(createdOrder);
      expect(OrderModel.create).toHaveBeenCalledWith({
        userId,
        status: 'pending',
        ...orderData
      });
    });

    it('should throw error for invalid order data', async () => {
      const userId = 1;
      const invalidOrderData = {
        items: [],
        shippingAddress: ''
      };

      await expect(orderService.createOrder(userId, invalidOrderData))
        .rejects
        .toThrow('Invalid order data');
    });
  });

  describe('updateOrder', () => {
    it('should update order if it belongs to user', async () => {
      const orderId = 1;
      const userId = 1;
      const updateData = { status: 'shipped' };
      const existingOrder = { id: orderId, userId, status: 'pending', total: 100 };
      const updatedOrder = { ...existingOrder, ...updateData };

      (OrderModel.findById as jest.Mock).mockResolvedValue(existingOrder);
      (OrderModel.update as jest.Mock).mockResolvedValue(updatedOrder);

      const result = await orderService.updateOrder(orderId, userId, updateData);
      expect(result).toEqual(updatedOrder);
    });

    it('should throw error for invalid status update', async () => {
      const orderId = 1;
      const userId = 1;
      const updateData = { status: 'invalid-status' };

      await expect(orderService.updateOrder(orderId, userId, updateData))
        .rejects
        .toThrow('Invalid status');
    });
  });

  describe('deleteOrder', () => {
    it('should delete order', async () => {
      const orderId = 1;
      (OrderModel.delete as jest.Mock).mockResolvedValue(true);

      const result = await orderService.deleteOrder(orderId);
      expect(result).toBe(true);
      expect(OrderModel.delete).toHaveBeenCalledWith(orderId);
    });
  });

  describe('completeOrder', () => {
    it('should complete order', async () => {
      const orderId = 1;
      const existingOrder = { id: orderId, userId: 1, status: 'shipped', total: 100 };
      const completedOrder = { ...existingOrder, status: 'completed' };

      (OrderModel.findById as jest.Mock).mockResolvedValue(existingOrder);
      (OrderModel.update as jest.Mock).mockResolvedValue(completedOrder);

      const result = await orderService.completeOrder(orderId);
      expect(result).toEqual(completedOrder);
      expect(OrderModel.update).toHaveBeenCalledWith(orderId, { status: 'completed' });
    });

    it('should throw error if order is already completed', async () => {
      const orderId = 1;
      const existingOrder = { id: orderId, userId: 1, status: 'completed', total: 100 };

      (OrderModel.findById as jest.Mock).mockResolvedValue(existingOrder);

      await expect(orderService.completeOrder(orderId))
        .rejects
        .toThrow('Order is already completed');
    });
  });
});

describe('Order Model Validation Tests', () => {
  describe('Order Creation Validation', () => {
    it('should validate required fields', () => {
      const invalidOrder = new Order;