







import { ShoppingCartController } from '../src/controllers/shoppingCartController.js';
import { AuthMiddleware } from '../src/middleware/authMiddleware.js';
import { Product } from '../src/models/Product.js';
import { ShoppingCart } from '../src/models/ShoppingCart.js';
import { ShoppingCartService } from '../src/services/ShoppingCartService';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a ShoppingCart in an e-commerce application. Based on the undefined application context, I'll assume standard e-commerce operations and generate appropriate tests.


// shoppingCart.test.ts







// Mock dependencies
jest.mock('../src/services/shoppingCartService');
jest.mock('../src/middleware/authMiddleware');

const mockShoppingCartService = ShoppingCartService as jest.Mocked<typeof ShoppingCartService>;
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;

// Create express app for testing
const app = express();
app.use(express.json());

// Mock authentication middleware
mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
  req.user = { id: 1, role: 'customer' };
  next();
});

mockAuthMiddleware.authorize.mockImplementation((roles) => {
  return (req, res, next) => {
    if (roles.includes(req.user.role)) {
      next();
    } else {
      res.status(403).json({ message: 'Forbidden' });
    }
  };
});

// Setup routes
const shoppingCartController = new ShoppingCartController();
app.get('/api/cart', mockAuthMiddleware.authenticate, shoppingCartController.getCart);
app.post('/api/cart/items', mockAuthMiddleware.authenticate, shoppingCartController.addItem);
app.put('/api/cart/items/:itemId', mockAuthMiddleware.authenticate, shoppingCartController.updateItem);
app.delete('/api/cart/items/:itemId', mockAuthMiddleware.authenticate, shoppingCartController.removeItem);
app.delete('/api/cart', mockAuthMiddleware.authenticate, shoppingCartController.clearCart);
app.post('/api/cart/checkout', mockAuthMiddleware.authenticate, shoppingCartController.checkout);

describe('ShoppingCart API Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/cart', () => {
    it('should return user cart successfully', async () => {
      const mockCart = {
        id: 1,
        userId: 1,
        items: [
          { id: 1, productId: 1, quantity: 2, price: 29.99 },
          { id: 2, productId: 2, quantity: 1, price: 49.99 }
        ],
        total: 109.97
      };

      mockShoppingCartService.prototype.getCart.mockResolvedValue(mockCart);

      const response = await request(app);
        .get('/api/cart')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(mockCart);
      expect(mockShoppingCartService.prototype.getCart).toHaveBeenCalledWith(1);
    });

    it('should return 404 when cart not found', async () => {
      mockShoppingCartService.prototype.getCart.mockRejectedValue(new Error('Cart not found'));

      await request(app)
        .get('/api/cart')
        .set('Authorization', 'Bearer token')
        .expect(404);
    });

    it('should return 500 on service error', async () => {
      mockShoppingCartService.prototype.getCart.mockRejectedValue(new Error('Database error'));

      await request(app)
        .get('/api/cart')
        .set('Authorization', 'Bearer token')
        .expect(500);
    });
  });

  describe('POST /api/cart/items', () => {
    it('should add item to cart successfully', async () => {
      const newItem = { productId: 1, quantity: 2 };
      const updatedCart = {
        id: 1,
        userId: 1,
        items: [{ id: 1, productId: 1, quantity: 2, price: 29.99 }],
        total: 59.98
      };

      mockShoppingCartService.prototype.addItem.mockResolvedValue(updatedCart);

      const response = await request(app);
        .post('/api/cart/items')
        .set('Authorization', 'Bearer token')
        .send(newItem)
        .expect(200);

      expect(response.body).toEqual(updatedCart);
      expect(mockShoppingCartService.prototype.addItem).toHaveBeenCalledWith(1, newItem.productId, newItem.quantity);
    });

    it('should return 400 for invalid input', async () => {
      const invalidItem = { productId: -1, quantity: 0 };

      await request(app)
        .post('/api/cart/items')
        .set('Authorization', 'Bearer token')
        .send(invalidItem)
        .expect(400);
    });

    it('should return 404 when product not found', async () => {
      const newItem = { productId: 999, quantity: 1 };
      mockShoppingCartService.prototype.addItem.mockRejectedValue(new Error('Product not found'));

      await request(app)
        .post('/api/cart/items')
        .set('Authorization', 'Bearer token')
        .send(newItem)
        .expect(404);
    });
  });

  describe('PUT /api/cart/items/:itemId', () => {
    it('should update item quantity successfully', async () => {
      const updatedCart = {
        id: 1,
        userId: 1,
        items: [{ id: 1, productId: 1, quantity: 5, price: 29.99 }],
        total: 149.95
      };

      mockShoppingCartService.prototype.updateItem.mockResolvedValue(updatedCart);

      const response = await request(app);
        .put('/api/cart/items/1')
        .set('Authorization', 'Bearer token')
        .send({ quantity: 5 })
        .expect(200);

      expect(response.body).toEqual(updatedCart);
      expect(mockShoppingCartService.prototype.updateItem).toHaveBeenCalledWith(1, 1, 5);
    });

    it('should return 400 for invalid quantity', async () => {
      await request(app)
        .put('/api/cart/items/1')
        .set('Authorization', 'Bearer token')
        .send({ quantity: -1 })
        .expect(400);
    });

    it('should return 404 when item not found', async () => {
      mockShoppingCartService.prototype.updateItem.mockRejectedValue(new Error('Item not found'));

      await request(app)
        .put('/api/cart/items/999')
        .set('Authorization', 'Bearer token')
        .send({ quantity: 3 })
        .expect(404);
    });
  });

  describe('DELETE /api/cart/items/:itemId', () => {
    it('should remove item from cart successfully', async () => {
      const updatedCart = {
        id: 1,
        userId: 1,
        items: [],
        total: 0
      };

      mockShoppingCartService.prototype.removeItem.mockResolvedValue(updatedCart);

      const response = await request(app);
        .delete('/api/cart/items/1')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(updatedCart);
      expect(mockShoppingCartService.prototype.removeItem).toHaveBeenCalledWith(1, 1);
    });

    it('should return 404 when item not found', async () => {
      mockShoppingCartService.prototype.removeItem.mockRejectedValue(new Error('Item not found'));

      await request(app)
        .delete('/api/cart/items/999')
        .set('Authorization', 'Bearer token')
        .expect(404);
    });
  });

  describe('DELETE /api/cart', () => {
    it('should clear cart successfully', async () => {
      const clearedCart = {
        id: 1,
        userId: 1,
        items: [],
        total: 0
      };

      mockShoppingCartService.prototype.clearCart.mockResolvedValue(clearedCart);

      const response = await request(app);
        .delete('/api/cart')
        .set('Authorization', 'Bearer token')
        .expect(200);

      expect(response.body).toEqual(clearedCart);
      expect(mockShoppingCartService.prototype.clearCart).toHaveBeenCalledWith(1);
    });

    it('should return 500 on service error', async () => {
      mockShoppingCartService.prototype.clearCart.mockRejectedValue(new Error('Clear cart failed'));

      await request(app)
        .delete('/api/cart')
        .set('Authorization', 'Bearer token')
        .expect(500);
    });
  });

  describe('POST /api/cart/checkout', () => {
    it('should process checkout successfully', async () => {
      const checkoutResult = {
        orderId: 123,
        total: 109.97,
        status: 'confirmed'
      };

      mockShoppingCartService.prototype.checkout.mockResolvedValue(checkoutResult);

      const response = await request(app);
        .post('/api/cart/checkout')
        .set('Authorization', 'Bearer token')
        .send({ paymentMethod: 'credit_card' })
        .expect(200);

      expect(response.body).toEqual(checkoutResult);
      expect(mockShoppingCartService.prototype.checkout).toHaveBeenCalledWith(1, 'credit_card');
    });

    it('should return 400 for invalid payment method', async () => {
      await request(app)
        .post('/api/cart/checkout')
        .set('Authorization', 'Bearer token')
        .send({ paymentMethod: 'invalid_method' })
        .expect(400);
    });

    it('should return 400 when cart is empty', async () => {
      mockShoppingCartService.prototype.checkout.mockRejectedValue(new Error('Cart is empty'));

      await request(app)
        .post('/api/cart/checkout')
        .set('Authorization', 'Bearer token')
        .send({ paymentMethod: 'credit_card' })
        .expect(400);
    });
  });
});

describe('ShoppingCart Service Tests', () => {
  let shoppingCartService: ShoppingCartService;

  beforeEach(() => {
    shoppingCartService = new ShoppingCartService();
  });

  describe('getCart', () => {
    it('should return existing cart', async () => {
      const mockCart = new ShoppingCart();
      mockCart.id = 1;
      mockCart.userId = 1;
      mockCart.items = [
        { id: 1, productId: 1, quantity: 2, price: 29.99, cartId: 1 }
      ];
      mockCart.calculateTotal = jest.fn().mockReturnValue(59.98);

      jest.spyOn(ShoppingCart, 'findOne').mockResolvedValue(mockCart);

      const result = await shoppingCartService.getCart(1);
      expect(result).toEqual({
        id: 1,
        userId: 1,
        items: [{ id: 1, productId: 1, quantity: 2, price: 29.99 }],
        total: 59.98
      });
    });

    it('should throw error when cart not found', async () => {
      jest.spyOn(ShoppingCart, 'findOne').mockResolvedValue(null);

      await expect(shoppingCartService.getCart(999)).rejects.toThrow('Cart not found');
    });
  });

  describe('addItem', () => {
    it('should add new item to cart', async () => {
      const mockProduct = new Product();
      mockProduct.id = 1;
      mockProduct.price = 29.99;
      mockProduct.stock = 10;

      const mockCart = new ShoppingCart();
      mockCart.id = 1;
      mockCart.userId = 1;
      mockCart.items = [];
      mockCart.save = jest.fn().mockResolvedValue(mockCart);

      jest.spyOn(ShoppingCart, 'findOne').mockResolvedValue(mockCart);
      jest.spyOn(Product, 'findOne').mockResolvedValue(mockProduct);

      const result = await shoppingCartService.addItem(1, 1, 2);
      expect(result.items).toHaveLength(1);
      expect(result.items[0].quantity).toBe(2);
      expect(result.items[0].price).toBe(29.99);
    });

    it('should update quantity if item already exists', async () => {
      const mockProduct = new Product();
      mockProduct.id = 1;
      mockProduct.price = 29.99;
      mockProduct.stock = 10;

      const mockCart = new ShoppingCart();
      mockCart.id = 1;
      mockCart.userId = 1;
      mockCart.items = [
        { id: 1, productId: 1, quantity: 2, price: 29.99, cartId: 1, save: jest.fn() }
      ];
      mockCart.save = jest.fn().mockResolvedValue(mockCart);

      jest.spyOn(ShoppingCart, 'findOne').mockResolvedValue(mockCart);
      jest.spyOn(Product, 'findOne').mockResolvedValue(mockProduct);

      const result = await shoppingCartService.addItem(1, 1, 3);
      expect(result.items[0].quantity).toBe(5);
    });

    it('should throw error when product not found', async () => {
      jest.spyOn(Product, 'findOne').mockResolvedValue(null);

      await expect(shoppingCartService.addItem(1, 999, 1)).rejects.toThrow('Product not found');
    });

    it('should throw error when insufficient stock', async () => {
      const mockProduct = new Product();
      mockProduct.id = 1;
      mockProduct.price = 29.99;
      mockProduct.stock = 2;

      const mockCart = new ShoppingCart();
      mockCart.id = 1;
      mockCart.userId = 1;
      mockCart.items = [];
      mockCart.save = jest.fn().mockResolvedValue(mockCart);

      jest.spyOn(ShoppingCart, 'findOne').mockResolvedValue(mockCart);
      jest.spyOn(Product, 'findOne').mockResolvedValue(mockProduct);

      await expect(shopping