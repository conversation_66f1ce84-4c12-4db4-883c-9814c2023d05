







import { ShoppingCartController } from '../controllers/shoppingCartController.js';
import { ValidationError } from '../errors/ValidationError.js';
import { authenticateToken, authorizeRole } from '../middleware/auth.js';
import { ShoppingCartService } from '../services/ShoppingCartService';
import { UserRole } from '../types/user.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a ShoppingCart entity. Based on the undefined application type, I'll assume this is for an e-commerce platform with typical shopping cart functionality.


// shoppingCart.test.ts







// Mock dependencies
jest.mock('../services/shoppingCartService');
jest.mock('../middleware/auth');

const mockAuthenticateToken = authenticateToken as jest.MockedFunction<typeof authenticateToken>;
const mockAuthorizeRole = authorizeRole as jest.MockedFunction<typeof authorizeRole>;
const mockShoppingCartService = ShoppingCartService as jest.MockedClass<typeof ShoppingCartService>;

// Create express app for testing
const app = express();
app.use(express.json());

// Mock authentication middleware
mockAuthenticateToken.mockImplementation((req, res, next) => next());
mockAuthorizeRole.mockImplementation((roles) => (req, res, next) => next());

// Initialize controller and routes
const shoppingCartController = new ShoppingCartController(new ShoppingCartService());
app.get('/api/cart', mockAuthenticateToken, shoppingCartController.getCart);
app.post('/api/cart/items', mockAuthenticateToken, shoppingCartController.addItem);
app.put('/api/cart/items/:itemId', mockAuthenticateToken, shoppingCartController.updateItem);
app.delete('/api/cart/items/:itemId', mockAuthenticateToken, shoppingCartController.removeItem);
app.post('/api/cart/checkout', mockAuthenticateToken, shoppingCartController.checkout);

describe('ShoppingCart', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Endpoints', () => {
    describe('GET /api/cart', () => {
      it('should return user cart successfully', async () => {
        const mockCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [
            { id: 'item-1', productId: 'prod-1', quantity: 2, price: 29.99 },
            { id: 'item-2', productId: 'prod-2', quantity: 1, price: 49.99 }
          ],
          total: 109.97,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockShoppingCartService.prototype.getCartByUserId.mockResolvedValue(mockCart);

        const response = await request(app);
          .get('/api/cart')
          .set('Authorization', 'Bearer token123')
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          data: mockCart
        });
        expect(mockShoppingCartService.prototype.getCartByUserId).toHaveBeenCalledWith('user-456');
      });

      it('should return empty cart when user has no items', async () => {
        mockShoppingCartService.prototype.getCartByUserId.mockResolvedValue({
          id: 'cart-123',
          userId: 'user-456',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        const response = await request(app);
          .get('/api/cart')
          .set('Authorization', 'Bearer token123')
          .expect(200);

        expect(response.body.data.items).toHaveLength(0);
        expect(response.body.data.total).toBe(0);
      });
    });

    describe('POST /api/cart/items', () => {
      it('should add item to cart successfully', async () => {
        const newItem = {
          productId: 'prod-123',
          quantity: 2
        };

        const updatedCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [
            { id: 'item-1', productId: 'prod-123', quantity: 2, price: 29.99 }
          ],
          total: 59.98,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockShoppingCartService.prototype.addItemToCart.mockResolvedValue(updatedCart);

        const response = await request(app);
          .post('/api/cart/items')
          .set('Authorization', 'Bearer token123')
          .send(newItem)
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          data: updatedCart
        });
        expect(mockShoppingCartService.prototype.addItemToCart).toHaveBeenCalledWith(;
          'user-456',
          newItem.productId,
          newItem.quantity
        );
      });

      it('should return 400 for invalid input', async () => {
        const invalidItem = {
          productId: '',
          quantity: -1
        };

        const response = await request(app);
          .post('/api/cart/items')
          .set('Authorization', 'Bearer token123')
          .send(invalidItem)
          .expect(400);

        expect(response.body).toEqual({
          success: false,
          error: 'Validation failed',
          details: expect.arrayContaining([
            expect.objectContaining({ field: 'productId' }),
            expect.objectContaining({ field: 'quantity' })
          ])
        });
      });
    });

    describe('PUT /api/cart/items/:itemId', () => {
      it('should update item quantity successfully', async () => {
        const updatedCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [
            { id: 'item-1', productId: 'prod-123', quantity: 5, price: 29.99 }
          ],
          total: 149.95,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockShoppingCartService.prototype.updateItemQuantity.mockResolvedValue(updatedCart);

        const response = await request(app);
          .put('/api/cart/items/item-1')
          .set('Authorization', 'Bearer token123')
          .send({ quantity: 5 })
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          data: updatedCart
        });
        expect(mockShoppingCartService.prototype.updateItemQuantity).toHaveBeenCalledWith(;
          'user-456',
          'item-1',
          5
        );
      });

      it('should return 404 for non-existent item', async () => {
        mockShoppingCartService.prototype.updateItemQuantity.mockRejectedValue(
          new Error('Item not found')
        );

        const response = await request(app);
          .put('/api/cart/items/item-999')
          .set('Authorization', 'Bearer token123')
          .send({ quantity: 3 })
          .expect(404);

        expect(response.body).toEqual({
          success: false,
          error: 'Item not found'
        });
      });
    });

    describe('DELETE /api/cart/items/:itemId', () => {
      it('should remove item from cart successfully', async () => {
        const updatedCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockShoppingCartService.prototype.removeItemFromCart.mockResolvedValue(updatedCart);

        const response = await request(app);
          .delete('/api/cart/items/item-1')
          .set('Authorization', 'Bearer token123')
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          message: 'Item removed from cart'
        });
        expect(mockShoppingCartService.prototype.removeItemFromCart).toHaveBeenCalledWith(;
          'user-456',
          'item-1'
        );
      });
    });

    describe('POST /api/cart/checkout', () => {
      it('should process checkout successfully', async () => {
        const checkoutResult = {
          orderId: 'order-789',
          total: 109.97,
          items: 2
        };

        mockShoppingCartService.prototype.checkout.mockResolvedValue(checkoutResult);

        const response = await request(app);
          .post('/api/cart/checkout')
          .set('Authorization', 'Bearer token123')
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          data: checkoutResult
        });
        expect(mockShoppingCartService.prototype.checkout).toHaveBeenCalledWith('user-456');
      });

      it('should return 400 when cart is empty', async () => {
        mockShoppingCartService.prototype.checkout.mockRejectedValue(
          new ValidationError('Cannot checkout empty cart')
        );

        const response = await request(app);
          .post('/api/cart/checkout')
          .set('Authorization', 'Bearer token123')
          .expect(400);

        expect(response.body).toEqual({
          success: false,
          error: 'Cannot checkout empty cart'
        });
      });
    });
  });

  describe('Service Layer', () => {
    let shoppingCartService: ShoppingCartService;

    beforeEach(() => {
      shoppingCartService = new ShoppingCartService();
    });

    describe('addItemToCart', () => {
      it('should add new item to cart', async () => {
        const mockCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const mockProduct = {
          id: 'prod-123',
          name: 'Test Product',
          price: 29.99,
          stock: 10
        };

        (shoppingCartService as any).getCartByUserId = jest.fn().mockResolvedValue(mockCart);
        (shoppingCartService as any).getProductById = jest.fn().mockResolvedValue(mockProduct);
        (shoppingCartService as any).updateCart = jest.fn().mockResolvedValue({
          ...mockCart,
          items: [{ id: 'item-1', productId: 'prod-123', quantity: 2, price: 29.99 }],
          total: 59.98
        });

        const result = await shoppingCartService.addItemToCart('user-456', 'prod-123', 2);

        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(59.98);
        expect(result.items[0]).toEqual({
          id: expect.any(String),
          productId: 'prod-123',
          quantity: 2,
          price: 29.99
        });
      });

      it('should update quantity if item already exists', async () => {
        const mockCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [{ id: 'item-1', productId: 'prod-123', quantity: 1, price: 29.99 }],
          total: 29.99,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const mockProduct = {
          id: 'prod-123',
          name: 'Test Product',
          price: 29.99,
          stock: 10
        };

        (shoppingCartService as any).getCartByUserId = jest.fn().mockResolvedValue(mockCart);
        (shoppingCartService as any).getProductById = jest.fn().mockResolvedValue(mockProduct);
        (shoppingCartService as any).updateCart = jest.fn().mockResolvedValue({
          ...mockCart,
          items: [{ id: 'item-1', productId: 'prod-123', quantity: 3, price: 29.99 }],
          total: 89.97
        });

        const result = await shoppingCartService.addItemToCart('user-456', 'prod-123', 2);

        expect(result.items).toHaveLength(1);
        expect(result.items[0].quantity).toBe(3);
        expect(result.total).toBe(89.97);
      });

      it('should throw error when product stock is insufficient', async () => {
        const mockCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const mockProduct = {
          id: 'prod-123',
          name: 'Test Product',
          price: 29.99,
          stock: 1
        };

        (shoppingCartService as any).getCartByUserId = jest.fn().mockResolvedValue(mockCart);
        (shoppingCartService as any).getProductById = jest.fn().mockResolvedValue(mockProduct);

        await expect(
          shoppingCartService.addItemToCart('user-456', 'prod-123', 5)
        ).rejects.toThrow('Insufficient stock for product');
      });
    });

    describe('updateItemQuantity', () => {
      it('should update item quantity successfully', async () => {
        const mockCart = {
          id: 'cart-123',
          userId: 'user-456',
          items: [{ id: 'item-1', productId: 'prod-123', quantity: 2, price: 29.99 }],
          total: 59.98,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const mockProduct = {
          id: 'prod-123',
          name: 'Test Product',
          price: 29.99,
          stock: 10
        };

        (shoppingCartService as any).getCartByUserId = jest.fn().mockResolvedValue(mockCart);
        (shoppingCartService as any).getProductById = jest.fn().mockResolvedValue(mockProduct);
        (shoppingCartService