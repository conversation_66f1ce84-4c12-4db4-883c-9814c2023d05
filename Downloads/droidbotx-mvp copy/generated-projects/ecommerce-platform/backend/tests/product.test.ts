






import { AuthMiddleware } from '../src/middleware/auth.js';
import { Product, ProductStatus } from '../src/models/Product.js';
import { ProductService } from '../src/services/ProductService.js';
import { UserRole } from '../src/types/auth.js';
import request from 'supertest';
import app from '../src/app.js';

Here's a comprehensive Jest test suite for a Product entity in an e-commerce application:'


// __tests__/product.test.ts






// Mock dependencies
jest.mock('../src/models/Product');
jest.mock('../src/services/ProductService');
jest.mock('../src/middleware/auth');

describe('Product API', () => {
  const mockProduct = {
    id: 1,
    name: 'Test Product',
    description: 'Test Description',
    price: 99.99,
    status: ProductStatus.ACTIVE,
    categoryId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    role: UserRole.ADMIN,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/products', () => {
    it('should return all products', async () => {
      (Product.findAll as jest.Mock).mockResolvedValue([mockProduct]);
      
      const response = await request(app);
        .get('/api/products')
        .expect(200);
      
      expect(response.body).toHaveLength(1);
      expect(Product.findAll).toHaveBeenCalled();
    });

    it('should handle empty product list', async () => {
      (Product.findAll as jest.Mock).mockResolvedValue([]);
      
      const response = await request(app);
        .get('/api/products')
        .expect(200);
      
      expect(response.body).toEqual([]);
    });
  });

  describe('GET /api/products/:id', () => {
    it('should return a product by ID', async () => {
      (Product.findByPk as jest.Mock).mockResolvedValue(mockProduct);
      
      const response = await request(app);
        .get('/api/products/1')
        .expect(200);
      
      expect(response.body.name).toBe('Test Product');
      expect(Product.findByPk).toHaveBeenCalledWith(1);
    });

    it('should return 404 for non-existent product', async () => {
      (Product.findByPk as jest.Mock).mockResolvedValue(null);
      
      await request(app)
        .get('/api/products/999')
        .expect(404);
    });
  });

  describe('POST /api/products', () => {
    const newProduct = {
      name: 'New Product',
      description: 'New Description',
      price: 149.99,
      categoryId: 2,
    };

    it('should create a new product for admin users', async () => {
      (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });
      (ProductService.createProduct as jest.Mock).mockResolvedValue({
        ...newProduct,
        id: 2,
        status: ProductStatus.ACTIVE,
      });

      const response = await request(app);
        .post('/api/products')
        .set('Authorization', 'Bearer admin-token')
        .send(newProduct)
        .expect(201);

      expect(response.body.id).toBe(2);
      expect(ProductService.createProduct).toHaveBeenCalledWith(newProduct);
    });

    it('should reject requests from non-admin users', async () => {
      (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
        req.user = { ...mockUser, role: UserRole.CUSTOMER };
        next();
      });

      await request(app)
        .post('/api/products')
        .set('Authorization', 'Bearer customer-token')
        .send(newProduct)
        .expect(403);
    });

    it('should validate required fields', async () => {
      await request(app)
        .post('/api/products')
        .set('Authorization', 'Bearer admin-token')
        .send({ name: 'Incomplete Product' })
        .expect(400);
    });
  });

  describe('PUT /api/products/:id', () => {
    const updateData = { name: 'Updated Product', price: 199.99 };

    it('should update an existing product', async () => {
      (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });
      (Product.findByPk as jest.Mock).mockResolvedValue({
        ...mockProduct,
        update: jest.fn().mockResolvedValue({ ...mockProduct, ...updateData }),
      });

      const response = await request(app);
        .put('/api/products/1')
        .set('Authorization', 'Bearer admin-token')
        .send(updateData)
        .expect(200);

      expect(response.body.name).toBe('Updated Product');
      expect(Product.findByPk).toHaveBeenCalledWith(1);
    });

    it('should return 404 for non-existent product', async () => {
      (Product.findByPk as jest.Mock).mockResolvedValue(null);

      await request(app)
        .put('/api/products/999')
        .set('Authorization', 'Bearer admin-token')
        .send(updateData)
        .expect(404);
    });
  });

  describe('DELETE /api/products/:id', () => {
    it('should delete a product', async () => {
      (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });
      const mockProductInstance = {
        ...mockProduct,
        destroy: jest.fn().mockResolvedValue(true),
      };
      (Product.findByPk as jest.Mock).mockResolvedValue(mockProductInstance);

      await request(app)
        .delete('/api/products/1')
        .set('Authorization', 'Bearer admin-token')
        .expect(204);

      expect(mockProductInstance.destroy).toHaveBeenCalled();
    });

    it('should return 404 for non-existent product', async () => {
      (Product.findByPk as jest.Mock).mockResolvedValue(null);

      await request(app)
        .delete('/api/products/999')
        .set('Authorization', 'Bearer admin-token')
        .expect(404);
    });
  });
});

describe('Product Service', () => {
  describe('createProduct', () => {
    it('should create a product with valid data', async () => {
      const productData = {
        name: 'Service Product',
        description: 'Service Description',
        price: 299.99,
        categoryId: 3,
      };

      (Product.create as jest.Mock).mockResolvedValue({
        id: 3,
        ...productData,
        status: ProductStatus.ACTIVE,
      });

      const result = await ProductService.createProduct(productData);
      
      expect(result.status).toBe(ProductStatus.ACTIVE);
      expect(Product.create).toHaveBeenCalledWith({
        ...productData,
        status: ProductStatus.ACTIVE,
      });
    });

    it('should throw validation error for invalid price', async () => {
      const productData = {
        name: 'Invalid Product',
        description: 'Invalid Description',
        price: -50,
        categoryId: 1,
      };

      await expect(ProductService.createProduct(productData))
        .rejects
        .toThrow('Price must be positive');
    });
  });

  describe('updateProduct', () => {
    it('should update product status correctly', async () => {
      const productInstance = {
        ...mockProduct,
        update: jest.fn().mockResolvedValue({
          ...mockProduct,
          status: ProductStatus.DISCONTINUED,
        }),
      };

      const result = await ProductService.updateProduct(;
        productInstance as any,
        { status: ProductStatus.DISCONTINUED }
      );

      expect(result.status).toBe(ProductStatus.DISCONTINUED);
    });
  });
});

describe('Product Model Validation', () => {
  it('should validate required fields', async () => {
    const product = Product.build({
      name: '',
      price: 99.99,
    } as any);

    await expect(product.validate()).rejects.toThrow();
  });

  it('should validate price is positive', async () => {
    const product = Product.build({
      name: 'Test Product',
      price: -10,
    } as any);

    await expect(product.validate()).rejects.toThrow('Price must be positive');
  });

  it('should validate category ID is provided', async () => {
    const product = Product.build({
      name: 'Test Product',
      price: 99.99,
    } as any);

    await expect(product.validate()).rejects.toThrow('Category is required');
  });
});

describe('Error Handling', () => {
  it('should handle database errors gracefully', async () => {
    (Product.findAll as jest.Mock).mockRejectedValue(new Error('Database connection failed'));
    
    await request(app)
      .get('/api/products')
      .expect(500);
  });

  it('should handle service errors', async () => {
    (ProductService.createProduct as jest.Mock).mockRejectedValue(
      new Error('Service unavailable')
    );

    await request(app)
      .post('/api/products')
      .set('Authorization', 'Bearer admin-token')
      .send({
        name: 'Error Product',
        description: 'Error Description',
        price: 99.99,
        categoryId: 1,
      })
      .expect(500);
  });
});

describe('Authentication and Authorization', () => {
  it('should reject requests without authentication', async () => {
    await request(app)
      .post('/api/products')
      .send({
        name: 'Unauthorized Product',
        price: 99.99,
        categoryId: 1,
      })
      .expect(401);
  });

  it('should reject requests from customers for admin operations', async () => {
    (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
      req.user = { id: 2, email: '<EMAIL>', role: UserRole.CUSTOMER };
      next();
    });

    await request(app)
      .post('/api/products')
      .set('Authorization', 'Bearer customer-token')
      .send({
        name: 'Customer Product',
        price: 99.99,
        categoryId: 1,
      })
      .expect(403);
  });

  it('should allow admin users to perform all operations', async () => {
    (AuthMiddleware.authenticate as jest.Mock).mockImplementation((req, res, next) => {
      req.user = mockUser;
      next();
    });
    (ProductService.createProduct as jest.Mock).mockResolvedValue({
      id: 4,
      name: 'Admin Product',
      price: 199.99,
      categoryId: 1,
      status: ProductStatus.ACTIVE,
    });

    await request(app)
      .post('/api/products')
      .set('Authorization', 'Bearer admin-token')
      .send({
        name: 'Admin Product',
        price: 199.99,
        categoryId: 1,
      })
      .expect(201);
  });
});
`

This test suite covers:

1. **API Endpoints Testing**:
   - GET all products
   - GET product by ID
   - POST new product (with authentication)
   - PUT update product
   - DELETE product

2. **Service Layer Testing**:
   - Product creation with validation
   - Product updates
   - Business rule enforcement

3. **Data Model Validation**:
   - Required fields validation
   - Price validation (positive values)
   - Category association validation

4. **Error Handling**:
   - Database errors
   - Service layer errors
   - Validation errors

5. **Authentication and Authorization**:
   - JWT token validation
   - Role-based access control (admin vs customer)
   - Unauthorized access prevention

Key features of this implementation:
- Comprehensive mocking of dependencies
- Test isolation using beforeEach
- Business rule validation (e.g., price must be positive)
- Role-based access control testing
- Error scenario coverage
- Proper HTTP status code assertions
- Type-safe TypeScript implementation

The tests ensure the product management system works correctly while maintaining security and data integrity.