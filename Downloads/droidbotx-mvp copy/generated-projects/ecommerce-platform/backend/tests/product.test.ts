







import { ProductController } from '../src/controllers/ProductController.js';
import { AuthMiddleware } from '../src/middleware/auth.js';
import { Product, ProductModel } from '../src/models/Product.js';
import { ProductService } from '../src/services/ProductService.js';
import { Role } from '../src/types/Role.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a Product entity in an e-commerce application. Since the specific operations weren't defined, I'll implement common e-commerce product operations.'


// __tests__/product.test.ts







// Mock dependencies
jest.mock('../src/models/Product');
jest.mock('../src/middleware/auth');

const mockProductData: Product = {
  id: 1,
  name: 'Test Product',
  description: 'Test product description',
  price: 99.99,
  category: 'Electronics',
  stock: 10,
  createdAt: new Date(),
  updatedAt: new Date()
};

describe('Product Tests', () => {
  let app: express.Application;
  let productService: ProductService;
  let productController: ProductController;

  beforeAll(() => {
    productService = new ProductService();
    productController = new ProductController(productService);
    
    // Setup express app
    app = express();
    app.use(express.json());
    
    // Mock auth middleware
    (AuthMiddleware as jest.Mock).mockImplementation((req, res, next) => {
      req.user = { id: 1, role: Role.ADMIN };
      next();
    });
    
    // Setup routes
    app.get('/api/products', productController.getAllProducts.bind(productController));
    app.get('/api/products/:id', productController.getProductById.bind(productController));
    app.post('/api/products', AuthMiddleware, productController.createProduct.bind(productController));
    app.put('/api/products/:id', AuthMiddleware, productController.updateProduct.bind(productController));
    app.delete('/api/products/:id', AuthMiddleware, productController.deleteProduct.bind(productController));
  });

  describe('API Endpoints', () => {
    describe('GET /api/products', () => {
      it('should return all products', async () => {
        (ProductModel.findAll as jest.Mock).mockResolvedValue([mockProductData]);
        
        const response = await request(app).get('/api/products');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual([mockProductData]);
        expect(ProductModel.findAll).toHaveBeenCalled();
      });

      it('should handle errors when fetching products', async () => {
        (ProductModel.findAll as jest.Mock).mockRejectedValue(new Error('Database error'));
        
        const response = await request(app).get('/api/products');
        
        expect(response.status).toBe(500);
        expect(response.body).toEqual({ error: 'Failed to fetch products' });
      });
    });

    describe('GET /api/products/:id', () => {
      it('should return a product by ID', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        
        const response = await request(app).get('/api/products/1');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual(mockProductData);
        expect(ProductModel.findById).toHaveBeenCalledWith(1);
      });

      it('should return 404 when product not found', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        const response = await request(app).get('/api/products/999');
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({ error: 'Product not found' });
      });
    });

    describe('POST /api/products', () => {
      const newProductData = {
        name: 'New Product',
        description: 'New product description',
        price: 149.99,
        category: 'Books',
        stock: 5
      };

      it('should create a new product', async () => {
        (ProductModel.create as jest.Mock).mockResolvedValue({ id: 2, ...newProductData });
        
        const response = await request(app);
          .post('/api/products')
          .send(newProductData);
        
        expect(response.status).toBe(201);
        expect(response.body).toEqual({ id: 2, ...newProductData });
        expect(ProductModel.create).toHaveBeenCalledWith(newProductData);
      });

      it('should return validation error for invalid data', async () => {
        const invalidData = { name: '', price: -10 };
        
        const response = await request(app);
          .post('/api/products')
          .send(invalidData);
        
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('PUT /api/products/:id', () => {
      const updateData = { name: 'Updated Product', price: 129.99 };

      it('should update an existing product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        (ProductModel.update as jest.Mock).mockResolvedValue({ ...mockProductData, ...updateData });
        
        const response = await request(app);
          .put('/api/products/1')
          .send(updateData);
        
        expect(response.status).toBe(200);
        expect(response.body.name).toBe('Updated Product');
        expect(ProductModel.update).toHaveBeenCalledWith(1, updateData);
      });

      it('should return 404 when trying to update non-existent product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        const response = await request(app);
          .put('/api/products/999')
          .send(updateData);
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({ error: 'Product not found' });
      });
    });

    describe('DELETE /api/products/:id', () => {
      it('should delete a product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        (ProductModel.delete as jest.Mock).mockResolvedValue(true);
        
        const response = await request(app).delete('/api/products/1');
        
        expect(response.status).toBe(200);
        expect(response.body).toEqual({ message: 'Product deleted successfully' });
        expect(ProductModel.delete).toHaveBeenCalledWith(1);
      });

      it('should return 404 when trying to delete non-existent product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        const response = await request(app).delete('/api/products/999');
        
        expect(response.status).toBe(404);
        expect(response.body).toEqual({ error: 'Product not found' });
      });
    });
  });

  describe('Service Layer', () => {
    describe('getAllProducts', () => {
      it('should return all products from database', async () => {
        (ProductModel.findAll as jest.Mock).mockResolvedValue([mockProductData]);
        
        const result = await productService.getAllProducts();
        
        expect(result).toEqual([mockProductData]);
        expect(ProductModel.findAll).toHaveBeenCalled();
      });

      it('should handle database errors', async () => {
        (ProductModel.findAll as jest.Mock).mockRejectedValue(new Error('DB error'));
        
        await expect(productService.getAllProducts()).rejects.toThrow('Failed to fetch products');
      });
    });

    describe('getProductById', () => {
      it('should return product when found', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        
        const result = await productService.getProductById(1);
        
        expect(result).toEqual(mockProductData);
        expect(ProductModel.findById).toHaveBeenCalledWith(1);
      });

      it('should return null when product not found', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        const result = await productService.getProductById(999);
        
        expect(result).toBeNull();
      });
    });

    describe('createProduct', () => {
      const productData = {
        name: 'New Product',
        description: 'Description',
        price: 29.99,
        category: 'Toys',
        stock: 20
      };

      it('should create a valid product', async () => {
        (ProductModel.create as jest.Mock).mockResolvedValue({ id: 2, ...productData });
        
        const result = await productService.createProduct(productData);
        
        expect(result).toEqual({ id: 2, ...productData });
        expect(ProductModel.create).toHaveBeenCalledWith(productData);
      });

      it('should validate required fields', async () => {
        const invalidData = { name: '', price: 29.99 };
        
        await expect(productService.createProduct(invalidData as any))
          .rejects.toThrow('Product name is required');
      });

      it('should validate price is positive', async () => {
        const invalidData = { name: 'Product', price: -10 };
        
        await expect(productService.createProduct(invalidData as any))
          .rejects.toThrow('Price must be a positive number');
      });

      it('should validate stock is non-negative', async () => {
        const invalidData = { name: 'Product', price: 29.99, stock: -5 };
        
        await expect(productService.createProduct(invalidData as any))
          .rejects.toThrow('Stock cannot be negative');
      });
    });

    describe('updateProduct', () => {
      const updateData = { name: 'Updated Name', price: 39.99 };

      it('should update an existing product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        (ProductModel.update as jest.Mock).mockResolvedValue({ ...mockProductData, ...updateData });
        
        const result = await productService.updateProduct(1, updateData);
        
        expect(result).toEqual({ ...mockProductData, ...updateData });
        expect(ProductModel.findById).toHaveBeenCalledWith(1);
        expect(ProductModel.update).toHaveBeenCalledWith(1, updateData);
      });

      it('should throw error when product not found', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        await expect(productService.updateProduct(999, updateData))
          .rejects.toThrow('Product not found');
      });

      it('should validate price if provided', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        
        await expect(productService.updateProduct(1, { price: -5 } as any))
          .rejects.toThrow('Price must be a positive number');
      });
    });

    describe('deleteProduct', () => {
      it('should delete an existing product', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(mockProductData);
        (ProductModel.delete as jest.Mock).mockResolvedValue(true);
        
        const result = await productService.deleteProduct(1);
        
        expect(result).toBe(true);
        expect(ProductModel.findById).toHaveBeenCalledWith(1);
        expect(ProductModel.delete).toHaveBeenCalledWith(1);
      });

      it('should throw error when product not found', async () => {
        (ProductModel.findById as jest.Mock).mockResolvedValue(null);
        
        await expect(productService.deleteProduct(999))
          .rejects.toThrow('Product not found');
      });
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', () => {
      const product = new ProductModel();
      
      expect(() => product.validate()).toThrow('Product name is required');
    });

    it('should validate price is positive', () => {
      const product = new ProductModel();
      product.name = 'Test Product';
      product.price = -10;
      
      expect(() => product.validate()).toThrow('Price must be a positive number');
    });

    it('should validate stock is non-negative', () => {
      const product = new ProductModel();
      product.name = 'Test Product';
      product.price = 29.99;
      product.stock = -5;
      
      expect(() => product.validate()).toThrow('Stock cannot be negative');
    });

    it('should pass validation for valid product', () => {
      const product = new ProductModel();
      product.name = 'Valid Product';
      product.description = 'Valid description';
      product.price = 29.99;
      product.category = 'Electronics';
      product.stock = 10;
      
      expect(() => product.validate()).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      (ProductModel.findAll as jest.Mock).mockRejectedValue(new Error('Connection failed'));
      
      const response = await request(app).get('/api/products');
      
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ error: 'Failed to fetch products' });
    });

    it('should handle validation errors gracefully', async () => {
      const response = await request(app);
        .post('/api/products')
        .send({ name: '', price: -10 });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Authentication and Authorization', () => {
    it('should require authentication for create operations', async () => {
      // Reset auth middleware mock to simulate unauthenticated request
      (AuthMiddleware as jest.Mock).mockImplementation((req, res, next) => {
        res.status(401).json({ error: 'Unauthorized' });
      });
      
      const response = await request(app);
        .post('/api/products')
        .send({ name: 'Test Product', price: 29.99 });
      
      expect(response.status).toBe(401);
      expect(response.body).toEqual({ error: 'Unauthorized' });
    });

    it('should require admin role for delete operations', async () => {
      // Mock auth middleware to simulate non-admin user
      (AuthMiddleware as jest.Mock).mockImplementation((req, res