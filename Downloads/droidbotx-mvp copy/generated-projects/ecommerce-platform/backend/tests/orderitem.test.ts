







import { AuthMiddleware } from '../src/middleware/auth.js';
import { OrderItemModel } from '../src/models/orderItemModel.js';
import { orderItemRoutes } from '../src/routes/orderItemRoutes.js';
import { OrderItemService } from '../src/services/OrderItemService';
import { Role } from '../src/types/roles.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for an OrderItem entity. Since the application type wasn't specified, I'll assume it's an e-commerce system with typical order management functionality.


// __tests__/orderItem.test.ts






// Mock dependencies
jest.mock('../src/services/orderItemService');
jest.mock('../src/models/orderItemModel');
jest.mock('../src/middleware/auth');

const mockOrderItemService = new (OrderItemService as jest.Mock<OrderItemService>)();
const mockOrderItemModel = new (OrderItemModel as jest.Mock<OrderItemModel>)();

// Create Express app for testing
const app = express();
app.use(express.json());

// Mock authentication middleware
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;
mockAuthMiddleware.authenticate = jest.fn((req, res, next) => next());
mockAuthMiddleware.authorize = jest.fn(() => (req, res, next) => next());

// Import routes after mocking

app.use('/api/order-items', orderItemRoutes);

describe('OrderItem Tests', () => {
  const mockOrderItem = {
    id: 1,
    orderId: 101,
    productId: 201,
    quantity: 2,
    price: 29.99,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  };

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    role: Role.CUSTOMER
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Endpoints', () => {
    describe('GET /api/order-items/:id', () => {
      it('should return order item when found', async () => {
        (mockOrderItemService.getOrderItem as jest.Mock).mockResolvedValue(mockOrderItem);

        const response = await request(app);
          .get('/api/order-items/1')
          .expect(200);

        expect(response.body).toEqual(mockOrderItem);
        expect(mockOrderItemService.getOrderItem).toHaveBeenCalledWith(1);
      });

      it('should return 404 when order item not found', async () => {
        (mockOrderItemService.getOrderItem as jest.Mock).mockResolvedValue(null);

        await request(app)
          .get('/api/order-items/999')
          .expect(404);
      });

      it('should handle service errors', async () => {
        (mockOrderItemService.getOrderItem as jest.Mock).mockRejectedValue(new Error('Database error'));

        await request(app)
          .get('/api/order-items/1')
          .expect(500);
      });
    });

    describe('POST /api/order-items', () => {
      const newOrderItem = {
        orderId: 101,
        productId: 201,
        quantity: 3,
        price: 19.99
      };

      it('should create a new order item', async () => {
        (mockOrderItemService.createOrderItem as jest.Mock).mockResolvedValue({
          ...mockOrderItem,
          ...newOrderItem,
          id: 2
        });

        const response = await request(app);
          .post('/api/order-items')
          .send(newOrderItem)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.orderId).toBe(newOrderItem.orderId);
        expect(mockOrderItemService.createOrderItem).toHaveBeenCalledWith(newOrderItem);
      });

      it('should return 400 for invalid input', async () => {
        await request(app)
          .post('/api/order-items')
          .send({ orderId: 101 }) // Missing required fields
          .expect(400);
      });

      it('should handle validation errors', async () => {
        (mockOrderItemService.createOrderItem as jest.Mock).mockRejectedValue(
          new Error('Validation failed: quantity must be greater than 0')
        );

        await request(app)
          .post('/api/order-items')
          .send({ ...newOrderItem, quantity: -1 })
          .expect(400);
      });
    });

    describe('PUT /api/order-items/:id', () => {
      const updateData = { quantity: 5, price: 24.99 };

      it('should update an existing order item', async () => {
        const updatedOrderItem = { ...mockOrderItem, ...updateData };
        (mockOrderItemService.updateOrderItem as jest.Mock).mockResolvedValue(updatedOrderItem);

        const response = await request(app);
          .put('/api/order-items/1')
          .send(updateData)
          .expect(200);

        expect(response.body.quantity).toBe(updateData.quantity);
        expect(mockOrderItemService.updateOrderItem).toHaveBeenCalledWith(1, updateData);
      });

      it('should return 404 when order item not found', async () => {
        (mockOrderItemService.updateOrderItem as jest.Mock).mockResolvedValue(null);

        await request(app)
          .put('/api/order-items/999')
          .send(updateData)
          .expect(404);
      });
    });

    describe('DELETE /api/order-items/:id', () => {
      it('should delete an order item', async () => {
        (mockOrderItemService.deleteOrderItem as jest.Mock).mockResolvedValue(true);

        await request(app)
          .delete('/api/order-items/1')
          .expect(204);

        expect(mockOrderItemService.deleteOrderItem).toHaveBeenCalledWith(1);
      });

      it('should return 404 when order item not found', async () => {
        (mockOrderItemService.deleteOrderItem as jest.Mock).mockResolvedValue(false);

        await request(app)
          .delete('/api/order-items/999')
          .expect(404);
      });
    });
  });

  describe('Service Layer', () => {
    describe('getOrderItem', () => {
      it('should retrieve order item by ID', async () => {
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(mockOrderItem);

        const result = await mockOrderItemService.getOrderItem(1);
        
        expect(result).toEqual(mockOrderItem);
        expect(mockOrderItemModel.findById).toHaveBeenCalledWith(1);
      });

      it('should return null when order item not found', async () => {
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(null);

        const result = await mockOrderItemService.getOrderItem(999);
        
        expect(result).toBeNull();
      });
    });

    describe('createOrderItem', () => {
      const newOrderItem = {
        orderId: 101,
        productId: 201,
        quantity: 2,
        price: 29.99
      };

      it('should create a new order item', async () => {
        (mockOrderItemModel.create as jest.Mock).mockResolvedValue({
          ...mockOrderItem,
          ...newOrderItem
        });

        const result = await mockOrderItemService.createOrderItem(newOrderItem);
        
        expect(result).toMatchObject(newOrderItem);
        expect(mockOrderItemModel.create).toHaveBeenCalledWith(newOrderItem);
      });

      it('should validate required fields', async () => {
        await expect(mockOrderItemService.createOrderItem({
          orderId: 101
          // Missing required fields
        } as any)).rejects.toThrow();
      });

      it('should validate quantity is positive', async () => {
        await expect(mockOrderItemService.createOrderItem({
          ...newOrderItem,
          quantity: -1
        })).rejects.toThrow('Quantity must be greater than 0');
      });

      it('should validate price is non-negative', async () => {
        await expect(mockOrderItemService.createOrderItem({
          ...newOrderItem,
          price: -5.00
        })).rejects.toThrow('Price must be non-negative');
      });
    });

    describe('updateOrderItem', () => {
      const updateData = { quantity: 3 };

      it('should update an existing order item', async () => {
        const existingItem = { ...mockOrderItem };
        const updatedItem = { ...mockOrderItem, ...updateData };
        
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(existingItem);
        (mockOrderItemModel.update as jest.Mock).mockResolvedValue(updatedItem);

        const result = await mockOrderItemService.updateOrderItem(1, updateData);
        
        expect(result).toEqual(updatedItem);
        expect(mockOrderItemModel.findById).toHaveBeenCalledWith(1);
        expect(mockOrderItemModel.update).toHaveBeenCalledWith(1, updateData);
      });

      it('should return null when order item not found', async () => {
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(null);

        const result = await mockOrderItemService.updateOrderItem(999, updateData);
        
        expect(result).toBeNull();
      });

      it('should validate quantity when provided', async () => {
        const existingItem = { ...mockOrderItem };
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(existingItem);

        await expect(mockOrderItemService.updateOrderItem(1, { quantity: -1 }))
          .rejects.toThrow('Quantity must be greater than 0');
      });
    });

    describe('deleteOrderItem', () => {
      it('should delete an existing order item', async () => {
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(mockOrderItem);
        (mockOrderItemModel.delete as jest.Mock).mockResolvedValue(true);

        const result = await mockOrderItemService.deleteOrderItem(1);
        
        expect(result).toBe(true);
        expect(mockOrderItemModel.findById).toHaveBeenCalledWith(1);
        expect(mockOrderItemModel.delete).toHaveBeenCalledWith(1);
      });

      it('should return false when order item not found', async () => {
        (mockOrderItemModel.findById as jest.Mock).mockResolvedValue(null);

        const result = await mockOrderItemService.deleteOrderItem(999);
        
        expect(result).toBe(false);
      });
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', () => {
      const invalidOrderItem = new mockOrderItemModel({
        // Missing required fields
      } as any);
      
      expect(invalidOrderItem.validate).toThrow();
    });

    it('should validate data types', () => {
      const invalidOrderItem = new mockOrderItemModel({
        orderId: 'invalid', // Should be number
        productId: 'invalid', // Should be number
        quantity: 'invalid', // Should be number
        price: 'invalid' // Should be number
      } as any);
      
      expect(invalidOrderItem.validate).toThrow();
    });

    it('should validate positive quantity', () => {
      const invalidOrderItem = new mockOrderItemModel({
        orderId: 101,
        productId: 201,
        quantity: -1, // Invalid negative quantity
        price: 29.99
      } as any);
      
      expect(invalidOrderItem.validate).toThrow('Quantity must be greater than 0');
    });

    it('should validate non-negative price', () => {
      const invalidOrderItem = new mockOrderItemModel({
        orderId: 101,
        productId: 201,
        quantity: 2,
        price: -5.00 // Invalid negative price
      } as any);
      
      expect(invalidOrderItem.validate).toThrow('Price must be non-negative');
    });

    it('should accept valid order item data', () => {
      const validOrderItem = new mockOrderItemModel({
        orderId: 101,
        productId: 201,
        quantity: 2,
        price: 29.99
      });
      
      expect(validOrderItem.validate()).toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      (mockOrderItemModel.findById as jest.Mock).mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(mockOrderItemService.getOrderItem(1))
        .rejects.toThrow('Database connection failed');
    });

    it('should handle validation errors in service layer', async () => {
      (mockOrderItemModel.create as jest.Mock).mockRejectedValue(
        new Error('Validation failed')
      );

      await expect(mockOrderItemService.createOrderItem({
        orderId: 101,
        productId: 201,
        quantity: -1, // Invalid
        price: 29.99
      })).rejects.toThrow('Validation failed');
    });

    it('should handle unexpected errors gracefully', async () => {
      (mockOrderItemModel.update as jest.Mock).mockRejectedValue(
        new Error('Unexpected error')
      );

      await expect(mockOrderItemService.updateOrderItem(1, { quantity: 5 }))
        .rejects.toThrow('Unexpected error');
    });
  });

  describe('Authentication and Authorization', () => {
    const adminUser = { ...mockUser, role: Role.ADMIN };
    const customerUser = { ...mockUser, role: Role.CUSTOMER };

    it('should require authentication for all endpoints', async () => {
      mockAuthMiddleware.authenticate.mockImplementationOnce((req, res, next) => {
        return res.status(401).json({ message: 'Unauthorized' });
      });

      await request(app)
        .get('/api/order-items/1')
        .expect(401);
    });

    it('should allow admins to access all operations', async () => {
      mockAuthMiddleware