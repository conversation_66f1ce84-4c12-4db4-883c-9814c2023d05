






import { AuthMiddleware } from '../src/middleware/auth.js';
import { ProductCategory } from '../src/models/ProductCategory.js';
import { ProductCategoryService } from '../src/services/ProductCategoryService';
import { UserRole } from '../src/types/enums.js';
import request from 'supertest';
import app from '../src/app.js';

Here's a comprehensive test suite for the ProductCategory entity in an e-commerce application:'


// __tests__/productCategory.test.ts






// Mock dependencies
jest.mock('../src/services/productCategoryService');
jest.mock('../src/middleware/auth');

const mockProductCategoryService = ProductCategoryService as jest.Mocked<typeof ProductCategoryService>;
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;

describe('ProductCategory', () => {
  const baseUrl = '/api/categories';
  const mockCategory: ProductCategory = {
    id: 1,
    name: 'Electronics',
    description: 'Electronic devices and accessories',
    parentId: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Endpoints', () => {
    describe('GET /api/categories', () => {
      it('should return all product categories', async () => {
        mockProductCategoryService.getAll.mockResolvedValue([mockCategory]);
        
        const response = await request(app);
          .get(baseUrl)
          .expect(200);
        
        expect(response.body).toEqual([mockCategory]);
        expect(mockProductCategoryService.getAll).toHaveBeenCalled();
      });

      it('should handle empty categories list', async () => {
        mockProductCategoryService.getAll.mockResolvedValue([]);
        
        const response = await request(app);
          .get(baseUrl)
          .expect(200);
        
        expect(response.body).toEqual([]);
      });
    });

    describe('GET /api/categories/:id', () => {
      it('should return a specific category by ID', async () => {
        mockProductCategoryService.getById.mockResolvedValue(mockCategory);
        
        const response = await request(app);
          .get(`${baseUrl}/1`)
          .expect(200);
        
        expect(response.body).toEqual(mockCategory);
        expect(mockProductCategoryService.getById).toHaveBeenCalledWith(1);
      });

      it('should return 404 for non-existent category', async () => {
        mockProductCategoryService.getById.mockResolvedValue(null);
        
        await request(app)
          .get(`${baseUrl}/999`)
          .expect(404);
      });
    });

    describe('POST /api/categories', () => {
      const newCategoryData = {
        name: 'Books',
        description: 'Physical and digital books'
      };

      it('should create a new category with admin privileges', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 1, role: UserRole.ADMIN };
          next();
        });
        mockProductCategoryService.create.mockResolvedValue({
          ...mockCategory,
          ...newCategoryData,
          id: 2
        });

        const response = await request(app);
          .post(baseUrl)
          .set('Authorization', 'Bearer admin-token')
          .send(newCategoryData)
          .expect(201);

        expect(response.body.name).toBe('Books');
        expect(mockProductCategoryService.create).toHaveBeenCalledWith(newCategoryData);
      });

      it('should reject requests without authentication', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          res.status(401).json({ message: 'Unauthorized' });
        });

        await request(app)
          .post(baseUrl)
          .send(newCategoryData)
          .expect(401);
      });

      it('should reject requests from non-admin users', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 2, role: UserRole.CUSTOMER };
          next();
        });

        await request(app)
          .post(baseUrl)
          .set('Authorization', 'Bearer customer-token')
          .send(newCategoryData)
          .expect(403);
      });
    });

    describe('PUT /api/categories/:id', () => {
      const updateData = { name: 'Updated Electronics' };

      it('should update an existing category', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 1, role: UserRole.ADMIN };
          next();
        });
        mockProductCategoryService.update.mockResolvedValue({
          ...mockCategory,
          name: 'Updated Electronics'
        });

        const response = await request(app);
          .put(`${baseUrl}/1`)
          .set('Authorization', 'Bearer admin-token')
          .send(updateData)
          .expect(200);

        expect(response.body.name).toBe('Updated Electronics');
        expect(mockProductCategoryService.update).toHaveBeenCalledWith(1, updateData);
      });

      it('should return 404 when updating non-existent category', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 1, role: UserRole.ADMIN };
          next();
        });
        mockProductCategoryService.update.mockResolvedValue(null);

        await request(app)
          .put(`${baseUrl}/999`)
          .set('Authorization', 'Bearer admin-token')
          .send(updateData)
          .expect(404);
      });
    });

    describe('DELETE /api/categories/:id', () => {
      it('should delete a category', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 1, role: UserRole.ADMIN };
          next();
        });
        mockProductCategoryService.delete.mockResolvedValue(true);

        await request(app)
          .delete(`${baseUrl}/1`)
          .set('Authorization', 'Bearer admin-token')
          .expect(204);

        expect(mockProductCategoryService.delete).toHaveBeenCalledWith(1);
      });

      it('should return 404 when deleting non-existent category', async () => {
        mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
          req.user = { id: 1, role: UserRole.ADMIN };
          next();
        });
        mockProductCategoryService.delete.mockResolvedValue(false);

        await request(app)
          .delete(`${baseUrl}/999`)
          .set('Authorization', 'Bearer admin-token')
          .expect(404);
      });
    });
  });

  describe('Service Layer', () => {
    describe('getAll', () => {
      it('should return all categories from repository', async () => {
        const categories = [mockCategory];
        mockProductCategoryService.getAll.mockResolvedValue(categories);
        
        const result = await mockProductCategoryService.getAll();
        expect(result).toEqual(categories);
      });
    });

    describe('getById', () => {
      it('should return category when found', async () => {
        mockProductCategoryService.getById.mockResolvedValue(mockCategory);
        
        const result = await mockProductCategoryService.getById(1);
        expect(result).toEqual(mockCategory);
      });

      it('should return null when category not found', async () => {
        mockProductCategoryService.getById.mockResolvedValue(null);
        
        const result = await mockProductCategoryService.getById(999);
        expect(result).toBeNull();
      });
    });

    describe('create', () => {
      it('should create a new category with valid data', async () => {
        const newCategory = {
          name: 'Clothing',
          description: 'Apparel and accessories'
        };
        const createdCategory = { ...mockCategory, ...newCategory, id: 2 };
        
        mockProductCategoryService.create.mockResolvedValue(createdCategory);
        
        const result = await mockProductCategoryService.create(newCategory);
        expect(result.name).toBe('Clothing');
        expect(result.description).toBe('Apparel and accessories');
      });

      it('should throw error for invalid category data', async () => {
        const invalidCategory = { name: '' }; // Empty name should be invalid
        
        await expect(
          mockProductCategoryService.create(invalidCategory as any)
        ).rejects.toThrow();
      });
    });

    describe('update', () => {
      it('should update existing category', async () => {
        const updateData = { name: 'Home & Kitchen' };
        const updatedCategory = { ...mockCategory, ...updateData };
        
        mockProductCategoryService.update.mockResolvedValue(updatedCategory);
        
        const result = await mockProductCategoryService.update(1, updateData);
        expect(result?.name).toBe('Home & Kitchen');
      });

      it('should return null when updating non-existent category', async () => {
        mockProductCategoryService.update.mockResolvedValue(null);
        
        const result = await mockProductCategoryService.update(999, { name: 'Test' });
        expect(result).toBeNull();
      });
    });

    describe('delete', () => {
      it('should delete existing category', async () => {
        mockProductCategoryService.delete.mockResolvedValue(true);
        
        const result = await mockProductCategoryService.delete(1);
        expect(result).toBe(true);
      });

      it('should return false when deleting non-existent category', async () => {
        mockProductCategoryService.delete.mockResolvedValue(false);
        
        const result = await mockProductCategoryService.delete(999);
        expect(result).toBe(false);
      });
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', () => {
      const category = new ProductCategory();
      const errors = category.validate();
      
      expect(errors).toContain('Name is required');
    });

    it('should validate name length', () => {
      const category = new ProductCategory();
      category.name = 'A'.repeat(101); // Exceeds max length
      const errors = category.validate();
      
      expect(errors).toContain('Name must be less than 100 characters');
    });

    it('should validate description length', () => {
      const category = new ProductCategory();
      category.name = 'Valid Name';
      category.description = 'A'.repeat(501); // Exceeds max length
      const errors = category.validate();
      
      expect(errors).toContain('Description must be less than 500 characters');
    });

    it('should pass validation with valid data', () => {
      const category = new ProductCategory();
      category.name = 'Electronics';
      category.description = 'Electronic devices';
      const errors = category.validate();
      
      expect(errors).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const dbError = new Error('Database connection failed');
      mockProductCategoryService.getAll.mockRejectedValue(dbError);
      
      await expect(mockProductCategoryService.getAll()).rejects.toThrow('Database connection failed');
    });

    it('should handle validation errors', async () => {
      const invalidData = { name: '' };
      
      await expect(
        mockProductCategoryService.create(invalidData as any)
      ).rejects.toThrow();
    });
  });

  describe('Authentication and Authorization', () => {
    it('should allow admin users to create categories', async () => {
      mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
        req.user = { id: 1, role: UserRole.ADMIN };
        next();
      });

      await request(app)
        .post(baseUrl)
        .set('Authorization', 'Bearer admin-token')
        .send({ name: 'Test Category' })
        .expect(201);
    });

    it('should deny non-admin users from creating categories', async () => {
      mockAuthMiddleware.authenticate.mockImplementation((req, res, next) => {
        req.user = { id: 2, role: UserRole.CUSTOMER };
        next();
      });

      await request(app)
        .post(baseUrl)
        .set('Authorization', 'Bearer customer-token')
        .send({ name: 'Test Category' })
        .expect(403);
    });

    it('should allow unauthenticated access to read operations', async () => {
      mockProductCategoryService.getAll.mockResolvedValue([mockCategory]);
      
      await request(app)
        .get(baseUrl)
        .expect(200);
    });
  });
});
`

This test suite covers:

1. **API Endpoints Testing**:
   - GET all categories
   - GET category by ID
   - POST new category (with auth)
   - PUT update category (with auth)
   - DELETE category (with auth)

2. **Service Layer Testing**:
   - getAll, getById, create, update, delete operations
   - Validation and error handling scenarios

3. **Data Model Validation**:
   - Required field validation
   - String length constraints
   - Valid/invalid data scenarios

4. **Error Handling**:
   - Database errors
   - Validation errors
   - Graceful error responses

5. **Authentication and Authorization**:
   - Admin-only operations
   - Role-based access control
   - Unauthenticated read access

Key features of this implementation:
- Comprehensive mocking of dependencies
- Realistic test data and scenarios
- Proper assertion of expected behaviors
- Coverage of both success and error cases
- Authentication/authorization flow testing
- TypeScript type safety throughout

The tests follow Jest best practices with proper setup/teardown, clear test descriptions, and focused assertions. Mock implementations simulate real-world scenarios including edge cases and error conditions.