






import { authenticateToken, authorizeRole } from '../src/middleware/auth.js';
import { CartItemModel } from '../src/models/cartItemModel.js';
import { CartItemService } from '../src/services/CartItemService';
import { UserRole } from '../src/types/user.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a CartItem entity. Based on the undefined application context, I'll assume this is for an e-commerce platform with typical cart functionality.


// cartItem.test.ts






// Mock dependencies
jest.mock('../src/models/cartItemModel');
jest.mock('../src/middleware/auth');

const mockCartItemModel = CartItemModel as jest.Mocked<typeof CartItemModel>;
const mockAuthenticateToken = authenticateToken as jest.Mock;
const mockAuthorizeRole = authorizeRole as jest.Mock;

// Create express app for testing routes
const app = express();
app.use(express.json());

// Import routes after mocks are set up
const cartItemRoutes = require('../src/routes/cartItemRoutes').default;
app.use('/api/cart-items', cartItemRoutes);

describe('CartItem Tests', () => {
  const mockUserId = 1;
  const mockAdminId = 2;
  const mockCartItem = {
    id: 1,
    userId: mockUserId,
    productId: 1,
    quantity: 2,
    price: 29.99,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock authentication middleware
    mockAuthenticateToken.mockImplementation((req, res, next) => {
      req.user = { id: mockUserId, role: UserRole.CUSTOMER };
      next();
    });
    
    mockAuthorizeRole.mockImplementation((roles) => (req, res, next) => {
      next();
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', async () => {
      const invalidCartItem = { userId: 1 };
      await expect(CartItemModel.create(invalidCartItem)).rejects.toThrow();
    });

    it('should validate positive quantity', async () => {
      const invalidCartItem = { 
        userId: 1, 
        productId: 1, 
        quantity: -1, 
        price: 10 
      };
      await expect(CartItemModel.create(invalidCartItem)).rejects.toThrow('Quantity must be positive');
    });

    it('should validate positive price', async () => {
      const invalidCartItem = { 
        userId: 1, 
        productId: 1, 
        quantity: 1, 
        price: -5 
      };
      await expect(CartItemModel.create(invalidCartItem)).rejects.toThrow('Price must be positive');
    });

    it('should create valid cart item', async () => {
      mockCartItemModel.create.mockResolvedValue(mockCartItem);
      
      const result = await CartItemModel.create({
        userId: 1,
        productId: 1,
        quantity: 2,
        price: 29.99
      });
      
      expect(result).toEqual(mockCartItem);
    });
  });

  describe('Service Layer', () => {
    describe('addItemToCart', () => {
      it('should add item to cart successfully', async () => {
        mockCartItemModel.findByUserAndProduct.mockResolvedValue(null);
        mockCartItemModel.create.mockResolvedValue(mockCartItem);
        
        const result = await CartItemService.addItemToCart({
          userId: mockUserId,
          productId: 1,
          quantity: 2,
          price: 29.99
        });
        
        expect(result).toEqual(mockCartItem);
        expect(mockCartItemModel.create).toHaveBeenCalledWith({
          userId: mockUserId,
          productId: 1,
          quantity: 2,
          price: 29.99
        });
      });

      it('should update quantity if item already exists', async () => {
        const existingItem = { ...mockCartItem, quantity: 1 };
        mockCartItemModel.findByUserAndProduct.mockResolvedValue(existingItem);
        mockCartItemModel.update.mockResolvedValue({ ...existingItem, quantity: 3 });
        
        const result = await CartItemService.addItemToCart({
          userId: mockUserId,
          productId: 1,
          quantity: 2,
          price: 29.99
        });
        
        expect(result.quantity).toBe(3);
        expect(mockCartItemModel.update).toHaveBeenCalledWith(;
          existingItem.id,
          { quantity: 3 }
        );
      });

      it('should throw error for invalid quantity', async () => {
        await expect(CartItemService.addItemToCart({
          userId: mockUserId,
          productId: 1,
          quantity: 0,
          price: 29.99
        })).rejects.toThrow('Quantity must be greater than 0');
      });
    });

    describe('getCartItems', () => {
      it('should retrieve all items for a user', async () => {
        const mockCartItems = [mockCartItem];
        mockCartItemModel.findByUserId.mockResolvedValue(mockCartItems);
        
        const result = await CartItemService.getCartItems(mockUserId);
        
        expect(result).toEqual(mockCartItems);
        expect(mockCartItemModel.findByUserId).toHaveBeenCalledWith(mockUserId);
      });
    });

    describe('updateCartItem', () => {
      it('should update cart item quantity', async () => {
        mockCartItemModel.findById.mockResolvedValue(mockCartItem);
        mockCartItemModel.update.mockResolvedValue({ ...mockCartItem, quantity: 5 });
        
        const result = await CartItemService.updateCartItem(1, { quantity: 5 });
        
        expect(result.quantity).toBe(5);
        expect(mockCartItemModel.update).toHaveBeenCalledWith(1, { quantity: 5 });
      });

      it('should throw error if item not found', async () => {
        mockCartItemModel.findById.mockResolvedValue(null);
        
        await expect(CartItemService.updateCartItem(999, { quantity: 5 }))
          .rejects.toThrow('Cart item not found');
      });

      it('should throw error for unauthorized update', async () => {
        const otherUserItem = { ...mockCartItem, userId: 999 };
        mockCartItemModel.findById.mockResolvedValue(otherUserItem);
        
        await expect(CartItemService.updateCartItem(1, { quantity: 5 }, mockUserId))
          .rejects.toThrow('Unauthorized');
      });
    });

    describe('removeFromCart', () => {
      it('should remove item from cart', async () => {
        mockCartItemModel.findById.mockResolvedValue(mockCartItem);
        mockCartItemModel.delete.mockResolvedValue(true);
        
        const result = await CartItemService.removeFromCart(1, mockUserId);
        
        expect(result).toBe(true);
        expect(mockCartItemModel.delete).toHaveBeenCalledWith(1);
      });

      it('should throw error if item not found', async () => {
        mockCartItemModel.findById.mockResolvedValue(null);
        
        await expect(CartItemService.removeFromCart(999, mockUserId))
          .rejects.toThrow('Cart item not found');
      });
    });

    describe('clearCart', () => {
      it('should clear all items for user', async () => {
        mockCartItemModel.deleteByUserId.mockResolvedValue(3);
        
        const result = await CartItemService.clearCart(mockUserId);
        
        expect(result).toBe(3); // Number of items deleted
        expect(mockCartItemModel.deleteByUserId).toHaveBeenCalledWith(mockUserId);
      });
    });
  });

  describe('API Endpoints', () => {
    describe('POST /api/cart-items', () => {
      it('should add item to cart successfully', async () => {
        mockCartItemModel.findByUserAndProduct.mockResolvedValue(null);
        mockCartItemModel.create.mockResolvedValue(mockCartItem);
        
        const response = await request(app);
          .post('/api/cart-items')
          .set('Authorization', 'Bearer token')
          .send({
            productId: 1,
            quantity: 2,
            price: 29.99
          })
          .expect(201);
        
        expect(response.body).toEqual(expect.objectContaining({
          id: 1,
          userId: mockUserId,
          productId: 1,
          quantity: 2
        }));
      });

      it('should return 400 for invalid input', async () => {
        await request(app)
          .post('/api/cart-items')
          .set('Authorization', 'Bearer token')
          .send({
            productId: 1,
            quantity: -1
          })
          .expect(400);
      });
    });

    describe('GET /api/cart-items', () => {
      it('should retrieve user cart items', async () => {
        mockCartItemModel.findByUserId.mockResolvedValue([mockCartItem]);
        
        const response = await request(app);
          .get('/api/cart-items')
          .set('Authorization', 'Bearer token')
          .expect(200);
        
        expect(response.body).toHaveLength(1);
        expect(response.body[0]).toEqual(expect.objectContaining({
          id: 1,
          productId: 1,
          quantity: 2
        }));
      });
    });

    describe('PUT /api/cart-items/:id', () => {
      it('should update cart item', async () => {
        mockCartItemModel.findById.mockResolvedValue(mockCartItem);
        mockCartItemModel.update.mockResolvedValue({ ...mockCartItem, quantity: 5 });
        
        const response = await request(app);
          .put('/api/cart-items/1')
          .set('Authorization', 'Bearer token')
          .send({ quantity: 5 })
          .expect(200);
        
        expect(response.body.quantity).toBe(5);
      });

      it('should return 404 for non-existent item', async () => {
        mockCartItemModel.findById.mockResolvedValue(null);
        
        await request(app)
          .put('/api/cart-items/999')
          .set('Authorization', 'Bearer token')
          .send({ quantity: 5 })
          .expect(404);
      });
    });

    describe('DELETE /api/cart-items/:id', () => {
      it('should remove item from cart', async () => {
        mockCartItemModel.findById.mockResolvedValue(mockCartItem);
        mockCartItemModel.delete.mockResolvedValue(true);
        
        await request(app)
          .delete('/api/cart-items/1')
          .set('Authorization', 'Bearer token')
          .expect(204);
      });

      it('should return 404 for non-existent item', async () => {
        mockCartItemModel.findById.mockResolvedValue(null);
        
        await request(app)
          .delete('/api/cart-items/999')
          .set('Authorization', 'Bearer token')
          .expect(404);
      });
    });

    describe('DELETE /api/cart-items', () => {
      it('should clear entire cart', async () => {
        mockCartItemModel.deleteByUserId.mockResolvedValue(3);
        
        const response = await request(app);
          .delete('/api/cart-items')
          .set('Authorization', 'Bearer token')
          .expect(200);
        
        expect(response.body.deletedCount).toBe(3);
      });
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject unauthenticated requests', async () => {
      mockAuthenticateToken.mockImplementation((req, res, next) => {
        return res.status(401).json({ message: 'Unauthorized' });
      });
      
      await request(app)
        .get('/api/cart-items')
        .expect(401);
    });

    it('should allow customers to manage their own cart', async () => {
      mockAuthenticateToken.mockImplementation((req, res, next) => {
        req.user = { id: mockUserId, role: UserRole.CUSTOMER };
        next();
      });
      
      mockCartItemModel.findByUserId.mockResolvedValue([mockCartItem]);
      
      await request(app)
        .get('/api/cart-items')
        .set('Authorization', 'Bearer token')
        .expect(200);
    });

    it('should allow admins to access any cart', async () => {
      mockAuthenticateToken.mockImplementation((req, res, next) => {
        req.user = { id: mockAdminId, role: UserRole.ADMIN };
        next();
      });
      
      mockCartItemModel.findByUserId.mockResolvedValue([mockCartItem]);
      
      await request(app)
        .get('/api/cart-items')
        .set('Authorization', 'Bearer token')
        .expect(200);
    });

    it('should prevent customers from accessing other users carts', async () => {
      mockAuthenticateToken.mockImplementation((req, res, next) => {
        req.user = { id: mockUserId, role: UserRole.CUSTOMER };
        next();
      });
      
      // Mock service to check ownership
      jest.spyOn(CartItemService, 'getCartItems').mockImplementation(async (userId) => {
        if (userId !== mockUserId) {
          throw new Error('Unauthorized');
        }
        return [mockCartItem];
      });
      
      await request(app)
        .get(`/api/cart-items?userId=999`) // Trying to access another user's cart'
        .set('Authorization', 'Bearer token')
        .expect(403);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockCartItemModel.findByUserId.mockRejectedValue(new Error('Database connection failed'));
      
      const response = await request(app);
        .get('/api/cart-items')
        .set('Authorization', 'Bearer token')
        .expect(500);
      
      expect(response.body).toEqual({
        message: 'Internal server error'
      });
    });

    it('should handle validation errors', async () => {
      const response = await request(app);
        .post('/api/cart-items')
        .set('Authorization', 'Bearer token')
        .send({
          productId: 1,
          quantity: 'invalid'
        })
        .expect(400);
      
      expect(response.body).toHaveProperty('message');
    });

    it('should handle not found errors', async () => {
      mockCartItemModel.findById.mockResolvedValue(null);
      
      const response = await request(app;