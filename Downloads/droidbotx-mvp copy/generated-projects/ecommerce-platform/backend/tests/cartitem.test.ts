







import { Application } from 'express';
import { createApp } from '../src/app.js';
import { AuthMiddleware } from '../src/middleware/authMiddleware.js';
import { CartItemModel } from '../src/models/cartItemModel.js';
import { CartItemService } from '../src/services/CartItemService';
import { UserRole } from '../src/types/user.js';
import request from 'supertest';

I'll generate comprehensive Jest tests for a CartItem entity. Based on the undefined application context, I'll assume this is for an e-commerce platform with typical cart functionality.


// __tests__/cartItem.test.ts







// Mock dependencies
jest.mock('../src/services/cartItemService');
jest.mock('../src/models/cartItemModel');
jest.mock('../src/middleware/authMiddleware');

describe('CartItem Tests', () => {
  let app: Application;
  let mockCartItemService: jest.Mocked<CartItemService>;
  let mockAuthMiddleware: jest.Mock;

  beforeAll(() => {
    app = createApp();
    mockCartItemService = new CartItemService() as jest.Mocked<CartItemService>;
    mockAuthMiddleware = AuthMiddleware.authenticate as jest.Mock;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', async () => {
      const invalidCartItem = new CartItemModel({
        productId: '',
        quantity: 0,
        userId: '',
      });

      const errors = await invalidCartItem.validateSync();
      expect(errors?.errors).toHaveProperty('productId');
      expect(errors?.errors).toHaveProperty('quantity');
      expect(errors?.errors).toHaveProperty('userId');
    });

    it('should validate quantity is positive', async () => {
      const invalidCartItem = new CartItemModel({
        productId: 'prod123',
        quantity: -1,
        userId: 'user123',
      });

      const errors = await invalidCartItem.validateSync();
      expect(errors?.errors.quantity.message).toBe('Quantity must be at least 1');
    });

    it('should validate valid cart item', async () => {
      const validCartItem = new CartItemModel({
        productId: 'prod123',
        quantity: 2,
        userId: 'user123',
      });

      const errors = await validCartItem.validateSync();
      expect(errors).toBeUndefined();
    });
  });

  describe('Service Layer', () => {
    describe('addItemToCart', () => {
      it('should add item to cart successfully', async () => {
        const cartItemData = {
          productId: 'prod123',
          quantity: 2,
          userId: 'user123',
        };

        const expectedCartItem = {
          id: 'cartItem123',
          ...cartItemData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mockCartItemService.addItemToCart.mockResolvedValue(expectedCartItem);

        const result = await mockCartItemService.addItemToCart(cartItemData);
        
        expect(mockCartItemService.addItemToCart).toHaveBeenCalledWith(cartItemData);
        expect(result).toEqual(expectedCartItem);
      });

      it('should throw error for invalid product', async () => {
        const cartItemData = {
          productId: 'invalid',
          quantity: 2,
          userId: 'user123',
        };

        mockCartItemService.addItemToCart.mockRejectedValue(
          new Error('Product not found')
        );

        await expect(mockCartItemService.addItemToCart(cartItemData))
          .rejects
          .toThrow('Product not found');
      });
    });

    describe('updateCartItem', () => {
      it('should update cart item quantity', async () => {
        const updateData = { quantity: 5 };
        const updatedCartItem = {
          id: 'cartItem123',
          productId: 'prod123',
          quantity: 5,
          userId: 'user123',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mockCartItemService.updateCartItem.mockResolvedValue(updatedCartItem);

        const result = await mockCartItemService.updateCartItem('cartItem123', updateData);
        
        expect(mockCartItemService.updateCartItem).toHaveBeenCalledWith('cartItem123', updateData);
        expect(result).toEqual(updatedCartItem);
      });

      it('should throw error when updating non-existent item', async () => {
        mockCartItemService.updateCartItem.mockRejectedValue(
          new Error('Cart item not found')
        );

        await expect(mockCartItemService.updateCartItem('invalid', { quantity: 5 }))
          .rejects
          .toThrow('Cart item not found');
      });
    });

    describe('removeFromCart', () => {
      it('should remove item from cart', async () => {
        mockCartItemService.removeFromCart.mockResolvedValue(true);

        const result = await mockCartItemService.removeFromCart('cartItem123');
        
        expect(mockCartItemService.removeFromCart).toHaveBeenCalledWith('cartItem123');
        expect(result).toBe(true);
      });

      it('should return false when removing non-existent item', async () => {
        mockCartItemService.removeFromCart.mockResolvedValue(false);

        const result = await mockCartItemService.removeFromCart('invalid');
        
        expect(result).toBe(false);
      });
    });

    describe('getCartItems', () => {
      it('should retrieve all cart items for user', async () => {
        const userId = 'user123';
        const cartItems = [;
          {
            id: 'cartItem1',
            productId: 'prod1',
            quantity: 2,
            userId,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: 'cartItem2',
            productId: 'prod2',
            quantity: 1,
            userId,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        ];

        mockCartItemService.getCartItems.mockResolvedValue(cartItems);

        const result = await mockCartItemService.getCartItems(userId);
        
        expect(mockCartItemService.getCartItems).toHaveBeenCalledWith(userId);
        expect(result).toEqual(cartItems);
      });

      it('should return empty array for user with empty cart', async () => {
        const userId = 'user456';
        mockCartItemService.getCartItems.mockResolvedValue([]);

        const result = await mockCartItemService.getCartItems(userId);
        
        expect(result).toEqual([]);
      });
    });
  });

  describe('API Endpoints', () => {
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      role: UserRole.CUSTOMER,
    };

    beforeEach(() => {
      mockAuthMiddleware.mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });
    });

    describe('POST /api/cart/items', () => {
      it('should add item to cart successfully', async () => {
        const cartItemData = {
          productId: 'prod123',
          quantity: 2,
        };

        const expectedResponse = {
          id: 'cartItem123',
          ...cartItemData,
          userId: 'user123',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        mockCartItemService.addItemToCart.mockResolvedValue(expectedResponse);

        const response = await request(app);
          .post('/api/cart/items')
          .set('Authorization', 'Bearer token123')
          .send(cartItemData)
          .expect(201);

        expect(response.body).toEqual(expectedResponse);
        expect(mockCartItemService.addItemToCart).toHaveBeenCalledWith({
          ...cartItemData,
          userId: 'user123',
        });
      });

      it('should return 400 for invalid request data', async () => {
        const invalidData = {
          productId: '',
          quantity: 0,
        };

        await request(app)
          .post('/api/cart/items')
          .set('Authorization', 'Bearer token123')
          .send(invalidData)
          .expect(400);
      });
    });

    describe('PUT /api/cart/items/:id', () => {
      it('should update cart item successfully', async () => {
        const updateData = { quantity: 3 };
        const updatedItem = {
          id: 'cartItem123',
          productId: 'prod123',
          quantity: 3,
          userId: 'user123',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        mockCartItemService.updateCartItem.mockResolvedValue(updatedItem);

        const response = await request(app);
          .put('/api/cart/items/cartItem123')
          .set('Authorization', 'Bearer token123')
          .send(updateData)
          .expect(200);

        expect(response.body).toEqual(updatedItem);
      });

      it('should return 404 for non-existent cart item', async () => {
        mockCartItemService.updateCartItem.mockRejectedValue(
          new Error('Cart item not found')
        );

        await request(app)
          .put('/api/cart/items/invalid')
          .set('Authorization', 'Bearer token123')
          .send({ quantity: 3 })
          .expect(404);
      });
    });

    describe('DELETE /api/cart/items/:id', () => {
      it('should remove item from cart successfully', async () => {
        mockCartItemService.removeFromCart.mockResolvedValue(true);

        await request(app)
          .delete('/api/cart/items/cartItem123')
          .set('Authorization', 'Bearer token123')
          .expect(204);

        expect(mockCartItemService.removeFromCart).toHaveBeenCalledWith('cartItem123');
      });

      it('should return 404 when item not found', async () => {
        mockCartItemService.removeFromCart.mockResolvedValue(false);

        await request(app)
          .delete('/api/cart/items/invalid')
          .set('Authorization', 'Bearer token123')
          .expect(404);
      });
    });

    describe('GET /api/cart/items', () => {
      it('should retrieve all cart items for user', async () => {
        const cartItems = [;
          {
            id: 'cartItem1',
            productId: 'prod1',
            quantity: 2,
            userId: 'user123',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ];

        mockCartItemService.getCartItems.mockResolvedValue(cartItems);

        const response = await request(app);
          .get('/api/cart/items')
          .set('Authorization', 'Bearer token123')
          .expect(200);

        expect(response.body).toEqual(cartItems);
        expect(mockCartItemService.getCartItems).toHaveBeenCalledWith('user123');
      });
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject requests without authentication', async () => {
      await request(app)
        .post('/api/cart/items')
        .send({ productId: 'prod123', quantity: 2 })
        .expect(401);
    });

    it('should allow authenticated users to access their own cart', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        role: UserRole.CUSTOMER,
      };

      mockAuthMiddleware.mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });

      mockCartItemService.getCartItems.mockResolvedValue([]);

      await request(app)
        .get('/api/cart/items')
        .set('Authorization', 'Bearer token123')
        .expect(200);
    });

    it('should prevent users from accessing other users carts', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        role: UserRole.CUSTOMER,
      };

      mockAuthMiddleware.mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });

      mockCartItemService.updateCartItem.mockRejectedValue(
        new Error('Unauthorized access')
      );

      await request(app)
        .put('/api/cart/items/otherUserCartItem')
        .set('Authorization', 'Bearer token123')
        .send({ quantity: 5 })
        .expect(403);
    });
  });

  describe('Error Handling', () => {
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      role: UserRole.CUSTOMER,
    };

    beforeEach(() => {
      mockAuthMiddleware.mockImplementation((req, res, next) => {
        req.user = mockUser;
        next();
      });
    });

    it('should handle database errors gracefully', async () => {
      mockCartItemService.addItemToCart.mockRejectedValue(
        new Error('Database connection failed')
      );

      await request(app)
        .post('/api/cart/items')
        .set('Authorization', 'Bearer token123')
        .send({ productId: 'prod123', quantity: 2 })
        .expect(500);
    });

    it('should handle validation errors', async () => {
      await request(app)
        .post('/api/cart/items')
        .set('Authorization', 'Bearer token123')
        .send({ productId: '', quantity: -1 })
        .expect(400);
    });

    it('should handle unexpected errors', async () => {
      mockCartItemService.getCartItems.mockRejectedValue(new Error('Unexpected error'));

      const response = await request(app);
        .get('/api/cart/items')
        .set('Authorization', 'Bearer token123')
        .expect(500);

      expect(response.body).toHaveProperty('error');
    });
  });
});
`

This comprehensive test suite covers:

1. **Data Model Validation**:
   - Required field validation
   - Business rule validation (quantity must be positive)
   - Valid data structure testing

2. **Service Layer**:
   - addItemToCart functionality
   - updateCartItem functionality
   - removeFromCart functionality
   - getCartItems functionality
   - Error handling in service methods

3. **API Endpoints**:
   - POST /api/cart/items (add item)