






import { NextFunction, Request, Response } from 'express';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { ProductCategoryService } from './ProductCategoryService.js';
import winston from 'winston';

export class ProductCategoryController {
  private productCategoryService: ProductCategoryService;
  private logger: winston.Logger;

  constructor(pool: Pool, logger: winston.Logger) {
    this.productCategoryService = new ProductCategoryService(pool, logger);
    this.logger = logger;
  }

  async getAllCategories(req: Request, res: Response, next: NextFunction) {
    try {
      this.logger.info('Fetching all product categories');
      const categories = await this.productCategoryService.getAllCategories();
      res.status(200).json({
        success: true,
        data: categories
      });
    } catch (error) {
      this.logger.error('Error fetching product categories', { error });
      next(error);
    }
  }

  async getCategoryById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Category ID is required');
      }

      this.logger.info('Fetching product category by ID', { id });
      const category = await this.productCategoryService.getCategoryById(id);

      if (!category) {
        throw new BusinessError('Category not found', 404);
      }

      res.status(200).json({
        success: true,
        data: category
      });
    } catch (error) {
      this.logger.error('Error fetching product category by ID', { error });
      next(error);
    }
  }

  async createCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const { name, description, parentId } = req.body;

      if (!name) {
        throw new ValidationError('Category name is required');
      }

      this.logger.info('Creating new product category', { name });
      const category = await this.productCategoryService.createCategory({
        name,
        description,
        parentId
      });

      res.status(201).json({
        success: true,
        data: category
      });
    } catch (error) {
      this.logger.error('Error creating product category', { error });
      next(error);
    }
  }

  async updateCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { name, description, parentId } = req.body;

      if (!id) {
        throw new ValidationError('Category ID is required');
      }

      this.logger.info('Updating product category', { id });
      const category = await this.productCategoryService.updateCategory(id, {
        name,
        description,
        parentId
      });

      if (!category) {
        throw new BusinessError('Category not found', 404);
      }

      res.status(200).json({
        success: true,
        data: category
      });
    } catch (error) {
      this.logger.error('Error updating product category', { error });
      next(error);
    }
  }

  async deleteCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Category ID is required');
      }

      this.logger.info('Deleting product category', { id });
      const result = await this.productCategoryService.deleteCategory(id);

      if (!result) {
        throw new BusinessError('Category not found', 404);
      }

      res.status(200).json({
        success: true,
        message: 'Category deleted successfully'
      });
    } catch (error) {
      this.logger.error('Error deleting product category', { error });
      next(error);
    }
  }
}