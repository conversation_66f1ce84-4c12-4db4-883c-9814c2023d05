





import { NextFunction, Request, Response } from 'express';
import { AppError } from '../middleware/errorHandler.js';
import { Product } from '../models/Product.js';
import { ProductService } from '../services/ProductService.js';
import { logger } from '../utils/logger.js';

export class ProductController {
  private productService: ProductService;

  constructor(productService: ProductService) {
    this.productService = productService;
  }

  /**
   * Get all products
   */
  getAllProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      logger.info('Fetching all products');

      const products = await this.productService.getAllProducts();

      res.status(200).json({
        success: true,
        data: products,
        count: products.length
      });
    } catch (error) {
      logger.error('Error fetching all products:', error);
      next(error);
    }
  };

  /**
   * Get product by ID
   */
  getProductById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const productId = parseInt(req.params.id, 10);

      if (isNaN(productId)) {
        throw new AppError('Invalid product ID', 400);
      }

      logger.info(`Fetching product with ID: ${productId}`);

      const product = await this.productService.getProductById(productId);

      if (!product) {
        throw new AppError('Product not found', 404);
      }

      res.status(200).json({
        success: true,
        data: product
      });
    } catch (error) {
      logger.error(`Error fetching product by ID:`, error);
      next(error);
    }
  };

  /**
   * Create a new product
   */
  createProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const productData: Product = req.body;

      logger.info('Creating new product', { productData });

      // Validate required fields
      if (!productData.name || !productData.price) {
        throw new AppError('Name and price are required', 400);
      }

      const newProduct = await this.productService.createProduct(productData);

      res.status(201).json({
        success: true,
        data: newProduct
      });
    } catch (error) {
      logger.error('Error creating product:', error);
      next(error);
    }
  };

  /**
   * Update product
   */
  updateProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const productId = parseInt(req.params.id, 10);
      const productData: Partial<Product> = req.body;

      if (isNaN(productId)) {
        throw new AppError('Invalid product ID', 400);
      }

      logger.info(`Updating product with ID: ${productId}`, { productData });

      const updatedProduct = await this.productService.updateProduct(productId, productData);

      if (!updatedProduct) {
        throw new AppError('Product not found', 404);
      }

      res.status(200).json({
        success: true,
        data: updatedProduct
      });
    } catch (error) {
      logger.error('Error updating product:', error);
      next(error);
    }
  };

  /**
   * Delete product
   */
  deleteProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const productId = parseInt(req.params.id, 10);

      if (isNaN(productId)) {
        throw new AppError('Invalid product ID', 400);
      }

      logger.info(`Deleting product with ID: ${productId}`);

      const deleted = await this.productService.deleteProduct(productId);

      if (!deleted) {
        throw new AppError('Product not found', 404);
      }

      res.status(200).json({
        success: true,
        message: 'Product deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting product:', error);
      next(error);
    }
  };
}