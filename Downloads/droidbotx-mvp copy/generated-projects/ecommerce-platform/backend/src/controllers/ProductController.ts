






import { NextFunction, Request, Response } from 'express';
import { NotFoundError } from '../errors/NotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Product } from '../models/Product.js';
import { Logger } from '../utils/Logger.js';
import { ProductService } from './ProductService.js';

export class ProductController {
  private productService: ProductService;
  private logger: Logger;

  constructor(productService: ProductService, logger: Logger) {
    this.productService = productService;
    this.logger = logger;
  }

  getAllProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Fetching all products');
      const products = await this.productService.getAllProducts();
      res.status(200).json({
        success: true,
        data: products
      });
    } catch (error) {
      this.logger.error('Error fetching all products', error);
      next(error);
    }
  };

  getProductById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Product ID is required');
      }

      this.logger.info(`Fetching product with ID: ${id}`);
      const product = await this.productService.getProductById(id);

      if (!product) {
        throw new NotFoundError(`Product with ID ${id} not found`);
      }

      res.status(200).json({
        success: true,
        data: product
      });
    } catch (error) {
      this.logger.error(`Error fetching product with ID: ${req.params.id}`, error);
      next(error);
    }
  };

  createProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const productData: Partial<Product> = req.body;

      this.logger.info('Creating new product', productData);
      const product = await this.productService.createProduct(productData);

      res.status(201).json({
        success: true,
        data: product
      });
    } catch (error) {
      this.logger.error('Error creating product', error);
      next(error);
    }
  };

  updateProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const productData: Partial<Product> = req.body;

      if (!id) {
        throw new ValidationError('Product ID is required');
      }

      this.logger.info(`Updating product with ID: ${id}`, productData);
      const product = await this.productService.updateProduct(id, productData);

      res.status(200).json({
        success: true,
        data: product
      });
    } catch (error) {
      this.logger.error(`Error updating product with ID: ${req.params.id}`, error);
      next(error);
    }
  };

  deleteProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Product ID is required');
      }

      this.logger.info(`Deleting product with ID: ${id}`);
      await this.productService.deleteProduct(id);

      res.status(204).send();
    } catch (error) {
      this.logger.error(`Error deleting product with ID: ${req.params.id}`, error);
      next(error);
    }
  };
}