






import { NextFunction, Request, Response } from 'express';
import { NotFoundError } from '../errors/NotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { User } from '../models/User.js';
import { UserService } from '../services/UserService.js';
import { Logger } from '../utils/Logger.js';

export class UserController {
  private userService: UserService;
  private logger: Logger;

  constructor(userService: UserService) {
    this.userService = userService;
    this.logger = new Logger('UserController');
  }

  public getAllUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Fetching all users');
      const users: User[] = await this.userService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users,
        count: users.length
      });
    } catch (error) {
      this.logger.error('Error fetching all users', error);
      next(error);
    }
  };

  public getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId: number = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Fetching user with ID: ${userId}`);
      const user: User | null = await this.userService.getUserById(userId);

      if (!user) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`Error fetching user with ID: ${req.params.id}`, error);
      next(error);
    }
  };

  public createUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Creating new user');

      // Validate request body
      const userData: Partial<User> = req.body;
      if (!userData.email || !userData.firstName || !userData.lastName) {
        throw new ValidationError('Missing required fields: email, firstName, lastName');
      }

      const newUser: User = await this.userService.createUser(userData);

      res.status(201).json({
        success: true,
        data: newUser,
        message: 'User created successfully'
      });
    } catch (error) {
      this.logger.error('Error creating user', error);
      next(error);
    }
  };

  public updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId: number = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Updating user with ID: ${userId}`);

      const userData: Partial<User> = req.body;
      const updatedUser: User = await this.userService.updateUser(userId, userData);

      res.status(200).json({
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      });
    } catch (error) {
      this.logger.error(`Error updating user with ID: ${req.params.id}`, error);
      next(error);
    }
  };

  public deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId: number = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Deleting user with ID: ${userId}`);
      await this.userService.deleteUser(userId);

      res.status(200).json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      this.logger.error(`Error deleting user with ID: ${req.params.id}`, error);
      next(error);
    }
  };

  public getUserProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Assuming user ID is available in request (from auth middleware)
      const userId: number = (req as any).user.id;

      this.logger.info(`Fetching profile for user ID: ${userId}`);
      const user: User | null = await this.userService.getUserById(userId);

      if (!user) {
        throw new NotFoundError('User profile not found');
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error('Error fetching user profile', error);
      next(error);
    }
  };
}