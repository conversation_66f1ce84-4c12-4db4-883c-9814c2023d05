





import { NextFunction, Request, Response } from 'express';
import { ValidationError } from '../errors/ValidationError.js';
import { User } from '../models/User.js';
import { UserService } from '../services/UserService.js';
import { Logger } from '../utils/Logger.js';

export class UserController {
  private userService: UserService;
  private logger: Logger;

  constructor(userService: UserService, logger: Logger) {
    this.userService = userService;
    this.logger = logger;
  }

  getAllUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Fetching all users');
      const users = await this.userService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users,
        message: 'Users retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Error fetching users', { error });
      next(error);
    }
  };

  getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      this.logger.info(`Fetching user with ID: ${id}`);
      const user = await this.userService.getUserById(id);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user,
        message: 'User retrieved successfully'
      });
    } catch (error) {
      this.logger.error(`Error fetching user with ID: ${req.params.id}`, { error });
      next(error);
    }
  };

  createUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userData: Partial<User> = req.body;

      this.logger.info('Creating new user', { userData });
      const user = await this.userService.createUser(userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
      });
    } catch (error) {
      this.logger.error('Error creating user', { error });
      next(error);
    }
  };

  updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userData: Partial<User> = req.body;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      this.logger.info(`Updating user with ID: ${id}`, { userData });
      const user = await this.userService.updateUser(id, userData);

      res.status(200).json({
        success: true,
        data: user,
        message: 'User updated successfully'
      });
    } catch (error) {
      this.logger.error(`Error updating user with ID: ${req.params.id}`, { error });
      next(error);
    }
  };

  deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      this.logger.info(`Deleting user with ID: ${id}`);
      await this.userService.deleteUser(id);

      res.status(200).json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      this.logger.error(`Error deleting user with ID: ${req.params.id}`, { error });
      next(error);
    }
  };
}