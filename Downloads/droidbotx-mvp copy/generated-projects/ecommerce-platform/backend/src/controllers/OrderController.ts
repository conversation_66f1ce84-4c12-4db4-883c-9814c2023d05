





import { NextFunction, Request, Response } from 'express';
import { OrderNotFoundError } from '../errors/OrderNotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { OrderService } from '../services/OrderService.js';
import { Logger } from '../utils/Logger.js';

export class OrderController {
  private orderService: OrderService;
  private logger: Logger;

  constructor(orderService: OrderService, logger: Logger) {
    this.orderService = orderService;
    this.logger = logger;
  }

  createOrder = async (req: Request, res: Response, next: NextFunction) => {
    try {
      this.logger.info('Creating new order', { userId: req.user?.id });

      const order = await this.orderService.createOrder({
        userId: req.user!.id,
        items: req.body.items,
        shippingAddress: req.body.shippingAddress,
        paymentMethod: req.body.paymentMethod
      });

      this.logger.info('Order created successfully', { orderId: order.id });
      res.status(201).json({
        success: true,
        data: order
      });
    } catch (error) {
      this.logger.error('Failed to create order', { error });

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          error: {
            type: 'VALIDATION_ERROR',
            message: error.message,
            details: error.details
          }
        });
      }

      next(error);
    }
  };

  getOrderById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      this.logger.info('Fetching order by ID', { orderId: id, userId: req.user?.id });

      const order = await this.orderService.getOrderById(id);

      if (!order) {
        throw new OrderNotFoundError(id);
      }

      // Check if user has permission to access this order
      if (order.userId !== req.user!.id && !req.user?.isAdmin) {
        return res.status(403).json({
          success: false,
          error: {
            type: 'FORBIDDEN',
            message: 'You do not have permission to access this order'
          }
        });
      }

      this.logger.info('Order fetched successfully', { orderId: id });
      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      this.logger.error('Failed to fetch order', { error });

      if (error instanceof OrderNotFoundError) {
        return res.status(404).json({
          success: false,
          error: {
            type: 'ORDER_NOT_FOUND',
            message: error.message
          }
        });
      }

      next(error);
    }
  };

  getUserOrders = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user!.id;
      const { page = 1, limit = 10, status } = req.query;

      this.logger.info('Fetching user orders', { userId, page, limit, status });

      const orders = await this.orderService.getUserOrders(userId, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string
      });

      this.logger.info('User orders fetched successfully', { userId, count: orders.length });
      res.json({
        success: true,
        data: orders
      });
    } catch (error) {
      this.logger.error('Failed to fetch user orders', { error });
      next(error);
    }
  };

  updateOrderStatus = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      this.logger.info('Updating order status', { orderId: id, status, userId: req.user?.id });

      // Only admins can update order status
      if (!req.user?.isAdmin) {
        return res.status(403).json({
          success: false,
          error: {
            type: 'FORBIDDEN',
            message: 'Only administrators can update order status'
          }
        });
      }

      const order = await this.orderService.updateOrderStatus(id, status);

      this.logger.info('Order status updated successfully', { orderId: id, status });
      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      this.logger.error('Failed to update order status', { error });

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          error: {
            type: 'VALIDATION_ERROR',
            message: error.message,
            details: error.details
          }
        });
      }

      if (error instanceof OrderNotFoundError) {
        return res.status(404).json({
          success: false,
          error: {
            type: 'ORDER_NOT_FOUND',
            message: error.message
          }
        });
      }

      next(error);
    }
  };

  cancelOrder = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      this.logger.info('Cancelling order', { orderId: id, userId: req.user?.id });

      const order = await this.orderService.cancelOrder(id, req.user!.id);

      this.logger.info('Order cancelled successfully', { orderId: id });
      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      this.logger.error('Failed to cancel order', { error });

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          error: {
            type: 'VALIDATION_ERROR',
            message: error.message,
            details: error.details
          }
        });
      }

      if (error instanceof OrderNotFoundError) {
        return res.status(404).json({
          success: false,
          error: {
            type: 'ORDER_NOT_FOUND',
            message: error.message
          }
        });
      }

      next(error);
    }
  };
}