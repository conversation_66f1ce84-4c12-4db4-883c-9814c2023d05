





import { Request, Response, Router } from 'express';
import { ValidationError } from '../errors/ValidationError.js';
import { OrderItem } from '../models/OrderItem.js';
import { OrderItemService } from '../services/OrderItemService.js';
import { Logger } from '../utils/Logger.js';

export class OrderItemController {
  private router: Router;
  private orderItemService: OrderItemService;
  private logger: Logger;

  constructor(orderItemService: OrderItemService, logger: Logger) {
    this.router = Router();
    this.orderItemService = orderItemService;
    this.logger = logger;
    this.setupRoutes();
  }

  private setupRoutes(): void {
    this.router.get('/', this.getAllOrderItems.bind(this));
    this.router.get('/:id', this.getOrderItemById.bind(this));
    this.router.post('/', this.createOrderItem.bind(this));
    this.router.put('/:id', this.updateOrderItem.bind(this));
    this.router.delete('/:id', this.deleteOrderItem.bind(this));
  }

  public getRouter(): Router {
    return this.router;
  }

  private async getAllOrderItems(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('Fetching all order items');
      const orderItems = await this.orderItemService.getAllOrderItems();
      res.status(200).json({
        success: true,
        data: orderItems
      });
    } catch (error) {
      this.logger.error('Error fetching order items:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch order items'
      });
    }
  }

  private async getOrderItemById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Order item ID is required');
      }

      this.logger.info(`Fetching order item with ID: ${id}`);
      const orderItem = await this.orderItemService.getOrderItemById(id);

      if (!orderItem) {
        res.status(404).json({
          success: false,
          message: 'Order item not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: orderItem
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          message: error.message
        });
        return;
      }

      this.logger.error('Error fetching order item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch order item'
      });
    }
  }

  private async createOrderItem(req: Request, res: Response): Promise<void> {
    try {
      const orderItemData: Partial<OrderItem> = req.body;

      this.logger.info('Creating new order item', orderItemData);
      const orderItem = await this.orderItemService.createOrderItem(orderItemData);

      res.status(201).json({
        success: true,
        data: orderItem
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          message: error.message
        });
        return;
      }

      this.logger.error('Error creating order item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create order item'
      });
    }
  }

  private async updateOrderItem(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const orderItemData: Partial<OrderItem> = req.body;

      if (!id) {
        throw new ValidationError('Order item ID is required');
      }

      this.logger.info(`Updating order item with ID: ${id}`, orderItemData);
      const orderItem = await this.orderItemService.updateOrderItem(id, orderItemData);

      res.status(200).json({
        success: true,
        data: orderItem
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          message: error.message
        });
        return;
      }

      this.logger.error('Error updating order item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order item'
      });
    }
  }

  private async deleteOrderItem(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Order item ID is required');
      }

      this.logger.info(`Deleting order item with ID: ${id}`);
      await this.orderItemService.deleteOrderItem(id);

      res.status(200).json({
        success: true,
        message: 'Order item deleted successfully'
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          message: error.message
        });
        return;
      }

      this.logger.error('Error deleting order item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete order item'
      });
    }
  }
}