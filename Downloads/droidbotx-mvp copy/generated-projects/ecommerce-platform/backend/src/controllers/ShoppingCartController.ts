





import { NextFunction, Request, Response } from 'express';
import { BusinessError } from '../errors/BusinessError.js';
import { ShoppingCart } from '../models/ShoppingCart.js';
import { ShoppingCartService } from '../services/ShoppingCartService.js';
import { Logger } from '../utils/Logger.js';

export class ShoppingCartController {
  private shoppingCartService: ShoppingCartService;
  private logger: Logger;

  constructor(shoppingCartService: ShoppingCartService, logger: Logger) {
    this.shoppingCartService = shoppingCartService;
    this.logger = logger;
  }

  async getCart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      this.logger.info(`Fetching shopping cart for user ${userId}`);

      const cart = await this.shoppingCartService.getCart(userId);

      res.status(200).json({
        success: true,
        data: cart
      });
    } catch (error) {
      this.logger.error('Error fetching shopping cart', error);
      next(error);
    }
  }

  async addItem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;
      const { productId, quantity } = req.body;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      if (!productId || !quantity) {
        throw new BusinessError('Product ID and quantity are required', 400);
      }

      this.logger.info(`Adding item to cart for user ${userId}`, { productId, quantity });

      const updatedCart = await this.shoppingCartService.addItem(userId, productId, quantity);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error adding item to cart', error);
      next(error);
    }
  }

  async removeItem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;
      const { productId } = req.body;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      if (!productId) {
        throw new BusinessError('Product ID is required', 400);
      }

      this.logger.info(`Removing item from cart for user ${userId}`, { productId });

      const updatedCart = await this.shoppingCartService.removeItem(userId, productId);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error removing item from cart', error);
      next(error);
    }
  }

  async updateItemQuantity(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;
      const { productId, quantity } = req.body;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      if (!productId || quantity === undefined) {
        throw new BusinessError('Product ID and quantity are required', 400);
      }

      if (quantity < 0) {
        throw new BusinessError('Quantity cannot be negative', 400);
      }

      this.logger.info(`Updating item quantity in cart for user ${userId}`, { productId, quantity });

      const updatedCart = await this.shoppingCartService.updateItemQuantity(userId, productId, quantity);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error updating item quantity in cart', error);
      next(error);
    }
  }

  async clearCart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      this.logger.info(`Clearing cart for user ${userId}`);

      await this.shoppingCartService.clearCart(userId);

      res.status(200).json({
        success: true,
        message: 'Cart cleared successfully'
      });
    } catch (error) {
      this.logger.error('Error clearing cart', error);
      next(error);
    }
  }

  async getCartTotal(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.userId;

      if (!userId) {
        throw new BusinessError('User ID is required', 400);
      }

      this.logger.info(`Calculating cart total for user ${userId}`);

      const total = await this.shoppingCartService.getCartTotal(userId);

      res.status(200).json({
        success: true,
        data: { total }
      });
    } catch (error) {
      this.logger.error('Error calculating cart total', error);
      next(error);
    }
  }
}