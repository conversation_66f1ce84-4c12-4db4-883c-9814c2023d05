





import { NextFunction, Request, Response } from 'express';
import { BusinessError } from '../errors/BusinessError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { ShoppingCartService } from '../services/ShoppingCartService.js';
import { Logger } from '../utils/Logger.js';

export class ShoppingCartController {
  private shoppingCartService: ShoppingCartService;
  private logger: Logger;

  constructor(shoppingCartService: ShoppingCartService, logger: Logger) {
    this.shoppingCartService = shoppingCartService;
    this.logger = logger;
  }

  /**
   * Get shopping cart by ID
   */
  getCart = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      // Validate input
      if (!id) {
        throw new ValidationError('Cart ID is required');
      }

      this.logger.info('Fetching shopping cart', { cartId: id });

      const cart = await this.shoppingCartService.getCartById(id);

      if (!cart) {
        throw new BusinessError('Shopping cart not found', 404);
      }

      res.status(200).json({
        success: true,
        data: cart
      });
    } catch (error) {
      this.logger.error('Error fetching shopping cart', { error });
      next(error);
    }
  };

  /**
   * Create a new shopping cart
   */
  createCart = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.body;

      // Validate input
      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      this.logger.info('Creating new shopping cart', { userId });

      const cart = await this.shoppingCartService.createCart(userId);

      res.status(201).json({
        success: true,
        data: cart
      });
    } catch (error) {
      this.logger.error('Error creating shopping cart', { error });
      next(error);
    }
  };

  /**
   * Add item to shopping cart
   */
  addItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId } = req.params;
      const { productId, quantity } = req.body;

      // Validate inputs
      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!productId) {
        throw new ValidationError('Product ID is required');
      }

      if (!quantity || quantity <= 0) {
        throw new ValidationError('Quantity must be a positive number');
      }

      this.logger.info('Adding item to cart', { cartId, productId, quantity });

      const updatedCart = await this.shoppingCartService.addItemToCart(cartId, productId, quantity);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error adding item to cart', { error });
      next(error);
    }
  };

  /**
   * Remove item from shopping cart
   */
  removeItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId, productId } = req.params;

      // Validate inputs
      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!productId) {
        throw new ValidationError('Product ID is required');
      }

      this.logger.info('Removing item from cart', { cartId, productId });

      const updatedCart = await this.shoppingCartService.removeItemFromCart(cartId, productId);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error removing item from cart', { error });
      next(error);
    }
  };

  /**
   * Update item quantity in shopping cart
   */
  updateItemQuantity = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId, productId } = req.params;
      const { quantity } = req.body;

      // Validate inputs
      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!productId) {
        throw new ValidationError('Product ID is required');
      }

      if (quantity === undefined || quantity < 0) {
        throw new ValidationError('Quantity must be a non-negative number');
      }

      this.logger.info('Updating item quantity in cart', { cartId, productId, quantity });

      const updatedCart = await this.shoppingCartService.updateItemQuantity(cartId, productId, quantity);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error updating item quantity', { error });
      next(error);
    }
  };

  /**
   * Clear shopping cart
   */
  clearCart = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId } = req.params;

      // Validate input
      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      this.logger.info('Clearing shopping cart', { cartId });

      const updatedCart = await this.shoppingCartService.clearCart(cartId);

      res.status(200).json({
        success: true,
        data: updatedCart
      });
    } catch (error) {
      this.logger.error('Error clearing shopping cart', { error });
      next(error);
    }
  };

  /**
   * Get cart summary
   */
  getCartSummary = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId } = req.params;

      // Validate input
      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      this.logger.info('Fetching cart summary', { cartId });

      const summary = await this.shoppingCartService.getCartSummary(cartId);

      res.status(200).json({
        success: true,
        data: summary
      });
    } catch (error) {
      this.logger.error('Error fetching cart summary', { error });
      next(error);
    }
  };
}