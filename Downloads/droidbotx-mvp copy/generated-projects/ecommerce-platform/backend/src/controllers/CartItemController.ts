





import { NextFunction, Request, Response } from 'express';
import { NotFoundError } from '../errors/NotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { CartItemService } from '../services/CartItemService.js';
import { Logger } from '../utils/Logger.js';

export class CartItemController {
  private cartItemService: CartItemService;
  private logger: Logger;

  constructor(cartItemService: CartItemService, logger: Logger) {
    this.cartItemService = cartItemService;
    this.logger = logger;
  }

  getCartItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Cart item ID is required');
      }

      this.logger.info(`Fetching cart item with ID: ${id}`);
      const cartItem = await this.cartItemService.getCartItemById(id);

      if (!cartItem) {
        throw new NotFoundError(`Cart item with ID ${id} not found`);
      }

      res.status(200).json({
        success: true,
        data: cartItem
      });
    } catch (error) {
      this.logger.error('Error fetching cart item', { error });
      next(error);
    }
  };

  getCartItems = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId } = req.params;

      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      this.logger.info(`Fetching cart items for cart ID: ${cartId}`);
      const cartItems = await this.cartItemService.getCartItemsByCartId(cartId);

      res.status(200).json({
        success: true,
        data: cartItems
      });
    } catch (error) {
      this.logger.error('Error fetching cart items', { error });
      next(error);
    }
  };

  createCartItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { cartId, productId, quantity, price } = req.body;

      // Validate required fields
      if (!cartId || !productId || !quantity || price === undefined) {
        throw new ValidationError('Cart ID, Product ID, Quantity, and Price are required');
      }

      this.logger.info('Creating new cart item', { cartId, productId, quantity, price });
      const cartItem = await this.cartItemService.createCartItem({
        cartId,
        productId,
        quantity,
        price
      });

      res.status(201).json({
        success: true,
        data: cartItem
      });
    } catch (error) {
      this.logger.error('Error creating cart item', { error });
      next(error);
    }
  };

  updateCartItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const { quantity, price } = req.body;

      if (!id) {
        throw new ValidationError('Cart item ID is required');
      }

      if (quantity === undefined && price === undefined) {
        throw new ValidationError('At least one of quantity or price must be provided');
      }

      this.logger.info(`Updating cart item with ID: ${id}`, { quantity, price });
      const cartItem = await this.cartItemService.updateCartItem(id, { quantity, price });

      res.status(200).json({
        success: true,
        data: cartItem
      });
    } catch (error) {
      this.logger.error('Error updating cart item', { error });
      next(error);
    }
  };

  deleteCartItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Cart item ID is required');
      }

      this.logger.info(`Deleting cart item with ID: ${id}`);
      await this.cartItemService.deleteCartItem(id);

      res.status(200).json({
        success: true,
        message: 'Cart item deleted successfully'
      });
    } catch (error) {
      this.logger.error('Error deleting cart item', { error });
      next(error);
    }
  };
}