// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { Order, OrderStatus } from '../models/Order.js';
import { OrderRepository } from '../repositories/OrderRepository.js';
import { Logger } from '../utils/Logger.js';

export class OrderService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class OrderService {
  private orderRepository: OrderRepository;
  private logger: Logger;

  constructor(pool: Pool) {
    this.orderRepository = new OrderRepository(pool);
    this.logger = new Logger('OrderService');
  }

  async getOrderById(orderId: string): Promise<Order> {
    try {
      this.logger.info(`Fetching order with ID: ${orderId}`);

      if (!orderId) {
        throw new BusinessError('Order ID is required', 'INVALID_INPUT');
      }

      const order = await this.orderRepository.findById(orderId);

      if (!order) {
        throw new BusinessError(`Order with ID ${orderId} not found`, 'NOT_FOUND');
      }

      this.logger.info(`Successfully retrieved order ${orderId}`);
      return order;
    } catch (error) {
      this.logger.error(`Error fetching order ${orderId}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to retrieve order', 'DATABASE_ERROR');
    }
  }

  async getOrdersByCustomerId customerId: string): Promise<Order[]> {
    try {
      this.logger.info(`Fetching orders for customer ID: ${customerId}`);

      if (!customerId) {
        throw new BusinessError('Customer ID is required', 'INVALID_INPUT');
      }

      const orders = await this.orderRepository.findByCustomerId(customerId);

      this.logger.info(`Successfully retrieved ${orders.length} orders for customer ${customerId}`);
      return orders;
    } catch (error) {
      this.logger.error(`Error fetching orders for customer ${customerId}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to retrieve orders', 'DATABASE_ERROR');
    }
  }

  async createOrder(orderData: Partial<Order>): Promise<Order> {
    try {
      this.logger.info('Creating new order');

      // Validate required fields
      if (!orderData.customerId) {
        throw new BusinessError('Customer ID is required', 'INVALID_INPUT');
      }

      if (!orderData.items || orderData.items.length === 0) {
        throw new BusinessError('Order must contain at least one item', 'INVALID_INPUT');
      }

      // Set default values
      const newOrder: Order = {
        id: '',
        customerId: orderData.customerId,
        items: orderData.items,
        status: OrderStatus.PENDING,
        totalAmount: orderData.totalAmount || 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Validate business rules
      this.validateOrder(newOrder);

      const createdOrder = await this.orderRepository.create(newOrder);

      this.logger.info(`Successfully created order ${createdOrder.id}`);
      return createdOrder;
    } catch (error) {
      this.logger.error('Error creating order:', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create order', 'DATABASE_ERROR');
    }
  }

  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<Order> {
    try {
      this.logger.info(`Updating order ${orderId} status to ${status}`);

      if (!orderId) {
        throw new BusinessError('Order ID is required', 'INVALID_INPUT');
      }

      if (!status) {
        throw new BusinessError('Status is required', 'INVALID_INPUT');
      }

      // Validate status transition
      const currentOrder = await this.getOrderById(orderId);

      if (!this.isValidStatusTransition(currentOrder.status, status)) {
        throw new BusinessError(
          `Invalid status transition from ${currentOrder.status} to ${status}`,
          'INVALID_OPERATION'
        );
      }

      const updatedOrder = await this.orderRepository.updateStatus(orderId, status);

      this.logger.info(`Successfully updated order ${orderId} status to ${status}`);
      return updatedOrder;
    } catch (error) {
      this.logger.error(`Error updating order ${orderId} status:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update order status', 'DATABASE_ERROR');
    }
  }

  async cancelOrder(orderId: string): Promise<Order> {
    try {
      this.logger.info(`Cancelling order ${orderId}`);

      if (!orderId) {
        throw new BusinessError('Order ID is required', 'INVALID_INPUT');
      }

      const order = await this.getOrderById(orderId);

      // Check if order can be cancelled
      if (order.status === OrderStatus.COMPLETED || order.status === OrderStatus.CANCELLED) {
        throw new BusinessError(
          `Cannot cancel order with status ${order.status}`,
          'INVALID_OPERATION'
        );
      }

      const updatedOrder = await this.orderRepository.updateStatus(orderId, OrderStatus.CANCELLED);

      this.logger.info(`Successfully cancelled order ${orderId}`);
      return updatedOrder;
    } catch (error) {
      this.logger.error(`Error cancelling order ${orderId}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to cancel order', 'DATABASE_ERROR');
    }
  }

  private validateOrder(order: Order): void {
    // Validate total amount calculation
    const calculatedTotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    if (Math.abs(calculatedTotal - order.totalAmount) > 0.01) {
      throw new BusinessError('Total amount does not match item sum', 'INVALID_INPUT');
    }

    // Validate item quantities
    for (const item of order.items) {
      if (item.quantity <= 0) {
        throw new BusinessError(`Item quantity must be positive for item ${item.productId}`, 'INVALID_INPUT');
      }
    }
  }

  private isValidStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): boolean {
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.IN_PROGRESS, OrderStatus.CANCELLED],
      [OrderStatus.IN_PROGRESS]: [OrderStatus.COMPLETED, OrderStatus.CANCELLED],
      [OrderStatus.COMPLETED]: [],
      [OrderStatus.CANCELLED]: []
    };

    return validTransitions[currentStatus].includes(newStatus);
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'OrderService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'OrderService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default OrderService;
}