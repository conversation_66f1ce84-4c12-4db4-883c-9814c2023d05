// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Product } from '../models/Product.js';
import { ProductRepository } from '../repositories/ProductRepository.js';
import { Logger } from '../utils/Logger.js';

export class ProductService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }








export interface ProductServiceInterface {
  getAllProducts(): Promise<Product[]>;
  getProductById(id: number): Promise<Product | null>;
  createProduct(product: Product): Promise<Product>;
  updateProduct(id: number, product: Product): Promise<Product>;
  deleteProduct(id: number): Promise<boolean>;
}

export class ProductService implements ProductServiceInterface {
  private productRepository: ProductRepository;
  private logger: Logger;

  constructor(pool: Pool) {
    this.productRepository = new ProductRepository(pool);
    this.logger = new Logger('ProductService');
  }

  async getAllProducts(): Promise<Product[]> {
    try {
      this.logger.info('Fetching all products');
      const products = await this.productRepository.findAll();
      this.logger.info(`Retrieved ${products.length} products`);
      return products;
    } catch (error) {
      this.logger.error('Error fetching all products', error);
      throw new BusinessError('Failed to retrieve products');
    }
  }

  async getProductById(id: number): Promise<Product | null> {
    try {
      this.logger.info(`Fetching product with ID: ${id}`);

      if (!id || id <= 0) {
        throw new ValidationError('Product ID must be a positive number');
      }

      const product = await this.productRepository.findById(id);

      if (!product) {
        this.logger.warn(`Product with ID ${id} not found`);
        return null;
      }

      this.logger.info(`Product with ID ${id} retrieved successfully`);
      return product;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error(`Error fetching product with ID ${id}`, error);
      throw new BusinessError('Failed to retrieve product');
    }
  }

  async createProduct(product: Product): Promise<Product> {
    try {
      this.logger.info('Creating new product');

      // Business rule validation
      this.validateProduct(product);

      // Check if product with same SKU already exists
      if (product.sku) {
        const existingProduct = await this.productRepository.findBySku(product.sku);
        if (existingProduct) {
          throw new ValidationError(`Product with SKU ${product.sku} already exists`);
        }
      }

      const createdProduct = await this.productRepository.create(product);
      this.logger.info(`Product created successfully with ID: ${createdProduct.id}`);
      return createdProduct;
    } catch (error) {
      if (error instanceof ValidationError) {
        this.logger.warn('Product validation failed', error.message);
        throw error;
      }

      this.logger.error('Error creating product', error);
      throw new BusinessError('Failed to create product');
    }
  }

  async updateProduct(id: number, product: Product): Promise<Product> {
    try {
      this.logger.info(`Updating product with ID: ${id}`);

      if (!id || id <= 0) {
        throw new ValidationError('Product ID must be a positive number');
      }

      // Business rule validation
      this.validateProduct(product);

      // Check if product exists
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new ValidationError(`Product with ID ${id} does not exist`);
      }

      // Check if another product with same SKU exists
      if (product.sku && product.sku !== existingProduct.sku) {
        const existingProductWithSku = await this.productRepository.findBySku(product.sku);
        if (existingProductWithSku) {
          throw new ValidationError(`Product with SKU ${product.sku} already exists`);
        }
      }

      const updatedProduct = await this.productRepository.update(id, product);
      this.logger.info(`Product with ID ${id} updated successfully`);
      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationError) {
        this.logger.warn(`Product update validation failed for ID ${id}`, error.message);
        throw error;
      }

      this.logger.error(`Error updating product with ID ${id}`, error);
      throw new BusinessError('Failed to update product');
    }
  }

  async deleteProduct(id: number): Promise<boolean> {
    try {
      this.logger.info(`Deleting product with ID: ${id}`);

      if (!id || id <= 0) {
        throw new ValidationError('Product ID must be a positive number');
      }

      // Check if product exists
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new ValidationError(`Product with ID ${id} does not exist`);
      }

      const result = await this.productRepository.delete(id);
      this.logger.info(`Product with ID ${id} deleted successfully`);
      return result;
    } catch (error) {
      if (error instanceof ValidationError) {
        this.logger.warn(`Product deletion validation failed for ID ${id}`, error.message);
        throw error;
      }

      this.logger.error(`Error deleting product with ID ${id}`, error);
      throw new BusinessError('Failed to delete product');
    }
  }

  private validateProduct(product: Product): void {
    const errors: string[] = [];

    if (!product.name || product.name.trim().length === 0) {
      errors.push('Product name is required');
    }

    if (product.name && product.name.length > 255) {
      errors.push('Product name must not exceed 255 characters');
    }

    if (product.description && product.description.length > 1000) {
      errors.push('Product description must not exceed 1000 characters');
    }

    if (product.price !== undefined && (isNaN(product.price) || product.price < 0)) {
      errors.push('Product price must be a non-negative number');
    }

    if (product.sku && product.sku.length > 50) {
      errors.push('Product SKU must not exceed 50 characters');
    }

    if (product.category && product.category.length > 100) {
      errors.push('Product category must not exceed 100 characters');
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'ProductService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'ProductService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default ProductService;
}