// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { Product } from '../models/Product.js';
import { ProductRepository } from '../repositories/ProductRepository.js';
import { Logger } from '../utils/Logger.js';

export class ProductService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class ProductService {
  private productRepository: ProductRepository;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.productRepository = new ProductRepository(pool);
    this.logger = logger;
  }

  async getAllProducts(): Promise<Product[]> {
    try {
      this.logger.info('Fetching all products');
      const products = await this.productRepository.findAll();
      this.logger.info(`Retrieved ${products.length} products`);
      return products;
    } catch (error) {
      this.logger.error('Error fetching all products', error);
      throw new BusinessError('Failed to retrieve products');
    }
  }

  async getProductById(id: string): Promise<Product | null> {
    try {
      this.logger.info(`Fetching product with ID: ${id}`);

      if (!id) {
        throw new BusinessError('Product ID is required');
      }

      const product = await this.productRepository.findById(id);

      if (!product) {
        this.logger.warn(`Product not found with ID: ${id}`);
        return null;
      }

      this.logger.info(`Product retrieved: ${product.name}`);
      return product;
    } catch (error) {
      this.logger.error(`Error fetching product with ID: ${id}`, error);
      throw new BusinessError('Failed to retrieve product');
    }
  }

  async createProduct(productData: Partial<Product>): Promise<Product> {
    try {
      this.logger.info('Creating new product');

      // Validate required fields
      if (!productData.name) {
        throw new BusinessError('Product name is required');
      }

      if (productData.price === undefined || productData.price === null) {
        throw new BusinessError('Product price is required');
      }

      if (productData.price < 0) {
        throw new BusinessError('Product price cannot be negative');
      }

      const product = await this.productRepository.create(productData);
      this.logger.info(`Product created with ID: ${product.id}`);
      return product;
    } catch (error) {
      this.logger.error('Error creating product', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create product');
    }
  }

  async updateProduct(id: string, productData: Partial<Product>): Promise<Product> {
    try {
      this.logger.info(`Updating product with ID: ${id}`);

      if (!id) {
        throw new BusinessError('Product ID is required');
      }

      // Validate price if provided
      if (productData.price !== undefined && productData.price < 0) {
        throw new BusinessError('Product price cannot be negative');
      }

      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new BusinessError('Product not found');
      }

      const updatedProduct = await this.productRepository.update(id, productData);
      this.logger.info(`Product updated with ID: ${id}`);
      return updatedProduct;
    } catch (error) {
      this.logger.error(`Error updating product with ID: ${id}`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update product');
    }
  }

  async deleteProduct(id: string): Promise<boolean> {
    try {
      this.logger.info(`Deleting product with ID: ${id}`);

      if (!id) {
        throw new BusinessError('Product ID is required');
      }

      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new BusinessError('Product not found');
      }

      const result = await this.productRepository.delete(id);
      this.logger.info(`Product deleted with ID: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting product with ID: ${id}`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to delete product');
    }
  }

  async searchProducts(query: string): Promise<Product[]> {
    try {
      this.logger.info(`Searching products with query: ${query}`);

      if (!query) {
        return await this.getAllProducts();
      }

      const products = await this.productRepository.search(query);
      this.logger.info(`Found ${products.length} products matching query`);
      return products;
    } catch (error) {
      this.logger.error(`Error searching products with query: ${query}`, error);
      throw new BusinessError('Failed to search products');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'ProductService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'ProductService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default ProductService;
}