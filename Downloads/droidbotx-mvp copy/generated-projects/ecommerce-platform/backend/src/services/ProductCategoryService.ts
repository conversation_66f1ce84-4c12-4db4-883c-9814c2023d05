// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { ProductCategory } from '../models/ProductCategory.js';
import { ProductCategoryRepository } from '../repositories/ProductCategoryRepository.js';
import { Logger } from '../utils/Logger.js';

export class ProductCategoryService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class ProductCategoryService {
  private repository: ProductCategoryRepository;
  private logger: Logger;

  constructor(pool: Pool) {
    this.repository = new ProductCategoryRepository(pool);
    this.logger = new Logger('ProductCategoryService');
  }

  async getAllCategories(): Promise<ProductCategory[]> {
    try {
      this.logger.info('Fetching all product categories');
      const categories = await this.repository.findAll();
      this.logger.info(`Retrieved ${categories.length} product categories`);
      return categories;
    } catch (error) {
      this.logger.error('Error fetching product categories', { error });
      throw new BusinessError('Failed to retrieve product categories');
    }
  }

  async getCategoryById(id: string): Promise<ProductCategory | null> {
    try {
      this.logger.info('Fetching product category by ID', { id });

      if (!id) {
        throw new BusinessError('Category ID is required');
      }

      const category = await this.repository.findById(id);

      if (!category) {
        this.logger.warn('Product category not found', { id });
        return null;
      }

      this.logger.info('Product category retrieved successfully', { id });
      return category;
    } catch (error) {
      this.logger.error('Error fetching product category by ID', { id, error });
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to retrieve product category');
    }
  }

  async createCategory(categoryData: Partial<ProductCategory>): Promise<ProductCategory> {
    try {
      this.logger.info('Creating new product category', { name: categoryData.name });

      // Validate required fields
      if (!categoryData.name) {
        throw new BusinessError('Category name is required');
      }

      // Check for duplicate category name
      const existingCategory = await this.repository.findByName(categoryData.name);
      if (existingCategory) {
        throw new BusinessError('Category with this name already exists');
      }

      // Create the category
      const category = await this.repository.create({
        name: categoryData.name,
        description: categoryData.description || '',
        created_at: new Date(),
        updated_at: new Date()
      });

      this.logger.info('Product category created successfully', { id: category.id, name: category.name });
      return category;
    } catch (error) {
      this.logger.error('Error creating product category', { error });
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to create product category');
    }
  }

  async updateCategory(id: string, categoryData: Partial<ProductCategory>): Promise<ProductCategory> {
    try {
      this.logger.info('Updating product category', { id });

      if (!id) {
        throw new BusinessError('Category ID is required');
      }

      // Verify category exists
      const existingCategory = await this.repository.findById(id);
      if (!existingCategory) {
        throw new BusinessError('Category not found');
      }

      // Check for duplicate name (if name is being updated)
      if (categoryData.name && categoryData.name !== existingCategory.name) {
        const duplicateCategory = await this.repository.findByName(categoryData.name);
        if (duplicateCategory) {
          throw new BusinessError('Category with this name already exists');
        }
      }

      // Update the category
      const updatedCategory = await this.repository.update(id, {
        ...categoryData,
        updated_at: new Date()
      });

      this.logger.info('Product category updated successfully', { id: updatedCategory.id });
      return updatedCategory;
    } catch (error) {
      this.logger.error('Error updating product category', { id, error });
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to update product category');
    }
  }

  async deleteCategory(id: string): Promise<boolean> {
    try {
      this.logger.info('Deleting product category', { id });

      if (!id) {
        throw new BusinessError('Category ID is required');
      }

      // Verify category exists
      const existingCategory = await this.repository.findById(id);
      if (!existingCategory) {
        throw new BusinessError('Category not found');
      }

      // Check if category has products (business rule)
      const hasProducts = await this.repository.hasProducts(id);
      if (hasProducts) {
        throw new BusinessError('Cannot delete category that has products');
      }

      // Delete the category
      const result = await this.repository.delete(id);

      if (result) {
        this.logger.info('Product category deleted successfully', { id });
      } else {
        this.logger.warn('Product category not found for deletion', { id });
      }

      return result;
    } catch (error) {
      this.logger.error('Error deleting product category', { id, error });
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to delete product category');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'ProductCategoryService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'ProductCategoryService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default ProductCategoryService;
}