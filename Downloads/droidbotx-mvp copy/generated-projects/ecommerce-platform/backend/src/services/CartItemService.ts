// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { CartItem } from '../models/CartItem.js';
import { CartItemRepository } from '../repositories/CartItemRepository.js';
import { Logger } from '../utils/Logger.js';

export class CartItemService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class CartItemService {
  private cartItemRepository: CartItemRepository;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.cartItemRepository = new CartItemRepository(pool);
    this.logger = logger;
  }

  async getCartItemById(id: string): Promise<CartItem | null> {
    try {
      this.logger.info(`Fetching cart item with ID: ${id}`);
      const cartItem = await this.cartItemRepository.findById(id);

      if (!cartItem) {
        this.logger.warn(`CartItem with ID ${id} not found`);
        return null;
      }

      return cartItem;
    } catch (error) {
      this.logger.error(`Error fetching cart item with ID ${id}:`, error);
      throw new BusinessError('Failed to retrieve cart item');
    }
  }

  async getAllCartItems(): Promise<CartItem[]> {
    try {
      this.logger.info('Fetching all cart items');
      return await this.cartItemRepository.findAll();
    } catch (error) {
      this.logger.error('Error fetching all cart items:', error);
      throw new BusinessError('Failed to retrieve cart items');
    }
  }

  async createCartItem(cartItem: CartItem): Promise<CartItem> {
    try {
      this.logger.info('Creating new cart item');

      // Validate business rules
      this.validateCartItem(cartItem);

      const createdCartItem = await this.cartItemRepository.create(cartItem);
      this.logger.info(`Created cart item with ID: ${createdCartItem.id}`);

      return createdCartItem;
    } catch (error) {
      this.logger.error('Error creating cart item:', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create cart item');
    }
  }

  async updateCartItem(id: string, cartItem: CartItem): Promise<CartItem> {
    try {
      this.logger.info(`Updating cart item with ID: ${id}`);

      // Validate business rules
      this.validateCartItem(cartItem);

      const existingCartItem = await this.cartItemRepository.findById(id);
      if (!existingCartItem) {
        throw new BusinessError('CartItem not found');
      }

      const updatedCartItem = await this.cartItemRepository.update(id, cartItem);
      this.logger.info(`Updated cart item with ID: ${id}`);

      return updatedCartItem;
    } catch (error) {
      this.logger.error(`Error updating cart item with ID ${id}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update cart item');
    }
  }

  async deleteCartItem(id: string): Promise<boolean> {
    try {
      this.logger.info(`Deleting cart item with ID: ${id}`);

      const existingCartItem = await this.cartItemRepository.findById(id);
      if (!existingCartItem) {
        throw new BusinessError('CartItem not found');
      }

      const result = await this.cartItemRepository.delete(id);
      if (result) {
        this.logger.info(`Deleted cart item with ID: ${id}`);
      } else {
        this.logger.warn(`Failed to delete cart item with ID: ${id}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error deleting cart item with ID ${id}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to delete cart item');
    }
  }

  private validateCartItem(cartItem: CartItem): void {
    // Add business rule validations here
    if (cartItem.quantity <= 0) {
      throw new BusinessError('Quantity must be greater than zero');
    }

    if (cartItem.price < 0) {
      throw new BusinessError('Price cannot be negative');
    }

    // Additional business validations can be added here
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'CartItemService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'CartItemService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default CartItemService;
}