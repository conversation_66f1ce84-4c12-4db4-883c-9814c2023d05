// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError, ValidationError } from '../errors/BusinessError.js';
import { CartItem } from '../models/CartItem.js';
import { Logger } from '../utils/Logger.js';

export class CartItemService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }






interface CartItemRepository {
  findById(id: number): Promise<CartItem | null>;
  findByUserId(userId: number): Promise<CartItem[]>;
  create(cartItem: CartItem): Promise<CartItem>;
  update(cartItem: CartItem): Promise<CartItem>;
  delete(id: number): Promise<boolean>;
  deleteByUserId(userId: number): Promise<boolean>;
}

class DatabaseCartItemRepository implements CartItemRepository {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async findById(id: number): Promise<CartItem | null> {
    try {
      const query = 'SELECT * FROM cart_items WHERE id = $1 AND deleted_at IS NULL';
      const result = await this.pool.query(query, [id]);
      return result.rows.length ? new CartItem(result.rows[0]) : null;
    } catch (error) {
      this.logger.error('Error finding cart item by ID', { id, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to retrieve cart item');
    }
  }

  async findByUserId(userId: number): Promise<CartItem[]> {
    try {
      const query = 'SELECT * FROM cart_items WHERE user_id = $1 AND deleted_at IS NULL';
      const result = await this.pool.query(query, [userId]);
      return result.rows.map(row => new CartItem(row));
    } catch (error) {
      this.logger.error('Error finding cart items by user ID', { userId, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to retrieve cart items');
    }
  }

  async create(cartItem: CartItem): Promise<CartItem> {
    try {
      const query = `;`
        INSERT INTO cart_items (user_id, product_id, quantity, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW());
        RETURNING *
      `;`
      const values = [cartItem.userId, cartItem.productId, cartItem.quantity];
      const result = await this.pool.query(query, values);
      return new CartItem(result.rows[0]);
    } catch (error) {
      this.logger.error('Error creating cart item', { cartItem, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to create cart item');
    }
  }

  async update(cartItem: CartItem): Promise<CartItem> {
    try {
      const query = `;`
        UPDATE cart_items
        SET user_id = $1, product_id = $2, quantity = $3, updated_at = NOW()
        WHERE id = $4 AND deleted_at IS NULL
        RETURNING *
      `;`
      const values = [cartItem.userId, cartItem.productId, cartItem.quantity, cartItem.id];
      const result = await this.pool.query(query, values);

      if (result.rows.length === 0) {
        throw new BusinessError('NOT_FOUND', 'Cart item not found');
      }

      return new CartItem(result.rows[0]);
    } catch (error) {
      if (error instanceof BusinessError) throw error;
      this.logger.error('Error updating cart item', { cartItem, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to update cart item');
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const query = 'UPDATE cart_items SET deleted_at = NOW() WHERE id = $1 AND deleted_at IS NULL';
      const result = await this.pool.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Error deleting cart item', { id, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to delete cart item');
    }
  }

  async deleteByUserId(userId: number): Promise<boolean> {
    try {
      const query = 'UPDATE cart_items SET deleted_at = NOW() WHERE user_id = $1 AND deleted_at IS NULL';
      const result = await this.pool.query(query, [userId]);
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Error deleting cart items by user ID', { userId, error });
      throw new BusinessError('DATABASE_ERROR', 'Failed to delete cart items');
    }
  }
}

export class CartItemService {
  private repository: CartItemRepository;
  private logger: Logger;

  constructor(repository: CartItemRepository, logger: Logger) {
    this.repository = repository;
    this.logger = logger;
  }

  static createWithDatabase(pool: Pool, logger: Logger): CartItemService {
    const repository = new DatabaseCartItemRepository(pool, logger);
    return new CartItemService(repository, logger);
  }

  async getCartItemById(id: number): Promise<CartItem> {
    this.logger.info('Fetching cart item by ID', { id });

    if (!id || id <= 0) {
      throw new ValidationError('Invalid cart item ID');
    }

    const cartItem = await this.repository.findById(id);
    if (!cartItem) {
      throw new BusinessError('NOT_FOUND', 'Cart item not found');
    }

    return cartItem;
  }

  async getCartItemsByUserId(userId: number): Promise<CartItem[]> {
    this.logger.info('Fetching cart items by user ID', { userId });

    if (!userId || userId <= 0) {
      throw new ValidationError('Invalid user ID');
    }

    return await this.repository.findByUserId(userId);
  }

  async addCartItem(userId: number, productId: number, quantity: number): Promise<CartItem> {
    this.logger.info('Adding item to cart', { userId, productId, quantity });

    // Validate inputs
    if (!userId || userId <= 0) {
      throw new ValidationError('Invalid user ID');
    }

    if (!productId || productId <= 0) {
      throw new ValidationError('Invalid product ID');
    }

    if (!quantity || quantity <= 0) {
      throw new ValidationError('Quantity must be greater than zero');
    }

    // Business rule: Check if item already exists in cart
    const existingItems = await this.repository.findByUserId(userId);
    const existingItem = existingItems.find(item => item.productId === productId);

    if (existingItem) {
      // Update quantity if item already exists
      existingItem.quantity += quantity;
      return await this.repository.update(existingItem);
    }

    // Create new cart item
    const cartItem = new CartItem({
      userId,
      productId,
      quantity
    });

    return await this.repository.create(cartItem);
  }

  async updateCartItemQuantity(id: number, quantity: number): Promise<CartItem> {
    this.logger.info('Updating cart item quantity', { id, quantity });

    if (!id || id <= 0) {
      throw new ValidationError('Invalid cart item ID');
    }

    if (!quantity || quantity <= 0) {
      throw new ValidationError('Quantity must be greater than zero');
    }

    const cartItem = await this.repository.findById(id);
    if (!cartItem) {
      throw new BusinessError('NOT_FOUND', 'Cart item not found');
    }

    cartItem.quantity = quantity;
    return await this.repository.update(cartItem);
  }

  async removeCartItem(id: number): Promise<boolean> {
    this.logger.info('Removing cart item', { id });

    if (!id || id <= 0) {
      throw new ValidationError('Invalid cart item ID');
    }

    const result = await this.repository.delete(id);
    if (!result) {
      throw new BusinessError('NOT_FOUND', 'Cart item not found');
    }

    return result;
  }

  async clearUserCart(userId: number): Promise<boolean> {
    this.logger.info('Clearing user cart', { userId });

    if (!userId || userId <= 0) {
      throw new ValidationError('Invalid user ID');
    }

    return await this.repository.deleteByUserId(userId);
  }

  async getCartTotalQuantity(userId: number): Promise<number> {
    this.logger.info('Calculating cart total quantity', { userId });

    if (!userId || userId <= 0) {
      throw new ValidationError('Invalid user ID');
    }

    const cartItems = await this.repository.findByUserId(userId);
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  }

  async getCartItemCount(userId: number): Promise<number> {
    this.logger.info('Getting cart item count', { userId });

    if (!userId || userId <= 0) {
      throw new ValidationError('Invalid user ID');
    }

    const cartItems = await this.repository.findByUserId(userId);
    return cartItems.length;
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'CartItemService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'CartItemService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default CartItemService;
}