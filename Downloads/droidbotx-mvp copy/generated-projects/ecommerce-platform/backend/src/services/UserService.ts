// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool, QueryResult } from 'pg';
import { User, UserCreationAttributes, UserUpdateAttributes } from '../models/User.js';
import { UserRepository } from '../repositories/UserRepository.js';
import { BusinessError, ValidationError } from '../utils/CustomErrors.js';
import { Logger } from '../utils/Logger.js';

export class UserService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export interface UserServiceInterface {
  getUserById(id: number): Promise<User>;
  getAllUsers(): Promise<User[]>;
  createUser(userData: UserCreationAttributes): Promise<User>;
  updateUser(id: number, userData: UserUpdateAttributes): Promise<User>;
  deleteUser(id: number): Promise<boolean>;
}

export class UserService implements UserServiceInterface {
  private logger: Logger;
  private userRepository: UserRepository;

  constructor(userRepository: UserRepository, logger: Logger) {
    this.userRepository = userRepository;
    this.logger = logger;
  }

  async getUserById(id: number): Promise<User> {
    this.logger.info('Fetching user by ID', { userId: id });

    if (!id || id <= 0) {
      throw new ValidationError('User ID must be a positive integer');
    }

    try {
      const user = await this.userRepository.findById(id);

      if (!user) {
        throw new BusinessError(`User with ID ${id} not found`);
      }

      return user;
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }

      this.logger.error('Error fetching user by ID', { userId: id, error: (error as Error).message });
      throw new BusinessError('Failed to fetch user');
    }
  }

  async getAllUsers(): Promise<User[]> {
    this.logger.info('Fetching all users');

    try {
      const users = await this.userRepository.findAll();
      this.logger.info(`Retrieved ${users.length} users`);
      return users;
    } catch (error) {
      this.logger.error('Error fetching all users', { error: (error as Error).message });
      throw new BusinessError('Failed to fetch users');
    }
  }

  async createUser(userData: UserCreationAttributes): Promise<User> {
    this.logger.info('Creating new user', { email: userData.email });

    // Validate required fields
    if (!userData.email) {
      throw new ValidationError('Email is required');
    }

    if (!userData.firstName) {
      throw new ValidationError('First name is required');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      throw new ValidationError('Invalid email format');
    }

    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new ValidationError('User with this email already exists');
      }

      const user = await this.userRepository.create(userData);
      this.logger.info('User created successfully', { userId: user.id, email: user.email });
      return user;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Error creating user', { email: userData.email, error: (error as Error).message });
      throw new BusinessError('Failed to create user');
    }
  }

  async updateUser(id: number, userData: UserUpdateAttributes): Promise<User> {
    this.logger.info('Updating user', { userId: id });

    if (!id || id <= 0) {
      throw new ValidationError('User ID must be a positive integer');
    }

    // Validate email format if provided
    if (userData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        throw new ValidationError('Invalid email format');
      }
    }

    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new BusinessError(`User with ID ${id} not found`);
      }

      // Check email uniqueness if email is being updated
      if (userData.email && userData.email !== existingUser.email) {
        const userWithSameEmail = await this.userRepository.findByEmail(userData.email);
        if (userWithSameEmail) {
          throw new ValidationError('User with this email already exists');
        }
      }

      const user = await this.userRepository.update(id, userData);
      this.logger.info('User updated successfully', { userId: id });
      return user;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        throw error;
      }

      this.logger.error('Error updating user', { userId: id, error: (error as Error).message });
      throw new BusinessError('Failed to update user');
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    this.logger.info('Deleting user', { userId: id });

    if (!id || id <= 0) {
      throw new ValidationError('User ID must be a positive integer');
    }

    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new BusinessError(`User with ID ${id} not found`);
      }

      await this.userRepository.delete(id);
      this.logger.info('User deleted successfully', { userId: id });
      return true;
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }

      this.logger.error('Error deleting user', { userId: id, error: (error as Error).message });
      throw new BusinessError('Failed to delete user');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'UserService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'UserService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default UserService;
}