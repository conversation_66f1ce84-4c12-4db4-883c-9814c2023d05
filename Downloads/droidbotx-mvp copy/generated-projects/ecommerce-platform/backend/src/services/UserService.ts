// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { User } from '../models/User.js';
import { Logger } from '../utils/Logger.js';

export class UserService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class UserService {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async getAllUsers(): Promise<User[]> {
    try {
      this.logger.info('Fetching all users');
      const query = 'SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at DESC';
      const result = await this.pool.query(query);

      const users = result.rows.map(row => ({
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        createdAt: new Date(row.created_at)
      }));

      this.logger.info(`Retrieved ${users.length} users`);
      return users;
    } catch (error) {
      this.logger.error('Error fetching users:', error);
      throw new DatabaseError('Failed to retrieve users');
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      this.logger.info(`Fetching user with ID: ${id}`);

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      const query = 'SELECT id, email, first_name, last_name, created_at FROM users WHERE id = $1';
      const result = await this.pool.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info(`User with ID ${id} not found`);
        return null;
      }

      const row = result.rows[0];
      const user: User = {
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        createdAt: new Date(row.created_at)
      };

      this.logger.info(`User with ID ${id} retrieved successfully`);
      return user;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error(`Error fetching user with ID ${id}:`, error);
      throw new DatabaseError('Failed to retrieve user');
    }
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<User> {
    try {
      this.logger.info('Creating new user');

      // Validate input
      if (!userData.email) {
        throw new ValidationError('Email is required');
      }

      if (!userData.firstName) {
        throw new ValidationError('First name is required');
      }

      if (!userData.lastName) {
        throw new ValidationError('Last name is required');
      }

      // Check if user already exists
      const existingUser = await this.getUserByEmail(userData.email);
      if (existingUser) {
        throw new ValidationError('User with this email already exists');
      }

      const query = `;`
        INSERT INTO users (email, first_name, last_name)
        VALUES ($1, $2, $3);
        RETURNING id, email, first_name, last_name, created_at
      `;`

      const result = await this.pool.query(query, [;
        userData.email,
        userData.firstName,
        userData.lastName
      ]);

      const row = result.rows[0];
      const user: User = {
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        createdAt: new Date(row.created_at)
      };

      this.logger.info(`User created successfully with ID: ${user.id}`);
      return user;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Error creating user:', error);
      throw new DatabaseError('Failed to create user');
    }
  }

  async updateUser(id: string, userData: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<User> {
    try {
      this.logger.info(`Updating user with ID: ${id}`);

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      // Check if user exists
      const existingUser = await this.getUserById(id);
      if (!existingUser) {
        throw new ValidationError('User not found');
      }

      // Build dynamic update query
      const fields = [];
      const values = [];
      let index = 1;

      if (userData.email !== undefined) {
        fields.push(`email = $${index++}`);
        values.push(userData.email);
      }

      if (userData.firstName !== undefined) {
        fields.push(`first_name = $${index++}`);
        values.push(userData.firstName);
      }

      if (userData.lastName !== undefined) {
        fields.push(`last_name = $${index++}`);
        values.push(userData.lastName);
      }

      if (fields.length === 0) {
        return existingUser;
      }

      values.push(id);

      const query = `;`
        UPDATE users
        SET ${fields.join(', ')}
        WHERE id = $${index}
        RETURNING id, email, first_name, last_name, created_at
      `;`

      const result = await this.pool.query(query, values);

      const row = result.rows[0];
      const user: User = {
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        createdAt: new Date(row.created_at)
      };

      this.logger.info(`User with ID ${id} updated successfully`);
      return user;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error(`Error updating user with ID ${id}:`, error);
      throw new DatabaseError('Failed to update user');
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      this.logger.info(`Deleting user with ID: ${id}`);

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      const query = 'DELETE FROM users WHERE id = $1';
      const result = await this.pool.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ValidationError('User not found');
      }

      this.logger.info(`User with ID ${id} deleted successfully`);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error(`Error deleting user with ID ${id}:`, error);
      throw new DatabaseError('Failed to delete user');
    }
  }

  private async getUserByEmail(email: string): Promise<User | null> {
    try {
      const query = 'SELECT id, email, first_name, last_name, created_at FROM users WHERE email = $1';
      const result = await this.pool.query(query, [email]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        createdAt: new Date(row.created_at)
      };
    } catch (error) {
      this.logger.error(`Error fetching user by email ${email}:`, error);
      throw new DatabaseError('Failed to retrieve user');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'UserService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'UserService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default UserService;
}