// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { OrderItem } from '../models/OrderItem.js';
import { OrderItemRepository } from '../repositories/OrderItemRepository.js';
import { Logger } from '../utils/Logger.js';

export class OrderItemService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class OrderItemService {
  private orderItemRepository: OrderItemRepository;
  private logger: Logger;

  constructor(pool: Pool) {
    this.orderItemRepository = new OrderItemRepository(pool);
    this.logger = new Logger('OrderItemService');
  }

  async getOrderItemById(id: string): Promise<OrderItem | null> {
    try {
      this.logger.info(`Fetching order item with ID: ${id}`);
      const orderItem = await this.orderItemRepository.findById(id);

      if (!orderItem) {
        this.logger.warn(`Order item not found with ID: ${id}`);
        return null;
      }

      this.logger.info(`Successfully retrieved order item with ID: ${id}`);
      return orderItem;
    } catch (error) {
      this.logger.error(`Error fetching order item with ID ${id}:`, error);
      throw new BusinessError('Failed to retrieve order item');
    }
  }

  async getAllOrderItems(): Promise<OrderItem[]> {
    try {
      this.logger.info('Fetching all order items');
      const orderItems = await this.orderItemRepository.findAll();
      this.logger.info(`Successfully retrieved ${orderItems.length} order items`);
      return orderItems;
    } catch (error) {
      this.logger.error('Error fetching all order items:', error);
      throw new BusinessError('Failed to retrieve order items');
    }
  }

  async createOrderItem(orderItemData: Partial<OrderItem>): Promise<OrderItem> {
    try {
      this.logger.info('Creating new order item');

      // Validate required fields
      if (!orderItemData.orderId) {
        throw new BusinessError('Order ID is required');
      }

      if (!orderItemData.productId) {
        throw new BusinessError('Product ID is required');
      }

      if (!orderItemData.quantity || orderItemData.quantity <= 0) {
        throw new BusinessError('Quantity must be a positive number');
      }

      if (!orderItemData.price || orderItemData.price < 0) {
        throw new BusinessError('Price must be a non-negative number');
      }

      const orderItem = await this.orderItemRepository.create(orderItemData);
      this.logger.info(`Successfully created order item with ID: ${orderItem.id}`);
      return orderItem;
    } catch (error) {
      this.logger.error('Error creating order item:', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create order item');
    }
  }

  async updateOrderItem(id: string, orderItemData: Partial<OrderItem>): Promise<OrderItem> {
    try {
      this.logger.info(`Updating order item with ID: ${id}`);

      // Validate order item exists
      const existingOrderItem = await this.orderItemRepository.findById(id);
      if (!existingOrderItem) {
        throw new BusinessError('Order item not found');
      }

      // Validate update data
      if (orderItemData.quantity !== undefined && orderItemData.quantity <= 0) {
        throw new BusinessError('Quantity must be a positive number');
      }

      if (orderItemData.price !== undefined && orderItemData.price < 0) {
        throw new BusinessError('Price must be a non-negative number');
      }

      const updatedOrderItem = await this.orderItemRepository.update(id, orderItemData);
      this.logger.info(`Successfully updated order item with ID: ${id}`);
      return updatedOrderItem;
    } catch (error) {
      this.logger.error(`Error updating order item with ID ${id}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update order item');
    }
  }

  async deleteOrderItem(id: string): Promise<void> {
    try {
      this.logger.info(`Deleting order item with ID: ${id}`);

      // Validate order item exists
      const existingOrderItem = await this.orderItemRepository.findById(id);
      if (!existingOrderItem) {
        throw new BusinessError('Order item not found');
      }

      await this.orderItemRepository.delete(id);
      this.logger.info(`Successfully deleted order item with ID: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting order item with ID ${id}:`, error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to delete order item');
    }
  }

  async getOrderItemsByOrderId(orderId: string): Promise<OrderItem[]> {
    try {
      this.logger.info(`Fetching order items for order ID: ${orderId}`);
      const orderItems = await this.orderItemRepository.findByOrderId(orderId);
      this.logger.info(`Successfully retrieved ${orderItems.length} order items for order ID: ${orderId}`);
      return orderItems;
    } catch (error) {
      this.logger.error(`Error fetching order items for order ID ${orderId}:`, error);
      throw new BusinessError('Failed to retrieve order items');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'OrderItemService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'OrderItemService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default OrderItemService;
}