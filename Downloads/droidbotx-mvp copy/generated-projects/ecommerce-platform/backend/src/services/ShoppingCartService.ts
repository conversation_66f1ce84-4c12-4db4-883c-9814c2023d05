// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { ShoppingCart } from '../models/ShoppingCart.js';
import { ShoppingCartRepository } from '../repositories/ShoppingCartRepository.js';
import { Logger } from '../utils/Logger.js';

export class ShoppingCartService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class ShoppingCartService {
  private repository: ShoppingCartRepository;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.repository = new ShoppingCartRepository(pool);
    this.logger = logger;
  }

  async getCartById(cartId: string): Promise<ShoppingCart> {
    try {
      this.logger.info(`Fetching shopping cart with ID: ${cartId}`);
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new BusinessError('Shopping cart not found', 'CART_NOT_FOUND');
      }

      return cart;
    } catch (error) {
      this.logger.error(`Error fetching shopping cart: ${error.message}`, { cartId, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to retrieve shopping cart', 'CART_RETRIEVAL_FAILED');
    }
  }

  async createCart(userId: string): Promise<ShoppingCart> {
    try {
      this.logger.info(`Creating new shopping cart for user: ${userId}`);

      // Validate user ID
      if (!userId) {
        throw new BusinessError('User ID is required', 'INVALID_USER_ID');
      }

      const cart = new ShoppingCart({
        userId,
        items: [],
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const savedCart = await this.repository.create(cart);
      this.logger.info(`Shopping cart created successfully: ${savedCart.id}`);
      return savedCart;
    } catch (error) {
      this.logger.error(`Error creating shopping cart: ${error.message}`, { userId, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create shopping cart', 'CART_CREATION_FAILED');
    }
  }

  async addItemToCart(cartId: string, productId: string, quantity: number): Promise<ShoppingCart> {
    try {
      this.logger.info(`Adding item to cart`, { cartId, productId, quantity });

      // Validate inputs
      if (!cartId || !productId) {
        throw new BusinessError('Cart ID and Product ID are required', 'INVALID_INPUT');
      }

      if (quantity <= 0) {
        throw new BusinessError('Quantity must be greater than zero', 'INVALID_QUANTITY');
      }

      const cart = await this.getCartById(cartId);

      // Check if item already exists in cart
      const existingItemIndex = cart.items.findIndex(item => item.productId === productId);

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        cart.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item to cart
        cart.items.push({
          productId,
          quantity,
          addedAt: new Date()
        });
      }

      cart.updatedAt = new Date();
      const updatedCart = await this.repository.update(cart);

      this.logger.info(`Item added to cart successfully`, { cartId, productId });
      return updatedCart;
    } catch (error) {
      this.logger.error(`Error adding item to cart: ${error.message}`, { cartId, productId, quantity, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to add item to cart', 'ADD_ITEM_FAILED');
    }
  }

  async removeItemFromCart(cartId: string, productId: string): Promise<ShoppingCart> {
    try {
      this.logger.info(`Removing item from cart`, { cartId, productId });

      // Validate inputs
      if (!cartId || !productId) {
        throw new BusinessError('Cart ID and Product ID are required', 'INVALID_INPUT');
      }

      const cart = await this.getCartById(cartId);

      // Filter out the item to be removed
      cart.items = cart.items.filter(item => item.productId !== productId);
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);

      this.logger.info(`Item removed from cart successfully`, { cartId, productId });
      return updatedCart;
    } catch (error) {
      this.logger.error(`Error removing item from cart: ${error.message}`, { cartId, productId, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to remove item from cart', 'REMOVE_ITEM_FAILED');
    }
  }

  async updateItemQuantity(cartId: string, productId: string, quantity: number): Promise<ShoppingCart> {
    try {
      this.logger.info(`Updating item quantity in cart`, { cartId, productId, quantity });

      // Validate inputs
      if (!cartId || !productId) {
        throw new BusinessError('Cart ID and Product ID are required', 'INVALID_INPUT');
      }

      if (quantity <= 0) {
        throw new BusinessError('Quantity must be greater than zero', 'INVALID_QUANTITY');
      }

      const cart = await this.getCartById(cartId);

      // Find the item to update
      const itemIndex = cart.items.findIndex(item => item.productId === productId);

      if (itemIndex === -1) {
        throw new BusinessError('Product not found in cart', 'PRODUCT_NOT_IN_CART');
      }

      cart.items[itemIndex].quantity = quantity;
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);

      this.logger.info(`Item quantity updated successfully`, { cartId, productId, quantity });
      return updatedCart;
    } catch (error) {
      this.logger.error(`Error updating item quantity: ${error.message}`, { cartId, productId, quantity, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update item quantity', 'UPDATE_QUANTITY_FAILED');
    }
  }

  async clearCart(cartId: string): Promise<ShoppingCart> {
    try {
      this.logger.info(`Clearing shopping cart`, { cartId });

      // Validate input
      if (!cartId) {
        throw new BusinessError('Cart ID is required', 'INVALID_INPUT');
      }

      const cart = await this.getCartById(cartId);

      // Clear all items
      cart.items = [];
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);

      this.logger.info(`Shopping cart cleared successfully`, { cartId });
      return updatedCart;
    } catch (error) {
      this.logger.error(`Error clearing shopping cart: ${error.message}`, { cartId, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to clear shopping cart', 'CLEAR_CART_FAILED');
    }
  }

  async getCartItemCount(cartId: string): Promise<number> {
    try {
      this.logger.info(`Getting item count for cart`, { cartId });

      const cart = await this.getCartById(cartId);
      const count = cart.items.reduce((total, item) => total + item.quantity, 0);

      this.logger.info(`Item count retrieved`, { cartId, count });
      return count;
    } catch (error) {
      this.logger.error(`Error getting cart item count: ${error.message}`, { cartId, error });
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to get cart item count', 'GET_ITEM_COUNT_FAILED');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'ShoppingCartService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'ShoppingCartService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default ShoppingCartService;
}