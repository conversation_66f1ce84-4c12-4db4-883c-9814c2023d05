// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { ShoppingCart } from '../models/ShoppingCart.js';
import { ShoppingCartRepository } from '../repositories/ShoppingCartRepository.js';
import { Logger } from '../utils/Logger.js';

export class ShoppingCartService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }








interface ShoppingCartItem {
  productId: string;
  quantity: number;
  price: number;
}

interface CreateShoppingCartRequest {
  userId: string;
  items?: ShoppingCartItem[];
}

interface UpdateShoppingCartRequest {
  cartId: string;
  items: ShoppingCartItem[];
}

interface AddItemRequest {
  cartId: string;
  item: ShoppingCartItem;
}

interface RemoveItemRequest {
  cartId: string;
  productId: string;
}

export class ShoppingCartService {
  private logger: Logger;
  private repository: ShoppingCartRepository;

  constructor(pool: Pool) {
    this.logger = new Logger('ShoppingCartService');
    this.repository = new ShoppingCartRepository(pool);
  }

  async createShoppingCart(request: CreateShoppingCartRequest): Promise<ShoppingCart> {
    try {
      this.logger.info('Creating new shopping cart', { userId: request.userId });

      // Validate input
      if (!request.userId) {
        throw new ValidationError('User ID is required');
      }

      // Business rule: Check if user already has an active cart
      const existingCart = await this.repository.findByUserId(request.userId);
      if (existingCart) {
        this.logger.info('User already has an active cart', { userId: request.userId, cartId: existingCart.id });
        return existingCart;
      }

      // Create new cart
      const cart = new ShoppingCart({
        userId: request.userId,
        items: request.items || [],
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const result = await this.repository.create(cart);
      this.logger.info('Shopping cart created successfully', { cartId: result.id });
      return result;
    } catch (error) {
      this.logger.error('Failed to create shopping cart', { error: error.message });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to create shopping cart');
    }
  }

  async getShoppingCart(cartId: string): Promise<ShoppingCart> {
    try {
      this.logger.info('Retrieving shopping cart', { cartId });

      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      const cart = await this.repository.findById(cartId);
      if (!cart) {
        throw new ValidationError('Shopping cart not found');
      }

      this.logger.info('Shopping cart retrieved successfully', { cartId });
      return cart;
    } catch (error) {
      this.logger.error('Failed to retrieve shopping cart', { error: error.message, cartId });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to retrieve shopping cart');
    }
  }

  async updateShoppingCart(request: UpdateShoppingCartRequest): Promise<ShoppingCart> {
    try {
      this.logger.info('Updating shopping cart', { cartId: request.cartId });

      // Validate input
      if (!request.cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!request.items) {
        throw new ValidationError('Items are required');
      }

      // Validate items
      for (const item of request.items) {
        if (!item.productId || item.quantity <= 0 || item.price < 0) {
          throw new ValidationError('Invalid item data');
        }
      }

      // Retrieve existing cart
      const existingCart = await this.repository.findById(request.cartId);
      if (!existingCart) {
        throw new ValidationError('Shopping cart not found');
      }

      // Update cart
      existingCart.items = request.items;
      existingCart.updatedAt = new Date();

      const result = await this.repository.update(existingCart);
      this.logger.info('Shopping cart updated successfully', { cartId: result.id });
      return result;
    } catch (error) {
      this.logger.error('Failed to update shopping cart', { error: error.message });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to update shopping cart');
    }
  }

  async addItemToCart(request: AddItemRequest): Promise<ShoppingCart> {
    try {
      this.logger.info('Adding item to shopping cart', { cartId: request.cartId, productId: request.item.productId });

      // Validate input
      if (!request.cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!request.item.productId || request.item.quantity <= 0 || request.item.price < 0) {
        throw new ValidationError('Invalid item data');
      }

      // Retrieve existing cart
      const cart = await this.repository.findById(request.cartId);
      if (!cart) {
        throw new ValidationError('Shopping cart not found');
      }

      // Check if item already exists in cart
      const existingItemIndex = cart.items.findIndex(item => item.productId === request.item.productId);

      if (existingItemIndex >= 0) {
        // Update quantity of existing item
        cart.items[existingItemIndex].quantity += request.item.quantity;
      } else {
        // Add new item
        cart.items.push(request.item);
      }

      cart.updatedAt = new Date();

      const result = await this.repository.update(cart);
      this.logger.info('Item added to shopping cart successfully', { cartId: result.id });
      return result;
    } catch (error) {
      this.logger.error('Failed to add item to shopping cart', { error: error.message });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to add item to shopping cart');
    }
  }

  async removeItemFromCart(request: RemoveItemRequest): Promise<ShoppingCart> {
    try {
      this.logger.info('Removing item from shopping cart', { cartId: request.cartId, productId: request.productId });

      // Validate input
      if (!request.cartId) {
        throw new ValidationError('Cart ID is required');
      }

      if (!request.productId) {
        throw new ValidationError('Product ID is required');
      }

      // Retrieve existing cart
      const cart = await this.repository.findById(request.cartId);
      if (!cart) {
        throw new ValidationError('Shopping cart not found');
      }

      // Remove item
      cart.items = cart.items.filter(item => item.productId !== request.productId);
      cart.updatedAt = new Date();

      const result = await this.repository.update(cart);
      this.logger.info('Item removed from shopping cart successfully', { cartId: result.id });
      return result;
    } catch (error) {
      this.logger.error('Failed to remove item from shopping cart', { error: error.message });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to remove item from shopping cart');
    }
  }

  async clearShoppingCart(cartId: string): Promise<ShoppingCart> {
    try {
      this.logger.info('Clearing shopping cart', { cartId });

      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      // Retrieve existing cart
      const cart = await this.repository.findById(cartId);
      if (!cart) {
        throw new ValidationError('Shopping cart not found');
      }

      // Clear items
      cart.items = [];
      cart.updatedAt = new Date();

      const result = await this.repository.update(cart);
      this.logger.info('Shopping cart cleared successfully', { cartId });
      return result;
    } catch (error) {
      this.logger.error('Failed to clear shopping cart', { error: error.message, cartId });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to clear shopping cart');
    }
  }

  async deleteShoppingCart(cartId: string): Promise<void> {
    try {
      this.logger.info('Deleting shopping cart', { cartId });

      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      await this.repository.deleteById(cartId);
      this.logger.info('Shopping cart deleted successfully', { cartId });
    } catch (error) {
      this.logger.error('Failed to delete shopping cart', { error: error.message, cartId });
      throw new DatabaseError('Failed to delete shopping cart');
    }
  }

  async getUserActiveCart(userId: string): Promise<ShoppingCart | null> {
    try {
      this.logger.info('Retrieving user active cart', { userId });

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      const cart = await this.repository.findByUserId(userId);
      this.logger.info('User active cart retrieved', { userId, cartId: cart?.id });
      return cart;
    } catch (error) {
      this.logger.error('Failed to retrieve user active cart', { error: error.message, userId });
      throw new DatabaseError('Failed to retrieve user active cart');
    }
  }

  async calculateCartTotal(cartId: string): Promise<number> {
    try {
      this.logger.info('Calculating cart total', { cartId });

      if (!cartId) {
        throw new ValidationError('Cart ID is required');
      }

      const cart = await this.repository.findById(cartId);
      if (!cart) {
        throw new ValidationError('Shopping cart not found');
      }

      const total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      this.logger.info('Cart total calculated', { cartId, total });
      return total;
    } catch (error) {
      this.logger.error('Failed to calculate cart total', { error: error.message, cartId });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError('Failed to calculate cart total');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'ShoppingCartService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'ShoppingCartService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default ShoppingCartService;
}