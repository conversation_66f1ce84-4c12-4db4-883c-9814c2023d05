// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { orderRoutes } from '../services/OrderRoutes';
import { logger } from '../core/Logger.js';
import { body, validationResult } from 'express-validator';
import { Pool } from 'pg';
import jwt from 'jsonwebtoken';

const router = Router();
const orderRoutes = new orderRoutes();

router.use(authenticateToken);
router.use(validateRequest);






interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthenticatedRequest extends Request {
  userId?: string;
  userRole?: string;
}

const orderRoutes = Router();
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Authentication middleware
const authenticateToken = (req: AuthenticatedRequest, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret') as any;
    req.userId = decoded.userId;
    req.userRole = decoded.role;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: any) => {
    if (!req.userRole || !roles.includes(req.userRole)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// GET /orders - Get all orders (admin only)
orderRoutes.get(
  '/',
  authenticateToken,
  authorizeRole(['admin']),
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      const result = await pool.query('SELECT * FROM orders ORDER BY created_at DESC');
      const orders: Order[] = result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        status: row.status,
        totalAmount: parseFloat(row.total_amount),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at)
      }));
      res.json(orders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// GET /orders/my - Get current user's orders
orderRoutes.get(
  '/my',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      const result = await pool.query(
        'SELECT * FROM orders WHERE user_id = $1 ORDER BY created_at DESC',
        [req.userId]
      );
      const orders: Order[] = result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        status: row.status,
        totalAmount: parseFloat(row.total_amount),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at)
      }));
      res.json(orders);
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// GET /orders/:id - Get specific order
orderRoutes.get(
  '/:id',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    try {
      const result = await pool.query(
        'SELECT * FROM orders WHERE id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = result.rows[0];

      // Check if user owns the order or is admin
      if (order.user_id !== req.userId && req.userRole !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json({
        id: order.id,
        userId: order.user_id,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at)
      });
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// POST /orders - Create new order
orderRoutes.post(
  '/',
  authenticateToken,
  body('status').isIn(['pending', 'in_progress', 'completed', 'cancelled']).optional(),
  body('totalAmount').isNumeric().withMessage('Total amount must be a number'),
  async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { status = 'pending', totalAmount } = req.body;

    try {
      const result = await pool.query(
        'INSERT INTO orders (user_id, status, total_amount) VALUES ($1, $2, $3) RETURNING *',
        [req.userId, status, totalAmount]
      );

      const order = result.rows[0];
      res.status(201).json({
        id: order.id,
        userId: order.user_id,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at)
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// PUT /orders/:id - Update order
orderRoutes.put(
  '/:id',
  authenticateToken,
  body('status').isIn(['pending', 'in_progress', 'completed', 'cancelled']).optional(),
  body('totalAmount').isNumeric().optional().withMessage('Total amount must be a number'),
  async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { status, totalAmount } = req.body;

    try {
      // Check if order exists and user has permission
      const existingOrder = await pool.query(
        'SELECT * FROM orders WHERE id = $1',
        [id]
      );

      if (existingOrder.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      if (existingOrder.rows[0].user_id !== req.userId && req.userRole !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Build update query dynamically
      const updates = [];
      const values = [];
      let index = 1;

      if (status) {
        updates.push(`status = $${index}`);
        values.push(status);
        index++;
      }

      if (totalAmount !== undefined) {
        updates.push(`total_amount = $${index}`);
        values.push(totalAmount);
        index++;
      }

      if (updates.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
      }

      values.push(id);
      const query = `UPDATE orders SET ${updates.join(', ')}, updated_at = NOW() WHERE id = $${index} RETURNING *`;

      const result = await pool.query(query, values);
      const order = result.rows[0];

      res.json({
        id: order.id,
        userId: order.user_id,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at)
      });
    } catch (error) {
      console.error('Error updating order:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// DELETE /orders/:id - Delete order
orderRoutes.delete(
  '/:id',
  authenticateToken,
  authorizeRole(['admin']),
  async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    try {
      const result = await pool.query(
        'DELETE FROM orders WHERE id = $1 RETURNING *',
        [id]
      );

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      res.json({ message: 'Order deleted successfully' });
    } catch (error) {
      console.error('Error deleting order:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

export default orderRoutes;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default orderRoutes;