// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { productcategoryRoutes } from '../services/ProductcategoryRoutes';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import winston from 'winston';

const router = Router();
const productcategoryRoutes = new productcategoryRoutes();

router.use(authenticateToken);
router.use(validateRequest);







// Initialize router
const router = Router();

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Logger configuration
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'product-category.log' })
  ]
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret';

// Authorization middleware
const authenticateToken = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    logger.warn('Authorization token missing');
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      logger.error('Token verification failed', { error: err.message });
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    (req as any).user = user;
    next();
  });
};

// Role-based authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: Request, res: Response, next: Function) => {
    const user = (req as any).user;
    if (!user || !roles.includes(user.role)) {
      logger.warn('Unauthorized access attempt', { userId: user?.id, requiredRoles: roles });
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Validation schemas
const productCategorySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  parent_id: z.string().uuid().optional(),
});

// ProductCategory interface
interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  parent_id?: string;
  created_at: Date;
  updated_at: Date;
}

// GET /api/product-categories - Get all product categories
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, name, description, parent_id, created_at, updated_at FROM product_categories ORDER BY name'
      );

      const categories: ProductCategory[] = result.rows;
      logger.info('Retrieved product categories', { count: categories.length });
      res.status(200).json(categories);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error retrieving product categories', { error: (error as Error).message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/product-categories/:id - Get a specific product category
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const categoryId = req.params.id;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(categoryId)) {
      return res.status(400).json({ error: 'Invalid category ID format' });
    }

    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, name, description, parent_id, created_at, updated_at FROM product_categories WHERE id = $1',
        [categoryId]
      );

      if (result.rows.length === 0) {
        logger.warn('Product category not found', { categoryId });
        return res.status(404).json({ error: 'Product category not found' });
      }

      const category: ProductCategory = result.rows[0];
      logger.info('Retrieved product category', { categoryId: category.id });
      res.status(200).json(category);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error retrieving product category', {
      error: (error as Error).message,
      categoryId: req.params.id
    });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/product-categories - Create a new product category
router.post('/', authenticateToken, authorizeRole(['admin']), async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = productCategorySchema.safeParse(req.body);
    if (!validationResult.success) {
      logger.warn('Product category validation failed', {
        errors: validationResult.error.errors
      });
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors
      });
    }

    const { name, description, parent_id } = validationResult.data;

    const client = await pool.connect();
    try {
      // Check if parent category exists (if provided)
      if (parent_id) {
        const parentResult = await client.query(
          'SELECT id FROM product_categories WHERE id = $1',
          [parent_id]
        );

        if (parentResult.rows.length === 0) {
          logger.warn('Parent category not found', { parentId: parent_id });
          return res.status(400).json({ error: 'Parent category not found' });
        }
      }

      // Check if category with same name exists
      const existingResult = await client.query(
        'SELECT id FROM product_categories WHERE name = $1',
        [name]
      );

      if (existingResult.rows.length > 0) {
        logger.warn('Product category with this name already exists', { name });
        return res.status(409).json({ error: 'Category with this name already exists' });
      }

      // Insert new category
      const result = await client.query(
        `INSERT INTO product_categories (name, description, parent_id)
         VALUES ($1, $2, $3)
         RETURNING id, name, description, parent_id, created_at, updated_at`,
        [name, description, parent_id]
      );

      const newCategory: ProductCategory = result.rows[0];
      logger.info('Created new product category', { categoryId: newCategory.id });
      res.status(201).json(newCategory);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error creating product category', { error: (error as Error).message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/product-categories/:id - Update a product category
router.put('/:id', authenticateToken, authorizeRole(['admin']), async (req: Request, res: Response) => {
  try {
    const categoryId = req.params.id;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(categoryId)) {
      return res.status(400).json({ error: 'Invalid category ID format' });
    }

    // Validate request body
    const validationResult = productCategorySchema.safeParse(req.body);
    if (!validationResult.success) {
      logger.warn('Product category update validation failed', {
        errors: validationResult.error.errors
      });
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors
      });
    }

    const { name, description, parent_id } = validationResult.data;

    const client = await pool.connect();
    try {
      // Check if category exists
      const existingResult = await client.query(
        'SELECT id FROM product_categories WHERE id = $1',
        [categoryId]
      );

      if (existingResult.rows.length === 0) {
        logger.warn('Product category not found for update', { categoryId });
        return res.status(404).json({ error: 'Product category not found' });
      }

      // Check if parent category exists (if provided)
      if (parent_id) {
        const parentResult = await client.query(
          'SELECT id FROM product_categories WHERE id = $1',
          [parent_id]
        );

        if (parentResult.rows.length === 0) {
          logger.warn('Parent category not found', { parentId: parent_id });
          return res.status(400).json({ error: 'Parent category not found' });
        }
      }

      // Check if another category with same name exists
      const nameCheckResult = await client.query(
        'SELECT id FROM product_categories WHERE name = $1 AND id != $2',
        [name, categoryId]
      );

      if (nameCheckResult.rows.length > 0) {
        logger.warn('Product category with this name already exists', { name });
        return res.status(409).json({ error: 'Category with this name already exists' });
      }

      // Update category
      const result = await client.query(
        `UPDATE product_categories
         SET name = $1, description = $2, parent_id = $3, updated_at = NOW()
         WHERE id = $4
         RETURNING id, name, description, parent_id, created_at, updated_at`,
        [name, description, parent_id, categoryId]
      );

      const updatedCategory: ProductCategory = result.rows[0];
      logger.info('Updated product category', { categoryId: updatedCategory.id });
      res.status(200).json(updatedCategory);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error updating product category', {
      error: (error as Error).message,
      categoryId: req.params.id
    });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/product-categories/:id - Delete a product category
router.delete('/:id', authenticateToken, authorizeRole(['admin']), async (req: Request, res: Response) => {
  try {
    const categoryId = req.params.id;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(categoryId)) {
      return res.status(400).json({ error: 'Invalid category ID format' });
    }

    const client = await pool.connect();
    try {
      // Check if category exists
      const existingResult = await client.query(
        'SELECT id FROM product_categories WHERE id = $1',
        [categoryId]
      );

      if (existingResult.rows.length === 0) {
        logger.warn('Product category not found for deletion', { categoryId });
        return res.status(404).json({ error: 'Product category not found' });
      }

      // Check if category has child categories
      const childResult = await client.query(
        'SELECT id FROM product_categories WHERE parent_id = $1',
        [categoryId]
      );

      if (childResult.rows.length > 0) {
        logger.warn('Cannot delete category with child categories', { categoryId });
        return res.status(400).json({
          error: 'Cannot delete category with child categories. Reassign child categories first.'
        });
      }

      // Check if category is used by products
      const productResult = await client.query(
        'SELECT id FROM products WHERE category_id = $1 LIMIT 1',
        [categoryId]
      );

      if (productResult.rows.length > 0) {
        logger.warn('Cannot delete category assigned to products', { categoryId });
        return res.status(400).json({
          error: 'Cannot delete category assigned to products. Reassign products first.'
        });
      }

      // Delete category
      await client.query('DELETE FROM product_categories WHERE id = $1', [categoryId]);

      logger.info('Deleted product category', { categoryId });
      res.status(204).send();
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error deleting product category', {
      error: (error as Error).message,
      categoryId: req.params.id
    });
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default productcategoryRoutes;