// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { shoppingcartRoutes } from '../services/ShoppingcartRoutes';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import winston from 'winston';

const router = Router();
const shoppingcartRoutes = new shoppingcartRoutes();

router.use(authenticateToken);
router.use(validateRequest);







// Initialize router
const router = Router();

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Logger configuration
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'shoppingcart.log' })
  ]
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret';

// Authentication middleware
const authenticateToken = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    logger.warn('Authentication failed: No token provided');
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      logger.warn('Authentication failed: Invalid token');
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    (req as any).user = user;
    next();
  });
};

// Authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: Request, res: Response, next: Function) => {
    const user = (req as any).user;
    if (!user || !roles.includes(user.role)) {
      logger.warn(`Authorization failed for user ${user?.id}`);
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Validation schemas
const cartItemSchema = z.object({
  productId: z.string().uuid(),
  quantity: z.number().int().positive()
});

const updateCartItemSchema = z.object({
  quantity: z.number().int().positive()
});

// Create a new shopping cart
router.post('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const result = await client.query(
        'INSERT INTO shopping_carts (user_id) VALUES ($1) RETURNING id, user_id, created_at',
        [userId]
      );

      await client.query('COMMIT');
      logger.info(`Created new shopping cart for user ${userId}`);
      res.status(201).json(result.rows[0]);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error creating shopping cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user's shopping cart
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;

    const result = await pool.query(
      `SELECT sc.id, sc.user_id, sc.created_at,
              sci.id as item_id, sci.product_id, sci.quantity,
              p.name as product_name, p.price as product_price
       FROM shopping_carts sc
       LEFT JOIN shopping_cart_items sci ON sc.id = sci.cart_id
       LEFT JOIN products p ON sci.product_id = p.id
       WHERE sc.user_id = $1 AND sc.is_active = true`,
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Shopping cart not found' });
    }

    // Format response
    const cart = {
      id: result.rows[0].id,
      userId: result.rows[0].user_id,
      createdAt: result.rows[0].created_at,
      items: result.rows.filter(row => row.item_id).map(row => ({
        id: row.item_id,
        productId: row.product_id,
        productName: row.product_name,
        productPrice: row.product_price,
        quantity: row.quantity
      }))
    };

    logger.info(`Retrieved shopping cart for user ${userId}`);
    res.json(cart);
  } catch (error) {
    logger.error('Error retrieving shopping cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add item to shopping cart
router.post('/items', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const validation = cartItemSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.error.flatten()
      });
    }

    const { productId, quantity } = validation.data;

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Get user's active cart
      const cartResult = await client.query(
        'SELECT id FROM shopping_carts WHERE user_id = $1 AND is_active = true',
        [userId]
      );

      if (cartResult.rows.length === 0) {
        return res.status(404).json({ error: 'Shopping cart not found' });
      }

      const cartId = cartResult.rows[0].id;

      // Check if product exists
      const productResult = await client.query(
        'SELECT id, name, price FROM products WHERE id = $1 AND is_active = true',
        [productId]
      );

      if (productResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Product not found' });
      }

      // Check if item already exists in cart
      const existingItem = await client.query(
        'SELECT id, quantity FROM shopping_cart_items WHERE cart_id = $1 AND product_id = $2',
        [cartId, productId]
      );

      let result;
      if (existingItem.rows.length > 0) {
        // Update existing item
        const newQuantity = existingItem.rows[0].quantity + quantity;
        result = await client.query(
          'UPDATE shopping_cart_items SET quantity = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
          [newQuantity, existingItem.rows[0].id]
        );
      } else {
        // Insert new item
        result = await client.query(
          'INSERT INTO shopping_cart_items (cart_id, product_id, quantity) VALUES ($1, $2, $3) RETURNING *',
          [cartId, productId, quantity]
        );
      }

      await client.query('COMMIT');
      logger.info(`Added item to cart for user ${userId}`);
      res.status(201).json(result.rows[0]);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error adding item to cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update item quantity in shopping cart
router.put('/items/:itemId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { itemId } = req.params;

    const validation = updateCartItemSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.error.flatten()
      });
    }

    const { quantity } = validation.data;

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Verify the item belongs to user's cart
      const result = await client.query(
        `UPDATE shopping_cart_items
         SET quantity = $1, updated_at = NOW()
         FROM shopping_carts
         WHERE shopping_cart_items.id = $2
         AND shopping_carts.id = shopping_cart_items.cart_id
         AND shopping_carts.user_id = $3
         AND shopping_carts.is_active = true
         RETURNING shopping_cart_items.*`,
        [quantity, itemId, userId]
      );

      if (result.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Cart item not found' });
      }

      await client.query('COMMIT');
      logger.info(`Updated cart item ${itemId} for user ${userId}`);
      res.json(result.rows[0]);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error updating cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Remove item from shopping cart
router.delete('/items/:itemId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { itemId } = req.params;

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Verify the item belongs to user's cart
      const result = await client.query(
        `DELETE FROM shopping_cart_items
         USING shopping_carts
         WHERE shopping_cart_items.id = $1
         AND shopping_carts.id = shopping_cart_items.cart_id
         AND shopping_carts.user_id = $2
         AND shopping_carts.is_active = true
         RETURNING shopping_cart_items.*`,
        [itemId, userId]
      );

      if (result.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Cart item not found' });
      }

      await client.query('COMMIT');
      logger.info(`Removed item ${itemId} from cart for user ${userId}`);
      res.status(204).send();
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error removing item from cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Clear shopping cart
router.delete('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Get user's active cart
      const cartResult = await client.query(
        'SELECT id FROM shopping_carts WHERE user_id = $1 AND is_active = true',
        [userId]
      );

      if (cartResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Shopping cart not found' });
      }

      const cartId = cartResult.rows[0].id;

      // Delete all items from cart
      await client.query(
        'DELETE FROM shopping_cart_items WHERE cart_id = $1',
        [cartId]
      );

      await client.query('COMMIT');
      logger.info(`Cleared shopping cart for user ${userId}`);
      res.status(204).send();
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error clearing shopping cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default shoppingcartRoutes;