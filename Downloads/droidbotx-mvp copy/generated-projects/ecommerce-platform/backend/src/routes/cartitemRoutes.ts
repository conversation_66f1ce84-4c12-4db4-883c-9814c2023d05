// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { cartitemRoutes } from '../services/CartitemRoutes';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import winston from 'winston';

const router = Router();
const cartitemRoutes = new cartitemRoutes();

router.use(authenticateToken);
router.use(validateRequest);







// Initialize router
const router = Router();

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Logger configuration
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'cartitem.log' })
  ]
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret';

// CartItem interface
interface CartItem {
  id: string;
  cart_id: string;
  product_id: string;
  quantity: number;
  price: number;
  created_at: Date;
  updated_at: Date;
}

// Authentication middleware
const authenticateToken = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    logger.warn('Authentication failed: No token provided');
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      logger.warn('Authentication failed: Invalid token');
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    (req as any).user = user;
    next();
  });
};

// Authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: Request, res: Response, next: Function) => {
    const user = (req as any).user;
    if (!user || !roles.includes(user.role)) {
      logger.warn(`Authorization failed for user ${user?.id}`);
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Validation schemas
const cartItemSchema = z.object({
  cart_id: z.string().uuid(),
  product_id: z.string().uuid(),
  quantity: z.number().int().positive(),
  price: z.number().positive()
});

const updateCartItemSchema = z.object({
  quantity: z.number().int().positive().optional(),
  price: z.number().positive().optional()
});

// GET /cartitems - Get all cart items
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT * FROM cart_items ORDER BY created_at DESC'
      );

      logger.info('Retrieved all cart items');
      res.status(200).json(result.rows);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error retrieving cart items:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /cartitems/:id - Get a specific cart item
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({ error: 'Invalid cart item ID format' });
    }

    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT * FROM cart_items WHERE id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        logger.warn(`Cart item not found: ${id}`);
        return res.status(404).json({ error: 'Cart item not found' });
      }

      logger.info(`Retrieved cart item: ${id}`);
      res.status(200).json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error retrieving cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /cartitems - Create a new cart item
router.post('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = cartItemSchema.safeParse(req.body);
    if (!validationResult.success) {
      logger.warn('Cart item validation failed:', validationResult.error);
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.flatten()
      });
    }

    const { cart_id, product_id, quantity, price } = validationResult.data;

    const client = await pool.connect();
    try {
      // Check if cart item already exists
      const existingItem = await client.query(
        'SELECT * FROM cart_items WHERE cart_id = $1 AND product_id = $2',
        [cart_id, product_id]
      );

      if (existingItem.rows.length > 0) {
        logger.warn(`Cart item already exists for cart ${cart_id} and product ${product_id}`);
        return res.status(409).json({ error: 'Cart item already exists' });
      }

      // Create new cart item
      const result = await client.query(
        `INSERT INTO cart_items (cart_id, product_id, quantity, price)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [cart_id, product_id, quantity, price]
      );

      logger.info(`Created cart item: ${result.rows[0].id}`);
      res.status(201).json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error creating cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /cartitems/:id - Update a cart item
router.put('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({ error: 'Invalid cart item ID format' });
    }

    // Validate request body
    const validationResult = updateCartItemSchema.safeParse(req.body);
    if (!validationResult.success) {
      logger.warn('Cart item update validation failed:', validationResult.error);
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.flatten()
      });
    }

    const updateData = validationResult.data;

    // Check if any fields to update
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    const client = await pool.connect();
    try {
      // Check if cart item exists
      const existingItem = await client.query(
        'SELECT * FROM cart_items WHERE id = $1',
        [id]
      );

      if (existingItem.rows.length === 0) {
        logger.warn(`Cart item not found for update: ${id}`);
        return res.status(404).json({ error: 'Cart item not found' });
      }

      // Build dynamic update query
      const fields = [];
      const values = [];
      let index = 1;

      Object.entries(updateData).forEach(([key, value]) => {
        fields.push(`${key} = $${index}`);
        values.push(value);
        index++;
      });

      values.push(id);

      const query = `
        UPDATE cart_items
        SET ${fields.join(', ')}, updated_at = NOW()
        WHERE id = $${index}
        RETURNING *
      `;

      const result = await client.query(query, values);

      logger.info(`Updated cart item: ${id}`);
      res.status(200).json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error updating cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /cartitems/:id - Delete a cart item
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({ error: 'Invalid cart item ID format' });
    }

    const client = await pool.connect();
    try {
      const result = await client.query(
        'DELETE FROM cart_items WHERE id = $1 RETURNING *',
        [id]
      );

      if (result.rows.length === 0) {
        logger.warn(`Cart item not found for deletion: ${id}`);
        return res.status(404).json({ error: 'Cart item not found' });
      }

      logger.info(`Deleted cart item: ${id}`);
      res.status(200).json({ message: 'Cart item deleted successfully' });
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error deleting cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default cartitemRoutes;