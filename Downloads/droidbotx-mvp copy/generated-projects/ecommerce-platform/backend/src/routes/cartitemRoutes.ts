// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { cartitemRoutes } from '../services/CartitemRoutes';
import { logger } from '../core/Logger.js';
import { body, param, validationResult } from 'express-validator';
import { Pool } from 'pg';
import jwt from 'jsonwebtoken';

const router = Router();
const cartitemRoutes = new cartitemRoutes();

router.use(authenticateToken);
router.use(validateRequest);





// Initialize router
const router = express.Router();

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key';

// CartItem Interface
interface CartItem {
  id: number;
  user_id: number;
  product_id: number;
  quantity: number;
  created_at: Date;
  updated_at: Date;
}

// Authentication Middleware
const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    (req as any).user = user;
    next();
  });
};

// Authorization Middleware
const authorizeUser = (req: Request, res: Response, next: NextFunction) => {
  const userId = (req as any).user.id;
  const cartItemId = parseInt(req.params.id);

  // Users can only access their own cart items
  pool.query(
    'SELECT user_id FROM cart_items WHERE id = $1',
    [cartItemId],
    (err, result) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Cart item not found' });
      }

      if (result.rows[0].user_id !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      next();
    }
  );
};

// Validation Rules
const cartItemValidationRules = [
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
];

const cartItemIdValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Cart item ID must be a positive integer'),
];

// Error Formatter
const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// GET /cartitems - Get all cart items for the authenticated user
router.get(
  '/',
  authenticateToken,
  (req: Request, res: Response) => {
    const userId = (req as any).user.id;

    pool.query(
      'SELECT * FROM cart_items WHERE user_id = $1 ORDER BY created_at DESC',
      [userId],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to retrieve cart items' });
        }

        res.status(200).json(result.rows);
      }
    );
  }
);

// GET /cartitems/:id - Get a specific cart item
router.get(
  '/:id',
  authenticateToken,
  cartItemIdValidation,
  handleValidationErrors,
  authorizeUser,
  (req: Request, res: Response) => {
    const cartItemId = parseInt(req.params.id);

    pool.query(
      'SELECT * FROM cart_items WHERE id = $1',
      [cartItemId],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to retrieve cart item' });
        }

        if (result.rows.length === 0) {
          return res.status(404).json({ error: 'Cart item not found' });
        }

        res.status(200).json(result.rows[0]);
      }
    );
  }
);

// POST /cartitems - Create a new cart item
router.post(
  '/',
  authenticateToken,
  cartItemValidationRules,
  handleValidationErrors,
  (req: Request, res: Response) => {
    const userId = (req as any).user.id;
    const { product_id, quantity } = req.body;

    // Check if item already exists in cart
    pool.query(
      'SELECT id, quantity FROM cart_items WHERE user_id = $1 AND product_id = $2',
      [userId, product_id],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to check cart item' });
        }

        if (result.rows.length > 0) {
          // Update existing cart item
          const existingItemId = result.rows[0].id;
          const newQuantity = result.rows[0].quantity + quantity;

          pool.query(
            'UPDATE cart_items SET quantity = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
            [newQuantity, existingItemId],
            (updateErr, updateResult) => {
              if (updateErr) {
                console.error('Database error:', updateErr);
                return res.status(500).json({ error: 'Failed to update cart item' });
              }

              res.status(200).json(updateResult.rows[0]);
            }
          );
        } else {
          // Create new cart item
          pool.query(
            'INSERT INTO cart_items (user_id, product_id, quantity) VALUES ($1, $2, $3) RETURNING *',
            [userId, product_id, quantity],
            (insertErr, insertResult) => {
              if (insertErr) {
                console.error('Database error:', insertErr);
                return res.status(500).json({ error: 'Failed to create cart item' });
              }

              res.status(201).json(insertResult.rows[0]);
            }
          );
        }
      }
    );
  }
);

// PUT /cartitems/:id - Update a cart item
router.put(
  '/:id',
  authenticateToken,
  cartItemIdValidation,
  cartItemValidationRules,
  handleValidationErrors,
  authorizeUser,
  (req: Request, res: Response) => {
    const cartItemId = parseInt(req.params.id);
    const { product_id, quantity } = req.body;

    pool.query(
      'UPDATE cart_items SET product_id = $1, quantity = $2, updated_at = NOW() WHERE id = $3 RETURNING *',
      [product_id, quantity, cartItemId],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to update cart item' });
        }

        if (result.rows.length === 0) {
          return res.status(404).json({ error: 'Cart item not found' });
        }

        res.status(200).json(result.rows[0]);
      }
    );
  }
);

// DELETE /cartitems/:id - Delete a cart item
router.delete(
  '/:id',
  authenticateToken,
  cartItemIdValidation,
  handleValidationErrors,
  authorizeUser,
  (req: Request, res: Response) => {
    const cartItemId = parseInt(req.params.id);

    pool.query(
      'DELETE FROM cart_items WHERE id = $1 RETURNING *',
      [cartItemId],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to delete cart item' });
        }

        if (result.rows.length === 0) {
          return res.status(404).json({ error: 'Cart item not found' });
        }

        res.status(200).json({ message: 'Cart item deleted successfully' });
      }
    );
  }
);

// DELETE /cartitems - Clear all cart items for the authenticated user
router.delete(
  '/',
  authenticateToken,
  (req: Request, res: Response) => {
    const userId = (req as any).user.id;

    pool.query(
      'DELETE FROM cart_items WHERE user_id = $1',
      [userId],
      (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ error: 'Failed to clear cart' });
        }

        res.status(200).json({ message: 'Cart cleared successfully' });
      }
    );
  }
);

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default cartitemRoutes;