// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { userRoutes } from '../services/UserRoutes';
import { logger } from '../core/Logger.js';
import { body, param, validationResult } from 'express-validator';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import pool from '../config/database.js';
import logger from '../utils/logger.js';

const router = Router();
const userRoutes = new userRoutes();

router.use(authenticateToken);
router.use(validateRequest);







const router = express.Router();

// User interface
interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

// JWT payload interface
interface JwtPayload {
  userId: number;
  role: string;
}

// Authentication middleware
const authenticate = (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret') as JwtPayload;

    (req as any).user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    return res.status(401).json({ error: 'Invalid or expired token' });
  }
};

// Authorization middleware
const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = (req as any).user as JwtPayload;
    if (!user || !roles.includes(user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Input validation middleware
const validate = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// GET /users - Get all users (admin only)
router.get(
  '/',
  authenticate,
  authorize('admin'),
  async (req: Request, res: Response) => {
    try {
      const client = await pool.connect();
      try {
        const result = await client.query(;
          'SELECT id, email, first_name, last_name, role, created_at, updated_at FROM users ORDER BY id'
        );

        const users = result.rows.map(row => ({
          id: row.id,
          email: row.email,
          firstName: row.first_name,
          lastName: row.last_name,
          role: row.role,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        }));

        res.json(users);
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Error fetching users:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// GET /users/:id - Get user by ID
router.get(
  '/:id',
  authenticate,
  param('id').isInt({ min: 1 }).withMessage('Invalid user ID'),
  validate,
  async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id, 10);
      const requestingUser = (req as any).user as JwtPayload;

      // Users can only view their own profile unless they're admin'
      if (requestingUser.userId !== userId && requestingUser.role !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      const client = await pool.connect();
      try {
        const result = await client.query(;
          'SELECT id, email, first_name, last_name, role, created_at, updated_at FROM users WHERE id = $1',
          [userId]
        );

        if (result.rows.length === 0) {
          return res.status(404).json({ error: 'User not found' });
        }

        const user = result.rows[0];
        res.json({
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        });
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Error fetching user:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// POST /users - Create new user
router.post(
  '/',
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  validate,
  async (req: Request, res: Response) => {
    try {
      const { email, firstName, lastName, password } = req.body;

      const client = await pool.connect();
      try {
        // Check if user already exists
        const existingUser = await client.query(;
          'SELECT id FROM users WHERE email = $1',
          [email]
        );

        if (existingUser.rows.length > 0) {
          return res.status(409).json({ error: 'User with this email already exists' });
        }

        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Create user
        const result = await client.query(;
          `INSERT INTO users (email, first_name, last_name, password, role)`
           VALUES ($1, $2, $3, $4, $5);
           RETURNING id, email, first_name, last_name, role, created_at, updated_at`,`
          [email, firstName, lastName, hashedPassword, 'customer']
        );

        const newUser = result.rows[0];
        res.status(201).json({
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.first_name,
          lastName: newUser.last_name,
          role: newUser.role,
          createdAt: newUser.created_at,
          updatedAt: newUser.updated_at
        });
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Error creating user:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// PUT /users/:id - Update user
router.put(
  '/:id',
  authenticate,
  param('id').isInt({ min: 1 }).withMessage('Invalid user ID'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('firstName').optional().trim().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').optional().trim().isLength({ min: 1 }).withMessage('Last name is required'),
  validate,
  async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id, 10);
      const requestingUser = (req as any).user as JwtPayload;
      const { email, firstName, lastName } = req.body;

      // Users can only update their own profile unless they're admin'
      if (requestingUser.userId !== userId && requestingUser.role !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      const client = await pool.connect();
      try {
        // Check if user exists
        const existingUser = await client.query(;
          'SELECT id, email FROM users WHERE id = $1',
          [userId]
        );

        if (existingUser.rows.length === 0) {
          return res.status(404).json({ error: 'User not found' });
        }

        // If email is being updated, check if it's already taken'
        if (email && email !== existingUser.rows[0].email) {
          const emailCheck = await client.query(;
            'SELECT id FROM users WHERE email = $1 AND id != $2',
            [email, userId]
          );

          if (emailCheck.rows.length > 0) {
            return res.status(409).json({ error: 'Email is already in use' });
          }
        }

        // Update user
        const result = await client.query(;
          `UPDATE users`
           SET email = COALESCE($1, email),
               first_name = COALESCE($2, first_name),
               last_name = COALESCE($3, last_name),
               updated_at = NOW()
           WHERE id = $4
           RETURNING id, email, first_name, last_name, role, created_at, updated_at`,`
          [email, firstName, lastName, userId]
        );

        const updatedUser = result.rows[0];
        res.json({
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          role: updatedUser.role,
          createdAt: updatedUser.created_at,
          updatedAt: updatedUser.updated_at
        });
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Error updating user:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// DELETE /users/:id - Delete user
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  param('id').isInt({ min: 1 }).withMessage('Invalid user ID'),
  validate,
  async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id, 10);
      const requestingUser = (req as any).user as JwtPayload;

      // Prevent users from deleting themselves
      if (requestingUser.userId === userId) {
        return res.status(400).json({ error: 'Cannot delete your own account' });
      }

      const client = await pool.connect();
      try {
        // Check if user exists
        const existingUser = await client.query(;
          'SELECT id FROM users WHERE id = $1',
          [userId]
        );

        if (existingUser.rows.length === 0) {
          return res.status(404).json({ error: 'User not found' });
        }

        // Delete user
        await client.query('DELETE FROM users WHERE id = $1', [userId]);

        res.status(204).send();
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Error deleting user:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// POST /users/login - User login
router.post(
  '/login',
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').exists().withMessage('Password is required'),
  validate,
  async (req: Request, res: Response) => {
    try {
      const { email, password } = req.body;

      const client = await pool.connect();
      try {
        // Find user by email
        const result = await client.query(;
          'SELECT id, email, first_name, last_name, password, role FROM users WHERE email = $1',
          [email]
        );

        if (result.rows.length === 0) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = result.rows[0];

        // Check password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate JWT token
        const token = jwt.sign(;
          { userId: user.id, role: user.role },
          process.env.JWT_SECRET || 'fallback_secret',
          { expiresIn: '24h' }
        );

        res.json({
          token,
          user: {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            role: user.role
          }
        });
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default userRoutes;
}}}