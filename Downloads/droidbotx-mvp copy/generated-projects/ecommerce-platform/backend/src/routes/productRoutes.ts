// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { productRoutes } from '../services/ProductRoutes';
import { logger } from '../core/Logger.js';
import { body, param, validationResult } from 'express-validator';
import { Pool } from 'pg';
import rateLimit from 'express-rate-limit';
import jwt from 'jsonwebtoken';

const router = Router();
const productRoutes = new productRoutes();

router.use(authenticateToken);
router.use(validateRequest);






// Initialize router
const router = express.Router();

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key';

// Product interface
interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  stock_quantity: number;
  created_at: Date;
  updated_at: Date;
}

// User interface for authentication
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    role: string;
  };
}

// Authentication middleware
const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user as { id: number; role: string };
    next();
  });
};

// Authorization middleware
const authorizeRole = (...roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Rate limiting middleware
const createProductLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many product creation requests from this IP',
});

// Validation rules
const productIdValidation = param('id').isInt({ min: 1 }).withMessage('Product ID must be a positive integer');
const productValidationRules = [
  body('name').isString().isLength({ min: 1, max: 255 }).withMessage('Product name is required and must be less than 255 characters'),
  body('description').isString().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('category').isString().isLength({ min: 1, max: 100 }).withMessage('Category is required and must be less than 100 characters'),
  body('stock_quantity').isInt({ min: 0 }).withMessage('Stock quantity must be a non-negative integer'),
];

// GET /products - Get all products
router.get('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { rows } = await pool.query<Product>(
      'SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products ORDER BY created_at DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('Error fetching products:', error);
    next(error);
  }
});

// GET /products/:id - Get a specific product
router.get('/:id', productIdValidation, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const productId = parseInt(req.params.id, 10);
    const { rows } = await pool.query<Product>(
      'SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products WHERE id = $1',
      [productId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching product:', error);
    next(error);
  }
});

// POST /products - Create a new product
router.post(
  '/',
  createProductLimiter,
  authenticateToken,
  authorizeRole('admin', 'manager'),
  productValidationRules,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, description, price, category, stock_quantity } = req.body;

      const { rows } = await pool.query(
        'INSERT INTO products (name, description, price, category, stock_quantity) VALUES ($1, $2, $3, $4, $5) RETURNING *',
        [name, description, price, category, stock_quantity]
      );

      res.status(201).json(rows[0]);
    } catch (error) {
      console.error('Error creating product:', error);
      next(error);
    }
  }
);

// PUT /products/:id - Update a product
router.put(
  '/:id',
  authenticateToken,
  authorizeRole('admin', 'manager'),
  productIdValidation,
  productValidationRules,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const productId = parseInt(req.params.id, 10);
      const { name, description, price, category, stock_quantity } = req.body;

      // Check if product exists
      const existingProduct = await pool.query('SELECT id FROM products WHERE id = $1', [productId]);
      if (existingProduct.rows.length === 0) {
        return res.status(404).json({ error: 'Product not found' });
      }

      const { rows } = await pool.query(
        'UPDATE products SET name = $1, description = $2, price = $3, category = $4, stock_quantity = $5, updated_at = NOW() WHERE id = $6 RETURNING *',
        [name, description, price, category, stock_quantity, productId]
      );

      res.json(rows[0]);
    } catch (error) {
      console.error('Error updating product:', error);
      next(error);
    }
  }
);

// DELETE /products/:id - Delete a product
router.delete(
  '/:id',
  authenticateToken,
  authorizeRole('admin'),
  productIdValidation,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const productId = parseInt(req.params.id, 10);

      // Check if product exists
      const existingProduct = await pool.query('SELECT id FROM products WHERE id = $1', [productId]);
      if (existingProduct.rows.length === 0) {
        return res.status(404).json({ error: 'Product not found' });
      }

      await pool.query('DELETE FROM products WHERE id = $1', [productId]);

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting product:', error);
      next(error);
    }
  }
);

// Error handling middleware
router.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Product route error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default productRoutes;