// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { productRoutes } from '../services/ProductRoutes';
import { logger } from '../core/Logger.js';
import { body, validationResult } from 'express-validator';
import { Pool } from 'pg';
import jwt from 'jsonwebtoken';

const router = Router();
const productRoutes = new productRoutes();

router.use(authenticateToken);
router.use(validateRequest);






interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock_quantity: number;
  created_at: Date;
  updated_at: Date;
}

interface AuthRequest extends Request {
  user?: {
    id: string;
    role: string;
  };
}

const productRoutes = Router();
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Authentication middleware
const authenticateToken = (req: AuthRequest, res: Response, next: any) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user as { id: string; role: string };
    next();
  });
};

// Authorization middleware for admin roles
const authorizeAdmin = (req: AuthRequest, res: Response, next: any) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// GET /products - Get all products
productRoutes.get('/', async (req: Request, res: Response) => {
  try {
    const result = await pool.query(
      'SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products ORDER BY created_at DESC'
    );
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /products/:id - Get product by ID
productRoutes.get('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const result = await pool.query(
      'SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /products - Create new product
productRoutes.post(
  '/',
  authenticateToken,
  authorizeAdmin,
  [
    body('name').isString().notEmpty().trim(),
    body('description').isString().notEmpty().trim(),
    body('price').isNumeric().isFloat({ gt: 0 }),
    body('category').isString().notEmpty().trim(),
    body('stock_quantity').isNumeric().isInt({ min: 0 }),
  ],
  async (req: AuthRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description, price, category, stock_quantity } = req.body;

    try {
      const result = await pool.query(
        `INSERT INTO products (name, description, price, category, stock_quantity)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id, name, description, price, category, stock_quantity, created_at, updated_at`,
        [name, description, price, category, stock_quantity]
      );

      res.status(201).json(result.rows[0]);
    } catch (error) {
      console.error('Error creating product:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// PUT /products/:id - Update product
productRoutes.put(
  '/:id',
  authenticateToken,
  authorizeAdmin,
  [
    body('name').optional().isString().notEmpty().trim(),
    body('description').optional().isString().notEmpty().trim(),
    body('price').optional().isNumeric().isFloat({ gt: 0 }),
    body('category').optional().isString().notEmpty().trim(),
    body('stock_quantity').optional().isNumeric().isInt({ min: 0 }),
  ],
  async (req: AuthRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { name, description, price, category, stock_quantity } = req.body;

    try {
      const fields = [];
      const values = [];
      let index = 1;

      if (name !== undefined) {
        fields.push(`name = $${index}`);
        values.push(name);
        index++;
      }
      if (description !== undefined) {
        fields.push(`description = $${index}`);
        values.push(description);
        index++;
      }
      if (price !== undefined) {
        fields.push(`price = $${index}`);
        values.push(price);
        index++;
      }
      if (category !== undefined) {
        fields.push(`category = $${index}`);
        values.push(category);
        index++;
      }
      if (stock_quantity !== undefined) {
        fields.push(`stock_quantity = $${index}`);
        values.push(stock_quantity);
        index++;
      }

      if (fields.length === 0) {
        return res.status(400).json({ error: 'No valid fields provided for update' });
      }

      values.push(id);
      const query = `
        UPDATE products
        SET ${fields.join(', ')}, updated_at = NOW()
        WHERE id = $${index}
        RETURNING id, name, description, price, category, stock_quantity, created_at, updated_at`;

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Product not found' });
      }

      res.json(result.rows[0]);
    } catch (error) {
      console.error('Error updating product:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// DELETE /products/:id - Delete product
productRoutes.delete('/:id', authenticateToken, authorizeAdmin, async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  try {
    const result = await pool.query(
      'DELETE FROM products WHERE id = $1 RETURNING id',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default productRoutes;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default productRoutes;