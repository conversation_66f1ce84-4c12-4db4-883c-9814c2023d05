```typescript
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';

// Import route handlers
import productRoutes from './products';
import productCategoryRoutes from './productCategories';
import userRoutes from './users';
import shoppingCartRoutes from './shoppingCarts';
import cartItemRoutes from './cartItems';
import orderRoutes from './orders';
import orderItemRoutes from './orderItems';

// Import authentication middleware
import { authenticateToken, requireAdmin } from '../middleware/auth';

// Create router instance
const router = express.Router();

// Security middleware
router.use(helmet());
router.use(cors());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
router.use(limiter);

// Logging middleware
router.use(morgan('combined'));

// Body parsing middleware
router.use(express.json());
router.use(express.urlencoded({ extended: true }));

// Authentication middleware for all API routes
router.use('/api', authenticateToken);

// Mount entity routes with proper prefixes
router.use('/api/products', productRoutes);
router.use('/api/product-categories', productCategoryRoutes);
router.use('/api/users', userRoutes);
router.use('/api/shopping-carts', shoppingCartRoutes);
router.use('/api/cart-items', cartItemRoutes);
router.use('/api/orders', orderRoutes);
router.use('/api/order-items', orderItemRoutes);

// Health check endpoint
router.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
router.use((err: any, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: err.message,
      details: err.errors
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or missing authentication token'
    });
  }
  
  if (err.name === 'ForbiddenError') {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Insufficient permissions to access this resource'
    });
  }
  
  // Default error response
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
  });
});

// 404 handler for unmatched routes
router.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

export default router;
```