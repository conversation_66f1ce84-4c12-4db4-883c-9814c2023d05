// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { orderitemRoutes } from '../services/OrderitemRoutes';
import { logger } from '../core/Logger.js';
import { body, validationResult } from 'express-validator';
import { Pool } from 'pg';
import jwt from 'jsonwebtoken';

const router = Router();
const orderitemRoutes = new orderitemRoutes();

router.use(authenticateToken);
router.use(validateRequest);






// OrderItem interface
interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
}

// Database connection pool
const pool: Pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Authentication middleware
const authenticateToken = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    (req as any).user = user;
    next();
  });
};

// Authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: Request, res: Response, next: Function) => {
    const user = (req as any).user;
    if (!user || !roles.includes(user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

const router = Router();

// Validation rules
const validateOrderItem = [
  body('orderId').isUUID().withMessage('Valid orderId is required'),
  body('productId').isUUID().withMessage('Valid productId is required'),
  body('quantity').isInt({ min: 1 }).withMessage('Quantity must be a positive integer'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a non-negative number')
];

// GET /orderitems - Get all order items
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const result = await pool.query('SELECT * FROM order_items');
    const orderItems: OrderItem[] = result.rows;
    res.json(orderItems);
  } catch (error) {
    console.error('Error fetching order items:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /orderitems/:id - Get a specific order item
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  const id = req.params.id;

  try {
    const result = await pool.query('SELECT * FROM order_items WHERE id = $1', [id]);
    const orderItem: OrderItem = result.rows[0];

    if (!orderItem) {
      return res.status(404).json({ error: 'Order item not found' });
    }

    res.json(orderItem);
  } catch (error) {
    console.error('Error fetching order item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /orderitems - Create a new order item
router.post('/', authenticateToken, authorizeRole(['admin']), validateOrderItem, async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { orderId, productId, quantity, price }: OrderItem = req.body;

  try {
    const result = await pool.query(
      'INSERT INTO order_items (order_id, product_id, quantity, price) VALUES ($1, $2, $3, $4) RETURNING *',
      [orderId, productId, quantity, price]
    );
    const newOrderItem: OrderItem = result.rows[0];
    res.status(201).json(newOrderItem);
  } catch (error) {
    console.error('Error creating order item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /orderitems/:id - Update an order item
router.put('/:id', authenticateToken, authorizeRole(['admin']), validateOrderItem, async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const id = req.params.id;
  const { orderId, productId, quantity, price }: OrderItem = req.body;

  try {
    const result = await pool.query(
      'UPDATE order_items SET order_id = $1, product_id = $2, quantity = $3, price = $4 WHERE id = $5 RETURNING *',
      [orderId, productId, quantity, price, id]
    );
    const updatedOrderItem: OrderItem = result.rows[0];

    if (!updatedOrderItem) {
      return res.status(404).json({ error: 'Order item not found' });
    }

    res.json(updatedOrderItem);
  } catch (error) {
    console.error('Error updating order item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /orderitems/:id - Delete an order item
router.delete('/:id', authenticateToken, authorizeRole(['admin']), async (req: Request, res: Response) => {
  const id = req.params.id;

  try {
    const result = await pool.query('DELETE FROM order_items WHERE id = $1 RETURNING *', [id]);
    const deletedOrderItem: OrderItem = result.rows[0];

    if (!deletedOrderItem) {
      return res.status(404).json({ error: 'Order item not found' });
    }

    res.json({ message: 'Order item deleted successfully' });
  } catch (error) {
    console.error('Error deleting order item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default orderitemRoutes;