
















// Import routes




// Load environment variables
import { Pool } from 'pg';
import cors from 'cors';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import authRoutes from './routes/auth.js';
import cartRoutes from './routes/cart.js';
import productRoutes from './routes/products.js';
import userRoutes from './routes/users.js';

dotenv.config();

// Create Express app
const app: Application = express();
const PORT: number = parseInt(process.env.PORT as string, 10) || 3000;

// Database connection
export const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT as string, 10) || 5432,
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Routes
app.use('/api/products', productRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
interface ErrorResponse {
  status: number;
  message: string;
  details?: any;
}

app.use((err: any, req: Request, res: Response<ErrorResponse>, next: NextFunction) => {
  console.error(err.stack);
  
  const errorResponse: ErrorResponse = {
    status: err.status || 500,
    message: err.message || 'Internal Server Error',
  };
  
  if (process.env.NODE_ENV === 'development') {
    errorResponse.details = err.stack;
  }
  
  res.status(errorResponse.status).json(errorResponse);
});

// 404 handler
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({ status: 404, message: 'Route not found' });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    const client = await pool.connect();
    console.log('Database connected successfully');
    client.release();
    
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

export default app;
`