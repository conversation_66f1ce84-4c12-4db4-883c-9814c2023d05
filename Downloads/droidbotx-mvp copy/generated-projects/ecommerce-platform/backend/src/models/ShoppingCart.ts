```typescript
/**
 * ShoppingCart entity interface
 * Represents a user's shopping cart in an e-commerce application
 */
export interface ShoppingCart {
  /**
   * Unique identifier for the shopping cart
   * @type {string}
   * @required
   */
  id: string;

  /**
   * Identifier of the user who owns this shopping cart
   * @type {string}
   * @required
   */
  userId: string;

  /**
   * Creation timestamp of the shopping cart
   * @type {string}
   * @required
   * @format ISO 8601 date string
   */
  createdAt: string;

  /**
   * Last update timestamp of the shopping cart
   * @type {string}
   * @required
   * @format ISO 8601 date string
   */
  updatedAt: string;

  /**
   * Status indicating if the shopping cart is active
   * @type {boolean}
   * @required
   */
  isActive: boolean;
}
```