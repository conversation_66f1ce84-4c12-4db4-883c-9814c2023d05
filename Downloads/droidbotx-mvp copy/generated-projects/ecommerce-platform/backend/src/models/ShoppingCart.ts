```typescript
/**
 * ShoppingCart entity interface for e-commerce application
 * Represents temporary storage of products a user intends to purchase
 */
export interface ShoppingCart {
  /**
   * Unique identifier for the shopping cart
   * @type {string}
   * @required
   * @format UUID
   */
  id: string;

  /**
   * Identifier of the user who owns this shopping cart
   * @type {string}
   * @required
   * @format UUID
   */
  userId: string;

  /**
   * Creation timestamp of the shopping cart
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   */
  createdAt: string;

  /**
   * Last update timestamp of the shopping cart
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   */
  updatedAt: string;
}
```