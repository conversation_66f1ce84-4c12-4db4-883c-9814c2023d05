




import { Pool } from 'pg';
import { OrderNotFoundException } from '../exceptions/OrderNotFoundException.js';
import { ValidationError } from '../exceptions/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

export class OrderModel {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async create(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
    this.logger.info('Creating new order', { userId: order.userId });

    // Validate order
    this.validateOrder(order);

    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Insert order
      const orderResult = await client.query(
        `INSERT INTO orders (user_id, total_amount, status)
         VALUES ($1, $2, $3)
         RETURNING id, created_at, updated_at`,
        [order.userId, order.totalAmount, order.status]
      );

      const orderId = orderResult.rows[0].id;
      const createdAt = orderResult.rows[0].created_at;
      const updatedAt = orderResult.rows[0].updated_at;

      // Insert order items
      for (const item of order.items) {
        await client.query(
          `INSERT INTO order_items (order_id, product_id, quantity, price)
           VALUES ($1, $2, $3, $4)`,
          [orderId, item.productId, item.quantity, item.price]
        );
      }

      await client.query('COMMIT');

      const newOrder: Order = {
        id: orderId,
        userId: order.userId,
        items: order.items,
        totalAmount: order.totalAmount,
        status: order.status,
        createdAt,
        updatedAt
      };

      this.logger.info('Order created successfully', { orderId });
      return newOrder;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to create order', { error });
      throw error;
    } finally {
      client.release();
    }
  }

  async read(id: string): Promise<Order> {
    this.logger.info('Fetching order', { orderId: id });

    const client = await this.pool.connect();
    try {
      // Fetch order
      const orderResult = await client.query(
        `SELECT id, user_id, total_amount, status, created_at, updated_at
         FROM orders
         WHERE id = $1`,
        [id]
      );

      if (orderResult.rows.length === 0) {
        throw new OrderNotFoundException(`Order with ID ${id} not found`);
      }

      const orderData = orderResult.rows[0];

      // Fetch order items
      const itemsResult = await client.query(
        `SELECT product_id, quantity, price
         FROM order_items
         WHERE order_id = $1`,
        [id]
      );

      const order: Order = {
        id: orderData.id,
        userId: orderData.user_id,
        items: itemsResult.rows.map(row => ({
          productId: row.product_id,
          quantity: row.quantity,
          price: row.price
        })),
        totalAmount: orderData.total_amount,
        status: orderData.status,
        createdAt: orderData.created_at,
        updatedAt: orderData.updated_at
      };

      this.logger.info('Order fetched successfully', { orderId: id });
      return order;
    } finally {
      client.release();
    }
  }

  async update(id: string, order: Partial<Omit<Order, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Order> {
    this.logger.info('Updating order', { orderId: id });

    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Check if order exists
      const existingOrder = await this.read(id);

      // Update order
      const updateFields = [];
      const updateValues = [];
      let paramCount = 1;

      if (order.userId !== undefined) {
        updateFields.push(`user_id = $${paramCount}`);
        updateValues.push(order.userId);
        paramCount++;
      }

      if (order.totalAmount !== undefined) {
        updateFields.push(`total_amount = $${paramCount}`);
        updateValues.push(order.totalAmount);
        paramCount++;
      }

      if (order.status !== undefined) {
        updateFields.push(`status = $${paramCount}`);
        updateValues.push(order.status);
        paramCount++;
      }

      updateFields.push(`updated_at = NOW()`);
      updateValues.push(id); // Add id for WHERE clause

      const updateQuery = `
        UPDATE orders
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING updated_at
      `;

      const result = await client.query(updateQuery, updateValues);
      const updatedAt = result.rows[0].updated_at;

      // If items are being updated, replace all existing items
      if (order.items) {
        // Delete existing items
        await client.query('DELETE FROM order_items WHERE order_id = $1', [id]);

        // Insert new items
        for (const item of order.items) {
          await client.query(
            `INSERT INTO order_items (order_id, product_id, quantity, price)
             VALUES ($1, $2, $3, $4)`,
            [id, item.productId, item.quantity, item.price]
          );
        }
      }

      await client.query('COMMIT');

      // Return updated order
      const updatedOrder = await this.read(id);
      updatedOrder.updatedAt = updatedAt;

      this.logger.info('Order updated successfully', { orderId: id });
      return updatedOrder;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to update order', { orderId: id, error });
      throw error;
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting order', { orderId: id });

    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Delete order items first (foreign key constraint)
      await client.query('DELETE FROM order_items WHERE order_id = $1', [id]);

      // Delete order
      const result = await client.query('DELETE FROM orders WHERE id = $1', [id]);

      if (result.rowCount === 0) {
        throw new OrderNotFoundException(`Order with ID ${id} not found`);
      }

      await client.query('COMMIT');
      this.logger.info('Order deleted successfully', { orderId: id });
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to delete order', { orderId: id, error });
      throw error;
    } finally {
      client.release();
    }
  }

  async placeOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
    this.logger.info('Placing new order', { userId: order.userId });

    // Set default status for new orders
    const orderToCreate = {
      ...order,
      status: 'pending' as const
    };

    return await this.create(orderToCreate);
  }

  async updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
    this.logger.info('Updating order status', { orderId: id, status });

    return await this.update(id, { status });
  }

  private validateOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): void {
    if (!order.userId) {
      throw new ValidationError('User ID is required');
    }

    if (!order.items || order.items.length === 0) {
      throw new ValidationError('Order must have at least one item');
    }

    if (order.totalAmount <= 0) {
      throw new ValidationError('Total amount must be greater than zero');
    }

    for (const item of order.items) {
      if (!item.productId) {
        throw new ValidationError('Product ID is required for all items');
      }

      if (item.quantity <= 0) {
        throw new ValidationError('Item quantity must be greater than zero');
      }

      if (item.price < 0) {
        throw new ValidationError('Item price cannot be negative');
      }
    }
  }
}