



// Interfaces
import { Pool, QueryResult } from 'pg';
import { BusinessError, DatabaseError, ValidationError } from '../utils/CustomErrors.js';
import { Logger } from '../utils/Logger.js';

interface CartItem {
  productId: number;
  quantity: number;
  price: number;
}

interface ShoppingCart {
  id?: number;
  userId: number;
  items: CartItem[];
  createdAt: Date;
  updatedAt: Date;
}

// Custom exceptions
class CartNotFoundError extends BusinessError {
  constructor(cartId: number) {
    super(`Shopping cart with ID ${cartId} not found`);
    this.name = 'CartNotFoundError';
  }
}

class InvalidCartDataError extends ValidationError {
  constructor(message: string) {
    super(`Invalid cart data: ${message}`);
    this.name = 'InvalidCartDataError';
  }
}

// Repository interface
interface ShoppingCartRepository {
  create(cart: ShoppingCart): Promise<ShoppingCart>;
  findById(id: number): Promise<ShoppingCart | null>;
  findByUserId(userId: number): Promise<ShoppingCart | null>;
  update(cart: ShoppingCart): Promise<ShoppingCart>;
  delete(id: number): Promise<boolean>;
}

// Repository implementation
class PostgresShoppingCartRepository implements ShoppingCartRepository {
  private logger: Logger;

  constructor(private dbPool: Pool) {
    this.logger = new Logger('PostgresShoppingCartRepository');
  }

  async create(cart: ShoppingCart): Promise<ShoppingCart> {
    try {
      const query = `;`
        INSERT INTO shopping_carts (user_id, items, created_at, updated_at)
        VALUES ($1, $2, $3, $4);
        RETURNING id, user_id, items, created_at, updated_at
      `;`

      const values = [;
        cart.userId,
        JSON.stringify(cart.items),
        cart.createdAt,
        cart.updatedAt
      ];

      const result: QueryResult = await this.dbPool.query(query, values);
      const createdCart = result.rows[0];

      this.logger.info(`Created shopping cart for user ${cart.userId} with ID ${createdCart.id}`);

      return {
        id: createdCart.id,
        userId: createdCart.user_id,
        items: createdCart.items,
        createdAt: createdCart.created_at,
        updatedAt: createdCart.updated_at
      };
    } catch (error) {
      this.logger.error('Error creating shopping cart:', error);
      throw new DatabaseError('Failed to create shopping cart');
    }
  }

  async findById(id: number): Promise<ShoppingCart | null> {
    try {
      const query = `;`
        SELECT id, user_id, items, created_at, updated_at
        FROM shopping_carts
        WHERE id = $1
      `;`

      const result: QueryResult = await this.dbPool.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      const cart = result.rows[0];

      return {
        id: cart.id,
        userId: cart.user_id,
        items: cart.items,
        createdAt: cart.created_at,
        updatedAt: cart.updated_at
      };
    } catch (error) {
      this.logger.error(`Error finding shopping cart by ID ${id}:`, error);
      throw new DatabaseError('Failed to retrieve shopping cart');
    }
  }

  async findByUserId(userId: number): Promise<ShoppingCart | null> {
    try {
      const query = `;`
        SELECT id, user_id, items, created_at, updated_at
        FROM shopping_carts
        WHERE user_id = $1
      `;`

      const result: QueryResult = await this.dbPool.query(query, [userId]);

      if (result.rows.length === 0) {
        return null;
      }

      const cart = result.rows[0];

      return {
        id: cart.id,
        userId: cart.user_id,
        items: cart.items,
        createdAt: cart.created_at,
        updatedAt: cart.updated_at
      };
    } catch (error) {
      this.logger.error(`Error finding shopping cart by user ID ${userId}:`, error);
      throw new DatabaseError('Failed to retrieve shopping cart');
    }
  }

  async update(cart: ShoppingCart): Promise<ShoppingCart> {
    if (!cart.id) {
      throw new InvalidCartDataError('Cart ID is required for update');
    }

    try {
      const query = `;`
        UPDATE shopping_carts
        SET user_id = $1, items = $2, updated_at = $3
        WHERE id = $4
        RETURNING id, user_id, items, created_at, updated_at
      `;`

      const values = [;
        cart.userId,
        JSON.stringify(cart.items),
        cart.updatedAt,
        cart.id
      ];

      const result: QueryResult = await this.dbPool.query(query, values);

      if (result.rows.length === 0) {
        throw new CartNotFoundError(cart.id);
      }

      const updatedCart = result.rows[0];

      this.logger.info(`Updated shopping cart with ID ${cart.id}`);

      return {
        id: updatedCart.id,
        userId: updatedCart.user_id,
        items: updatedCart.items,
        createdAt: updatedCart.created_at,
        updatedAt: updatedCart.updated_at
      };
    } catch (error) {
      if (error instanceof CartNotFoundError) {
        throw error;
      }

      this.logger.error(`Error updating shopping cart with ID ${cart.id}:`, error);
      throw new DatabaseError('Failed to update shopping cart');
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const query = 'DELETE FROM shopping_carts WHERE id = $1';
      const result: QueryResult = await this.dbPool.query(query, [id]);

      const deleted = result.rowCount > 0;

      if (deleted) {
        this.logger.info(`Deleted shopping cart with ID ${id}`);
      } else {
        this.logger.warn(`Attempted to delete non-existent shopping cart with ID ${id}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Error deleting shopping cart with ID ${id}:`, error);
      throw new DatabaseError('Failed to delete shopping cart');
    }
  }
}

// Service class
class ShoppingCartModel {
  private logger: Logger;
  private repository: ShoppingCartRepository;

  constructor(dbPool: Pool) {
    this.logger = new Logger('ShoppingCartModel');
    this.repository = new PostgresShoppingCartRepository(dbPool);
  }

  /**
   * Validates shopping cart data
   */
  private validateCartData(cart: ShoppingCart): void {
    if (!cart.userId) {
      throw new InvalidCartDataError('User ID is required');
    }

    if (cart.userId <= 0) {
      throw new InvalidCartDataError('User ID must be a positive integer');
    }

    if (cart.items) {
      for (const item of cart.items) {
        if (!item.productId || item.productId <= 0) {
          throw new InvalidCartDataError('Each item must have a valid product ID');
        }

        if (!item.quantity || item.quantity <= 0) {
          throw new InvalidCartDataError('Each item must have a positive quantity');
        }

        if (item.price < 0) {
          throw new InvalidCartDataError('Item price cannot be negative');
        }
      }
    }
  }

  /**
   * Creates a new shopping cart
   */
  async createCart(userId: number): Promise<ShoppingCart> {
    this.logger.info(`Creating new shopping cart for user ${userId}`);

    const newCart: ShoppingCart = {
      userId,
      items: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.validateCartData(newCart);

    try {
      const createdCart = await this.repository.create(newCart);
      this.logger.info(`Successfully created shopping cart with ID ${createdCart.id} for user ${userId}`);
      return createdCart;
    } catch (error) {
      this.logger.error(`Failed to create shopping cart for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Gets the contents of a shopping cart
   */
  async getCartContents(cartId: number): Promise<ShoppingCart> {
    this.logger.info(`Retrieving contents of shopping cart ${cartId}`);

    try {
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new CartNotFoundError(cartId);
      }

      this.logger.info(`Successfully retrieved contents of shopping cart ${cartId}`);
      return cart;
    } catch (error) {
      if (error instanceof CartNotFoundError) {
        throw error;
      }

      this.logger.error(`Failed to retrieve contents of shopping cart ${cartId}:`, error);
      throw new DatabaseError('Failed to retrieve cart contents');
    }
  }

  /**
   * Clears all items from a shopping cart
   */
  async clearCart(cartId: number): Promise<ShoppingCart> {
    this.logger.info(`Clearing shopping cart ${cartId}`);

    try {
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new CartNotFoundError(cartId);
      }

      cart.items = [];
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);
      this.logger.info(`Successfully cleared shopping cart ${cartId}`);
      return updatedCart;
    } catch (error) {
      if (error instanceof CartNotFoundError) {
        throw error;
      }

      this.logger.error(`Failed to clear shopping cart ${cartId}:`, error);
      throw new DatabaseError('Failed to clear shopping cart');
    }
  }

  /**
   * Adds an item to the shopping cart
   */
  async addItemToCart(cartId: number, item: CartItem): Promise<ShoppingCart> {
    this.logger.info(`Adding item to shopping cart ${cartId}`);

    try {
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new CartNotFoundError(cartId);
      }

      // Check if item already exists in cart
      const existingItemIndex = cart.items.findIndex(;
        cartItem => cartItem.productId === item.productId
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        cart.items[existingItemIndex].quantity += item.quantity;
      } else {
        // Add new item if it doesn't exist'
        cart.items.push(item);
      }

      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);
      this.logger.info(`Successfully added item to shopping cart ${cartId}`);
      return updatedCart;
    } catch (error) {
      if (error instanceof CartNotFoundError) {
        throw error;
      }

      this.logger.error(`Failed to add item to shopping cart ${cartId}:`, error);
      throw new DatabaseError('Failed to add item to cart');
    }
  }

  /**
   * Removes an item from the shopping cart
   */
  async removeItemFromCart(cartId: number, productId: number): Promise<ShoppingCart> {
    this.logger.info(`Removing item from shopping cart ${cartId}`);

    try {
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new CartNotFoundError(cartId);
      }

      cart.items = cart.items.filter(item => item.productId !== productId);
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);
      this.logger.info(`Successfully removed item from shopping cart ${cartId}`);
      return updatedCart;
    } catch (error) {
      if (error instanceof CartNotFoundError) {
        throw error;
      }

      this.logger.error(`Failed to remove item from shopping cart ${cartId}:`, error);
      throw new DatabaseError('Failed to remove item from cart');
    }
  }

  /**
   * Updates the quantity of an item in the shopping cart
   */
  async updateItemQuantity(
    cartId: number,
    productId: number,
    quantity: number
  ): Promise<ShoppingCart> {
    if (quantity <= 0) {
      return this.removeItemFromCart(cartId, productId);
    }

    this.logger.info(`Updating item quantity in shopping cart ${cartId}`);

    try {
      const cart = await this.repository.findById(cartId);

      if (!cart) {
        throw new CartNotFoundError(cartId);
      }

      const itemIndex = cart.items.findIndex(item => item.productId === productId);

      if (itemIndex < 0) {
        throw new InvalidCartDataError(`Product ${productId} not found in cart`);
      }

      cart.items[itemIndex].quantity = quantity;
      cart.updatedAt = new Date();

      const updatedCart = await this.repository.update(cart);
      this.logger.info(`Successfully updated item quantity in shopping cart ${cartId}`);
      return updatedCart;
    } catch (error) {
      if (error instanceof CartNotFoundError || error instanceof InvalidCartDataError) {
        throw error;
      }

      this.logger.error(`Failed to update item quantity in shopping cart ${cartId}:`, error);
      throw new DatabaseError('Failed to update item quantity');
    }
  }

  /**
   * Gets a user's shopping cart'
   */
  async getUserCart(userId: number): Promise<ShoppingCart | null> {
    this.logger.info(`Retrieving shopping cart for user ${userId}`);

    try {
      const cart = await this.repository.findByUserId(userId);
      this.logger.info(cart
        ? `Successfully retrieved shopping cart for user ${userId}`
        : `No shopping cart found for user ${userId}`
      );
      return cart;
    } catch (error) {
      this.logger.error(`Failed to retrieve shopping cart for user ${userId}:`, error);
      throw new DatabaseError('Failed to retrieve user cart');
    }
  }
}

export {
  ShoppingCart,
  CartItem,
  ShoppingCartModel,
  CartNotFoundError,
  InvalidCartDataError
};
}}}