



// Interfaces
import { Pool, QueryResult } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import winston from 'winston';

interface ShoppingCartItem {
  productId: string;
  quantity: number;
  price: number;
}

interface ShoppingCart {
  id: string;
  userId: string;
  items: ShoppingCartItem[];
  createdAt: Date;
  updatedAt: Date;
}

// Custom Exceptions
class BusinessError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BusinessError';
  }
}

class ShoppingCartNotFoundError extends BusinessError {
  constructor(id: string) {
    super(`Shopping cart with id ${id} not found`);
    this.name = 'ShoppingCartNotFoundError';
  }
}

class InvalidCartItemError extends BusinessError {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidCartItemError';
  }
}

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'shopping-cart-service.log' })
  ]
});

// Database connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'ecommerce',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
});

class ShoppingCartModel {
  private db: Pool;

  constructor(database: Pool = pool) {
    this.db = database;
    logger.info('ShoppingCartModel initialized');
  }

  async create(userId: string): Promise<ShoppingCart> {
    logger.info(`Creating shopping cart for user ${userId}`);

    try {
      const id = uuidv4();
      const now = new Date();

      const query = `;`
        INSERT INTO shopping_carts (id, user_id, items, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5);
        RETURNING *`;`

      const values = [id, userId, JSON.stringify([]), now, now];
      const result: QueryResult = await this.db.query(query, values);

      if (result.rowCount === 0) {
        throw new BusinessError('Failed to create shopping cart');
      }

      const cart = this.mapRowToShoppingCart(result.rows[0]);
      logger.info(`Created shopping cart ${cart.id} for user ${userId}`);
      return cart;
    } catch (error) {
      logger.error('Error creating shopping cart', { error, userId });
      throw error;
    }
  }

  async read(id: string): Promise<ShoppingCart> {
    logger.info(`Reading shopping cart ${id}`);

    try {
      const query = 'SELECT * FROM shopping_carts WHERE id = $1';
      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ShoppingCartNotFoundError(id);
      }

      const cart = this.mapRowToShoppingCart(result.rows[0]);
      logger.info(`Retrieved shopping cart ${cart.id}`);
      return cart;
    } catch (error) {
      logger.error('Error reading shopping cart', { error, id });
      throw error;
    }
  }

  async update(id: string, items: ShoppingCartItem[]): Promise<ShoppingCart> {
    logger.info(`Updating shopping cart ${id}`);

    try {
      // Validate items
      this.validateCartItems(items);

      const query = `;`
        UPDATE shopping_carts
        SET items = $1, updated_at = $2
        WHERE id = $3
        RETURNING *`;`

      const values = [JSON.stringify(items), new Date(), id];
      const result: QueryResult = await this.db.query(query, values);

      if (result.rowCount === 0) {
        throw new ShoppingCartNotFoundError(id);
      }

      const cart = this.mapRowToShoppingCart(result.rows[0]);
      logger.info(`Updated shopping cart ${cart.id}`);
      return cart;
    } catch (error) {
      logger.error('Error updating shopping cart', { error, id });
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    logger.info(`Deleting shopping cart ${id}`);

    try {
      const query = 'DELETE FROM shopping_carts WHERE id = $1';
      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ShoppingCartNotFoundError(id);
      }

      logger.info(`Deleted shopping cart ${id}`);
    } catch (error) {
      logger.error('Error deleting shopping cart', { error, id });
      throw error;
    }
  }

  async addItemToCart(cartId: string, item: ShoppingCartItem): Promise<ShoppingCart> {
    logger.info(`Adding item to cart ${cartId}`, { item });

    try {
      // Validate item
      this.validateCartItem(item);

      // Get current cart
      const cart = await this.read(cartId);

      // Check if item already exists
      const existingItemIndex = cart.items.findIndex(i => i.productId === item.productId);

      if (existingItemIndex >= 0) {
        // Update quantity
        cart.items[existingItemIndex].quantity += item.quantity;
      } else {
        // Add new item
        cart.items.push(item);
      }

      // Update cart
      return await this.update(cartId, cart.items);
    } catch (error) {
      logger.error('Error adding item to cart', { error, cartId, item });
      throw error;
    }
  }

  async checkoutCart(cartId: string): Promise<ShoppingCart> {
    logger.info(`Checking out cart ${cartId}`);

    try {
      // Get current cart
      const cart = await this.read(cartId);

      // Validate cart has items
      if (cart.items.length === 0) {
        throw new BusinessError('Cannot checkout empty cart');
      }

      // In a real implementation, this would:
      // 1. Create an order
      // 2. Process payment
      // 3. Update inventory
      // 4. Clear the cart

      // For now, we'll just clear the cart'
      const updatedCart = await this.update(cartId, []);

      logger.info(`Checked out cart ${cartId}`);
      return updatedCart;
    } catch (error) {
      logger.error('Error checking out cart', { error, cartId });
      throw error;
    }
  }

  private mapRowToShoppingCart(row: any): ShoppingCart {
    return {
      id: row.id,
      userId: row.user_id,
      items: row.items ? JSON.parse(row.items) : [],
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private validateCartItems(items: ShoppingCartItem[]): void {
    if (!Array.isArray(items)) {
      throw new InvalidCartItemError('Cart items must be an array');
    }

    items.forEach(item => this.validateCartItem(item));
  }

  private validateCartItem(item: ShoppingCartItem): void {
    if (!item.productId || typeof item.productId !== 'string') {
      throw new InvalidCartItemError('Product ID is required and must be a string');
    }

    if (!Number.isInteger(item.quantity) || item.quantity <= 0) {
      throw new InvalidCartItemError('Quantity must be a positive integer');
    }

    if (typeof item.price !== 'number' || item.price < 0) {
      throw new InvalidCartItemError('Price must be a non-negative number');
    }
  }
}

export {
  ShoppingCartModel,
  ShoppingCart,
  ShoppingCartItem,
  BusinessError,
  ShoppingCartNotFoundError,
  InvalidCartItemError
};
}}}