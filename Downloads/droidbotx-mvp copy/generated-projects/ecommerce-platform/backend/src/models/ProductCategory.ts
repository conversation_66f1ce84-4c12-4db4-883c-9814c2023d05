```typescript
/**
 * ProductCategory entity interface
 * Categories for organizing products in the catalog
 */
export interface ProductCategory {
  /**
   * Unique identifier for the product category
   * @type {string}
   * @required
   * @format UUID
   */
  id: string;

  /**
   * Name of the product category
   * @type {string}
   * @required
   * @maxLength 100
   * @example "Electronics"
   */
  name: string;

  /**
   * Description of the product category
   * @type {string}
   * @required
   * @maxLength 500
   * @example "Electronic devices and accessories"
   */
  description: string;

  /**
   * Parent category identifier for hierarchical organization
   * @type {string}
   * @required
   * @format UUID
   * @example "550e8400-e29b-41d4-a716-************"
   */
  parentId: string;

  /**
   * Status indicating if the category is active
   * @type {boolean}
   * @required
   * @example true
   */
  isActive: boolean;
}
```