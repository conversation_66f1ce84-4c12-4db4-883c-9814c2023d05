




import { Pool, QueryResult } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface ProductCategory {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  slug: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProductCategoryInput {
  name: string;
  description?: string;
  parentId?: string;
  slug: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateProductCategoryInput {
  id: string;
  name?: string;
  description?: string;
  parentId?: string | null;
  slug?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface ProductCategoryTreeNode {
  id: string;
  name: string;
  description: string;
  slug: string;
  isActive: boolean;
  sortOrder: number;
  children: ProductCategoryTreeNode[];
}

export class ProductCategoryModel {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async create(input: CreateProductCategoryInput): Promise<ProductCategory> {
    this.logger.info('Creating new product category', { name: input.name });

    // Validate input
    this.validateCreateInput(input);

    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Check if slug already exists
      const slugExists = await this.checkSlugExists(input.slug, client);
      if (slugExists) {
        throw new ValidationError(`Category with slug '${input.slug}' already exists`);
      }

      // If parentId is provided, verify it exists
      if (input.parentId) {
        const parentExists = await this.checkCategoryExists(input.parentId, client);
        if (!parentExists) {
          throw new ValidationError(`Parent category with id '${input.parentId}' does not exist`);
        }
      }

      const query = `;`
        INSERT INTO product_categories (name, description, parent_id, slug, is_active, sort_order)
        VALUES ($1, $2, $3, $4, $5, $6);
        RETURNING *
      `;`

      const values = [;
        input.name,
        input.description || '',
        input.parentId || null,
        input.slug,
        input.isActive !== undefined ? input.isActive : true,
        input.sortOrder || 0
      ];

      const result: QueryResult<ProductCategory> = await client.query(query, values);
      const category = result.rows[0];

      await client.query('COMMIT');

      this.logger.info('Product category created successfully', { id: category.id });
      return category;
    } catch (error) {
      await client.query('ROLLBACK');

      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to create product category', { error: (error as Error).message });
      throw new DatabaseError('Failed to create product category', error as Error);
    } finally {
      client.release();
    }
  }

  async findById(id: string): Promise<ProductCategory | null> {
    this.logger.info('Fetching product category by id', { id });

    try {
      const query = 'SELECT * FROM product_categories WHERE id = $1';
      const result: QueryResult<ProductCategory> = await this.pool.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Product category not found', { id });
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error('Failed to fetch product category by id', { id, error: (error as Error).message });
      throw new DatabaseError('Failed to fetch product category', error as Error);
    }
  }

  async update(input: UpdateProductCategoryInput): Promise<ProductCategory> {
    this.logger.info('Updating product category', { id: input.id });

    // Validate input
    this.validateUpdateInput(input);

    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Check if category exists
      const existingCategory = await this.findById(input.id);
      if (!existingCategory) {
        throw new ValidationError(`Category with id '${input.id}' does not exist`);
      }

      // If slug is being updated, check it's not already taken'
      if (input.slug && input.slug !== existingCategory.slug) {
        const slugExists = await this.checkSlugExists(input.slug, client);
        if (slugExists) {
          throw new ValidationError(`Category with slug '${input.slug}' already exists`);
        }
      }

      // If parentId is being updated, verify it exists and is not self-reference
      if (input.parentId !== undefined) {
        if (input.parentId === input.id) {
          throw new ValidationError('Category cannot be its own parent');
        }

        if (input.parentId !== null) {
          const parentExists = await this.checkCategoryExists(input.parentId, client);
          if (!parentExists) {
            throw new ValidationError(`Parent category with id '${input.parentId}' does not exist`);
          }

          // Check for circular references
          await this.checkCircularReference(input.id, input.parentId, client);
        }
      }

      const query = `;`
        UPDATE product_categories
        SET name = COALESCE($1, name),
            description = COALESCE($2, description),
            parent_id = COALESCE($3, parent_id),
            slug = COALESCE($4, slug),
            is_active = COALESCE($5, is_active),
            sort_order = COALESCE($6, sort_order),
            updated_at = NOW()
        WHERE id = $7
        RETURNING *
      `;`

      const values = [;
        input.name,
        input.description,
        input.parentId,
        input.slug,
        input.isActive,
        input.sortOrder,
        input.id
      ];

      const result: QueryResult<ProductCategory> = await client.query(query, values);
      const updatedCategory = result.rows[0];

      await client.query('COMMIT');

      this.logger.info('Product category updated successfully', { id: input.id });
      return updatedCategory;
    } catch (error) {
      await client.query('ROLLBACK');

      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update product category', { id: input.id, error: (error as Error).message });
      throw new DatabaseError('Failed to update product category', error as Error);
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting product category', { id });

    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Check if category has children
      const hasChildren = await this.checkHasChildren(id, client);
      if (hasChildren) {
        throw new ValidationError('Cannot delete category with child categories');
      }

      // Delete the category
      const query = 'DELETE FROM product_categories WHERE id = $1';
      const result = await client.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ValidationError(`Category with id '${id}' does not exist`);
      }

      await client.query('COMMIT');

      this.logger.info('Product category deleted successfully', { id });
    } catch (error) {
      await client.query('ROLLBACK');

      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to delete product category', { id, error: (error as Error).message });
      throw new DatabaseError('Failed to delete product category', error as Error);
    } finally {
      client.release();
    }
  }

  async getCategoryTree(): Promise<ProductCategoryTreeNode[]> {
    this.logger.info('Fetching product category tree');

    try {
      const query = `;`
        SELECT id, name, description, parent_id, slug, is_active, sort_order
        FROM product_categories
        ORDER BY parent_id NULLS FIRST, sort_order ASC
      `;`

      const result = await this.pool.query(query);
      const categories = result.rows;

      // Build tree structure
      const categoryMap: Record<string, ProductCategoryTreeNode> = {};
      const roots: ProductCategoryTreeNode[] = [];

      // Initialize all nodes
      categories.forEach((category: any) => {
        categoryMap[category.id] = {
          id: category.id,
          name: category.name,
          description: category.description,
          slug: category.slug,
          isActive: category.is_active,
          sortOrder: category.sort_order,
          children: []
        };
      });

      // Build parent-child relationships
      categories.forEach((category: any) => {
        if (category.parent_id === null) {
          roots.push(categoryMap[category.id]);
        } else {
          const parent = categoryMap[category.parent_id];
          if (parent) {
            parent.children.push(categoryMap[category.id]);
          }
        }
      });

      this.logger.info('Product category tree fetched successfully');
      return roots;
    } catch (error) {
      this.logger.error('Failed to fetch product category tree', { error: (error as Error).message });
      throw new DatabaseError('Failed to fetch product category tree', error as Error);
    }
  }

  private validateCreateInput(input: CreateProductCategoryInput): void {
    if (!input.name || input.name.trim().length === 0) {
      throw new ValidationError('Category name is required');
    }

    if (input.name.trim().length > 100) {
      throw new ValidationError('Category name must be less than 100 characters');
    }

    if (!input.slug || input.slug.trim().length === 0) {
      throw new ValidationError('Category slug is required');
    }

    if (input.slug.trim().length > 100) {
      throw new ValidationError('Category slug must be less than 100 characters');
    }

    if (input.description && input.description.length > 500) {
      throw new ValidationError('Category description must be less than 500 characters');
    }
  }

  private validateUpdateInput(input: UpdateProductCategoryInput): void {
    if (!input.id) {
      throw new ValidationError('Category id is required');
    }

    if (input.name && input.name.trim().length === 0) {
      throw new ValidationError('Category name cannot be empty');
    }

    if (input.name && input.name.trim().length > 100) {
      throw new ValidationError('Category name must be less than 100 characters');
    }

    if (input.slug && input.slug.trim().length === 0) {
      throw new ValidationError('Category slug cannot be empty');
    }

    if (input.slug && input.slug.trim().length > 100) {
      throw new ValidationError('Category slug must be less than 100 characters');
    }

    if (input.description && input.description.length > 500) {
      throw new ValidationError('Category description must be less than 500 characters');
    }
  }

  private async checkSlugExists(slug: string, client: any): Promise<boolean> {
    const query = 'SELECT 1 FROM product_categories WHERE slug = $1';
    const result = await client.query(query, [slug]);
    return result.rowCount > 0;
  }

  private async checkCategoryExists(id: string, client: any): Promise<boolean> {
    const query = 'SELECT 1 FROM product_categories WHERE id = $1';
    const result = await client.query(query, [id]);
    return result.rowCount > 0;
  }

  private async checkHasChildren(id: string, client: any): Promise<boolean> {
    const query = 'SELECT 1 FROM product_categories WHERE parent_id = $1';
    const result = await client.query(query, [id]);
    return result.rowCount > 0;
  }

  private async checkCircularReference(categoryId: string, newParentId: string, client: any): Promise<void> {
    let currentParentId = newParentId;

    while (currentParentId) {
      if (currentParentId === categoryId) {
        throw new ValidationError('Circular reference detected in category hierarchy');
      }

      const query = 'SELECT parent_id FROM product_categories WHERE id = $1';
      const result = await client.query(query, [currentParentId]);

      if (result.rowCount === 0) {
        break;
      }

      currentParentId = result.rows[0].parent_id;
    }
  }
}
}}}