



// Product entity interface
import { Pool, QueryResult } from 'pg';
import { BusinessError, DatabaseError, ValidationError } from '../utils/Exceptions.js';
import { Logger } from '../utils/Logger.js';

export interface Product {
  id?: number;
  name: string;
  description: string;
  price: number;
  category: string;
  stockQuantity: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Product creation interface (without auto-generated fields)
export interface CreateProductInput {
  name: string;
  description: string;
  price: number;
  category: string;
  stockQuantity: number;
}

// Product update interface (all fields optional except id)
export interface UpdateProductInput {
  id: number;
  name?: string;
  description?: string;
  price?: number;
  category?: string;
  stockQuantity?: number;
}

export class ProductModel {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  /**
   * Create a new product
   */
  async create(productData: CreateProductInput): Promise<Product> {
    this.logger.info('Creating new product', { productName: productData.name });

    // Validate input
    this.validateProductData(productData);

    const client = await this.pool.connect();
    try {
      const query = `
        INSERT INTO products (name, description, price, category, stock_quantity)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id, name, description, price, category, stock_quantity, created_at, updated_at
      `;

      const values = [
        productData.name,
        productData.description,
        productData.price,
        productData.category,
        productData.stockQuantity
      ];

      const result: QueryResult = await client.query(query, values);
      const product: Product = result.rows[0];

      this.logger.info('Product created successfully', { productId: product.id });
      return product;
    } catch (error) {
      this.logger.error('Error creating product', { error: error.message, productData });
      throw new DatabaseError('Failed to create product');
    } finally {
      client.release();
    }
  }

  /**
   * Retrieve a product by ID
   */
  async read(id: number): Promise<Product | null> {
    this.logger.info('Retrieving product', { productId: id });

    if (!id || id <= 0) {
      throw new ValidationError('Invalid product ID');
    }

    const client = await this.pool.connect();
    try {
      const query = `
        SELECT id, name, description, price, category, stock_quantity, created_at, updated_at
        FROM products
        WHERE id = $1
      `;

      const result: QueryResult = await client.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Product not found', { productId: id });
        return null;
      }

      const product: Product = result.rows[0];
      this.logger.info('Product retrieved successfully', { productId: product.id });
      return product;
    } catch (error) {
      this.logger.error('Error retrieving product', { error: error.message, productId: id });
      throw new DatabaseError('Failed to retrieve product');
    } finally {
      client.release();
    }
  }

  /**
   * Update an existing product
   */
  async update(productData: UpdateProductInput): Promise<Product> {
    this.logger.info('Updating product', { productId: productData.id });

    if (!productData.id || productData.id <= 0) {
      throw new ValidationError('Invalid product ID');
    }

    const client = await this.pool.connect();
    try {
      // First, check if product exists
      const existingProduct = await this.read(productData.id);
      if (!existingProduct) {
        throw new BusinessError('Product not found');
      }

      // Build dynamic update query based on provided fields
      const fields: string[] = [];
      const values: any[] = [];
      let paramCount = 1;

      if (productData.name !== undefined) {
        fields.push(`name = $${paramCount++}`);
        values.push(productData.name);
      }

      if (productData.description !== undefined) {
        fields.push(`description = $${paramCount++}`);
        values.push(productData.description);
      }

      if (productData.price !== undefined) {
        // Validate price if provided
        if (productData.price < 0) {
          throw new ValidationError('Price cannot be negative');
        }
        fields.push(`price = $${paramCount++}`);
        values.push(productData.price);
      }

      if (productData.category !== undefined) {
        fields.push(`category = $${paramCount++}`);
        values.push(productData.category);
      }

      if (productData.stockQuantity !== undefined) {
        // Validate stock quantity if provided
        if (productData.stockQuantity < 0) {
          throw new ValidationError('Stock quantity cannot be negative');
        }
        fields.push(`stock_quantity = $${paramCount++}`);
        values.push(productData.stockQuantity);
      }

      // Always update the updated_at timestamp
      fields.push(`updated_at = NOW()`);

      if (fields.length === 1) { // Only updated_at would be updated
        this.logger.info('No product fields to update', { productId: productData.id });
        return existingProduct;
      }

      const query = `
        UPDATE products
        SET ${fields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING id, name, description, price, category, stock_quantity, created_at, updated_at
      `;

      values.push(productData.id);

      const result: QueryResult = await client.query(query, values);
      const updatedProduct: Product = result.rows[0];

      this.logger.info('Product updated successfully', { productId: updatedProduct.id });
      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        throw error;
      }
      this.logger.error('Error updating product', { error: error.message, productData });
      throw new DatabaseError('Failed to update product');
    } finally {
      client.release();
    }
  }

  /**
   * Delete a product by ID
   */
  async delete(id: number): Promise<boolean> {
    this.logger.info('Deleting product', { productId: id });

    if (!id || id <= 0) {
      throw new ValidationError('Invalid product ID');
    }

    const client = await this.pool.connect();
    try {
      const query = 'DELETE FROM products WHERE id = $1 RETURNING id';
      const result: QueryResult = await client.query(query, [id]);

      if (result.rowCount === 0) {
        this.logger.info('Product not found for deletion', { productId: id });
        return false;
      }

      this.logger.info('Product deleted successfully', { productId: id });
      return true;
    } catch (error) {
      this.logger.error('Error deleting product', { error: error.message, productId: id });
      throw new DatabaseError('Failed to delete product');
    } finally {
      client.release();
    }
  }

  /**
   * Search products with optional filters
   */
  async searchProducts(
    filters?: {
      category?: string;
      minPrice?: number;
      maxPrice?: number;
      name?: string;
    },
    limit: number = 20,
    offset: number = 0
  ): Promise<Product[]> {
    this.logger.info('Searching products', { filters, limit, offset });

    if (limit < 0 || offset < 0) {
      throw new ValidationError('Limit and offset must be non-negative');
    }

    const client = await this.pool.connect();
    try {
      let query = `
        SELECT id, name, description, price, category, stock_quantity, created_at, updated_at
        FROM products
        WHERE 1=1
      `;

      const values: any[] = [];
      let paramCount = 1;

      if (filters?.category) {
        query += ` AND category = $${paramCount++}`;
        values.push(filters.category);
      }

      if (filters?.minPrice !== undefined) {
        query += ` AND price >= $${paramCount++}`;
        values.push(filters.minPrice);
      }

      if (filters?.maxPrice !== undefined) {
        query += ` AND price <= $${paramCount++}`;
        values.push(filters.maxPrice);
      }

      if (filters?.name) {
        query += ` AND name ILIKE $${paramCount++}`;
        values.push(`%${filters.name}%`);
      }

      query += ` ORDER BY created_at DESC LIMIT $${paramCount++} OFFSET $${paramCount}`;
      values.push(limit, offset);

      const result: QueryResult = await client.query(query, values);
      const products: Product[] = result.rows;

      this.logger.info('Products search completed', { resultCount: products.length });
      return products;
    } catch (error) {
      this.logger.error('Error searching products', { error: error.message, filters });
      throw new DatabaseError('Failed to search products');
    } finally {
      client.release();
    }
  }

  /**
   * Validate product data according to business rules
   */
  private validateProductData(productData: CreateProductInput | UpdateProductInput): void {
    if ('name' in productData && (!productData.name || productData.name.trim().length === 0)) {
      throw new ValidationError('Product name is required');
    }

    if ('description' in productData && (!productData.description || productData.description.trim().length === 0)) {
      throw new ValidationError('Product description is required');
    }

    if ('price' in productData && (productData.price === undefined || productData.price < 0)) {
      throw new ValidationError('Product price must be a non-negative number');
    }

    if ('category' in productData && (!productData.category || productData.category.trim().length === 0)) {
      throw new ValidationError('Product category is required');
    }

    if ('stockQuantity' in productData && (productData.stockQuantity === undefined || productData.stockQuantity < 0)) {
      throw new ValidationError('Stock quantity must be a non-negative number');
    }
  }
}