




import { Pool, QueryResult } from 'pg';
import { DatabaseError } from '../exceptions/DatabaseError.js';
import { ValidationError } from '../exceptions/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  inventory: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProductInput {
  name: string;
  description: string;
  price: number;
  category: string;
  inventory: number;
}

export interface UpdateProductInput {
  id: string;
  name?: string;
  description?: string;
  price?: number;
  category?: string;
  inventory?: number;
}

export interface SearchProductsInput {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  searchTerm?: string;
  limit?: number;
  offset?: number;
}

export class ProductModel {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async create(input: CreateProductInput): Promise<Product> {
    this.logger.info('Creating new product', { input });

    // Validate input
    this.validateCreateInput(input);

    const client = await this.pool.connect();

    try {
      const query = `
        INSERT INTO products (name, description, price, category, inventory)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id, name, description, price, category, inventory, created_at, updated_at
      `;

      const values = [
        input.name,
        input.description,
        input.price,
        input.category,
        input.inventory
      ];

      const result: QueryResult = await client.query(query, values);
      const product = this.mapRowToProduct(result.rows[0]);

      this.logger.info('Product created successfully', { productId: product.id });
      return product;
    } catch (error) {
      this.logger.error('Failed to create product', { error, input });
      throw new DatabaseError('Failed to create product', error);
    } finally {
      client.release();
    }
  }

  async read(id: string): Promise<Product | null> {
    this.logger.info('Reading product', { productId: id });

    const client = await this.pool.connect();

    try {
      const query = `
        SELECT id, name, description, price, category, inventory, created_at, updated_at
        FROM products
        WHERE id = $1
      `;

      const result: QueryResult = await client.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Product not found', { productId: id });
        return null;
      }

      const product = this.mapRowToProduct(result.rows[0]);
      this.logger.info('Product retrieved successfully', { productId: product.id });
      return product;
    } catch (error) {
      this.logger.error('Failed to read product', { error, productId: id });
      throw new DatabaseError('Failed to read product', error);
    } finally {
      client.release();
    }
  }

  async update(input: UpdateProductInput): Promise<Product> {
    this.logger.info('Updating product', { input });

    // Validate input
    if (!input.id) {
      throw new ValidationError('Product ID is required for update');
    }

    const client = await this.pool.connect();

    try {
      // Build dynamic update query
      const fields = [];
      const values = [];
      let index = 1;

      if (input.name !== undefined) {
        fields.push(`name = $${index++}`);
        values.push(input.name);
      }

      if (input.description !== undefined) {
        fields.push(`description = $${index++}`);
        values.push(input.description);
      }

      if (input.price !== undefined) {
        this.validatePrice(input.price);
        fields.push(`price = $${index++}`);
        values.push(input.price);
      }

      if (input.category !== undefined) {
        fields.push(`category = $${index++}`);
        values.push(input.category);
      }

      if (input.inventory !== undefined) {
        this.validateInventory(input.inventory);
        fields.push(`inventory = $${index++}`);
        values.push(input.inventory);
      }

      if (fields.length === 0) {
        throw new ValidationError('No fields to update');
      }

      fields.push(`updated_at = NOW()`);
      values.push(input.id);

      const query = `
        UPDATE products
        SET ${fields.join(', ')}
        WHERE id = $${index}
        RETURNING id, name, description, price, category, inventory, created_at, updated_at
      `;

      const result: QueryResult = await client.query(query, values);

      if (result.rows.length === 0) {
        throw new ValidationError(`Product with ID ${input.id} not found`);
      }

      const product = this.mapRowToProduct(result.rows[0]);
      this.logger.info('Product updated successfully', { productId: product.id });
      return product;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update product', { error, input });
      throw new DatabaseError('Failed to update product', error);
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting product', { productId: id });

    const client = await this.pool.connect();

    try {
      const query = 'DELETE FROM products WHERE id = $1';
      const result: QueryResult = await client.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ValidationError(`Product with ID ${id} not found`);
      }

      this.logger.info('Product deleted successfully', { productId: id });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to delete product', { error, productId: id });
      throw new DatabaseError('Failed to delete product', error);
    } finally {
      client.release();
    }
  }

  async searchProducts(input: SearchProductsInput): Promise<Product[]> {
    this.logger.info('Searching products', { input });

    const client = await this.pool.connect();

    try {
      // Build dynamic search query
      const conditions = [];
      const values = [];
      let index = 1;

      if (input.category) {
        conditions.push(`category = $${index++}`);
        values.push(input.category);
      }

      if (input.minPrice !== undefined) {
        conditions.push(`price >= $${index++}`);
        values.push(input.minPrice);
      }

      if (input.maxPrice !== undefined) {
        conditions.push(`price <= $${index++}`);
        values.push(input.maxPrice);
      }

      if (input.searchTerm) {
        conditions.push(`(name ILIKE $${index} OR description ILIKE $${index})`);
        values.push(`%${input.searchTerm}%`);
        index++;
      }

      let query = `
        SELECT id, name, description, price, category, inventory, created_at, updated_at
        FROM products
      `;

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      query += ' ORDER BY created_at DESC';

      if (input.limit !== undefined) {
        query += ` LIMIT $${index++}`;
        values.push(input.limit);
      }

      if (input.offset !== undefined) {
        query += ` OFFSET $${index}`;
        values.push(input.offset);
      }

      const result: QueryResult = await client.query(query, values);
      const products = result.rows.map(row => this.mapRowToProduct(row));

      this.logger.info('Products search completed', { count: products.length });
      return products;
    } catch (error) {
      this.logger.error('Failed to search products', { error, input });
      throw new DatabaseError('Failed to search products', error);
    } finally {
      client.release();
    }
  }

  async updateInventory(productId: string, newInventory: number): Promise<Product> {
    this.logger.info('Updating product inventory', { productId, newInventory });

    this.validateInventory(newInventory);

    const client = await this.pool.connect();

    try {
      const query = `
        UPDATE products
        SET inventory = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING id, name, description, price, category, inventory, created_at, updated_at
      `;

      const result: QueryResult = await client.query(query, [newInventory, productId]);

      if (result.rows.length === 0) {
        throw new ValidationError(`Product with ID ${productId} not found`);
      }

      const product = this.mapRowToProduct(result.rows[0]);
      this.logger.info('Product inventory updated successfully', { productId: product.id, newInventory });
      return product;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update product inventory', { error, productId, newInventory });
      throw new DatabaseError('Failed to update product inventory', error);
    } finally {
      client.release();
    }
  }

  private validateCreateInput(input: CreateProductInput): void {
    if (!input.name || input.name.trim().length === 0) {
      throw new ValidationError('Product name is required');
    }

    if (input.name.length > 255) {
      throw new ValidationError('Product name must be less than 255 characters');
    }

    if (input.description && input.description.length > 1000) {
      throw new ValidationError('Product description must be less than 1000 characters');
    }

    this.validatePrice(input.price);
    this.validateInventory(input.inventory);

    if (!input.category || input.category.trim().length === 0) {
      throw new ValidationError('Product category is required');
    }
  }

  private validatePrice(price: number): void {
    if (price === undefined || price === null) {
      throw new ValidationError('Product price is required');
    }

    if (price < 0) {
      throw new ValidationError('Product price cannot be negative');
    }

    if (price > 999999.99) {
      throw new ValidationError('Product price is too high');
    }
  }

  private validateInventory(inventory: number): void {
    if (inventory === undefined || inventory === null) {
      throw new ValidationError('Product inventory is required');
    }

    if (!Number.isInteger(inventory)) {
      throw new ValidationError('Product inventory must be an integer');
    }

    if (inventory < 0) {
      throw new ValidationError('Product inventory cannot be negative');
    }
  }

  private mapRowToProduct(row: any): Product {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      price: row.price,
      category: row.category,
      inventory: row.inventory,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}