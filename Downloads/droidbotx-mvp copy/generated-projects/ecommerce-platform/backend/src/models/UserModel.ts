







import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseError } from '../errors/DatabaseError.js';
import { UserNotFoundError } from '../errors/UserNotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Logger } from '../utils/Logger.js';
import bcrypt from 'bcrypt';

export interface User {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'customer' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserInput {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: 'customer' | 'admin';
}

export interface UpdateUserInput {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: 'customer' | 'admin';
}

export interface AuthenticateUserInput {
  email: string;
  password: string;
}

export class UserModel {
  private logger: Logger;

  constructor(private dbPool: Pool) {
    this.logger = new Logger('UserModel');
  }

  async create(input: CreateUserInput): Promise<User> {
    this.logger.info('Creating new user', { email: input.email });

    // Validate input
    this.validateCreateInput(input);

    // Check if user already exists
    const existingUser = await this.findByEmail(input.email);
    if (existingUser) {
      this.logger.warn('User already exists', { email: input.email });
      throw new ValidationError('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(input.password, 10);

    // Create user in database
    const client = await this.dbPool.connect();
    try {
      const query = `
        INSERT INTO users (id, email, password, first_name, last_name, role)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, email, first_name, last_name, role, created_at, updated_at
      `;

      const values = [
        uuidv4(),
        input.email,
        hashedPassword,
        input.firstName,
        input.lastName,
        input.role || 'customer'
      ];

      const result = await client.query(query, values);
      const user = result.rows[0];

      this.logger.info('User created successfully', { userId: user.id, email: user.email });

      return {
        id: user.id,
        email: user.email,
        password: hashedPassword,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      this.logger.error('Error creating user', { error: (error as Error).message });
      throw new DatabaseError('Failed to create user');
    } finally {
      client.release();
    }
  }

  async read(id: string): Promise<User> {
    this.logger.info('Fetching user by ID', { userId: id });

    const client = await this.dbPool.connect();
    try {
      const query = `
        SELECT id, email, password, first_name, last_name, role, created_at, updated_at
        FROM users
        WHERE id = $1
      `;

      const result = await client.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.warn('User not found', { userId: id });
        throw new UserNotFoundError(`User with ID ${id} not found`);
      }

      const user = result.rows[0];

      return {
        id: user.id,
        email: user.email,
        password: user.password,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      if (error instanceof UserNotFoundError) {
        throw error;
      }

      this.logger.error('Error fetching user', { error: (error as Error).message, userId: id });
      throw new DatabaseError('Failed to fetch user');
    } finally {
      client.release();
    }
  }

  async update(id: string, input: UpdateUserInput): Promise<User> {
    this.logger.info('Updating user', { userId: id });

    // Validate input
    this.validateUpdateInput(input);

    // Check if user exists
    await this.read(id);

    const client = await this.dbPool.connect();
    try {
      const fields = [];
      const values = [];
      let index = 1;

      if (input.email !== undefined) {
        fields.push(`email = $${index++}`);
        values.push(input.email);
      }

      if (input.firstName !== undefined) {
        fields.push(`first_name = $${index++}`);
        values.push(input.firstName);
      }

      if (input.lastName !== undefined) {
        fields.push(`last_name = $${index++}`);
        values.push(input.lastName);
      }

      if (input.role !== undefined) {
        fields.push(`role = $${index++}`);
        values.push(input.role);
      }

      if (fields.length === 0) {
        // No fields to update
        return await this.read(id);
      }

      values.push(id);
      const query = `
        UPDATE users
        SET ${fields.join(', ')}, updated_at = NOW()
        WHERE id = $${index}
        RETURNING id, email, password, first_name, last_name, role, created_at, updated_at
      `;

      const result = await client.query(query, values);
      const user = result.rows[0];

      this.logger.info('User updated successfully', { userId: user.id });

      return {
        id: user.id,
        email: user.email,
        password: user.password,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      this.logger.error('Error updating user', { error: (error as Error).message, userId: id });
      throw new DatabaseError('Failed to update user');
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting user', { userId: id });

    const client = await this.dbPool.connect();
    try {
      const query = 'DELETE FROM users WHERE id = $1 RETURNING id';
      const result = await client.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.warn('User not found for deletion', { userId: id });
        throw new UserNotFoundError(`User with ID ${id} not found`);
      }

      this.logger.info('User deleted successfully', { userId: id });
    } catch (error) {
      if (error instanceof UserNotFoundError) {
        throw error;
      }

      this.logger.error('Error deleting user', { error: (error as Error).message, userId: id });
      throw new DatabaseError('Failed to delete user');
    } finally {
      client.release();
    }
  }

  async authenticateUser(input: AuthenticateUserInput): Promise<User> {
    this.logger.info('Authenticating user', { email: input.email });

    // Validate input
    this.validateAuthenticateInput(input);

    // Find user by email
    const user = await this.findByEmail(input.email);
    if (!user) {
      this.logger.warn('Authentication failed - user not found', { email: input.email });
      throw new ValidationError('Invalid email or password');
    }

    // Check password
    const isValidPassword = await bcrypt.compare(input.password, user.password);
    if (!isValidPassword) {
      this.logger.warn('Authentication failed - invalid password', { email: input.email });
      throw new ValidationError('Invalid email or password');
    }

    this.logger.info('User authenticated successfully', { userId: user.id });

    return user;
  }

  async updateProfile(userId: string, input: UpdateUserInput): Promise<User> {
    this.logger.info('Updating user profile', { userId });

    // For profile updates, we only allow updating first name, last name, and email
    const profileUpdate: UpdateUserInput = {
      firstName: input.firstName,
      lastName: input.lastName,
      email: input.email
    };

    return await this.update(userId, profileUpdate);
  }

  private async findByEmail(email: string): Promise<User | null> {
    const client = await this.dbPool.connect();
    try {
      const query = `
        SELECT id, email, password, first_name, last_name, role, created_at, updated_at
        FROM users
        WHERE email = $1
      `;

      const result = await client.query(query, [email]);

      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];

      return {
        id: user.id,
        email: user.email,
        password: user.password,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } finally {
      client.release();
    }
  }

  private validateCreateInput(input: CreateUserInput): void {
    const errors: string[] = [];

    if (!input.email || !this.isValidEmail(input.email)) {
      errors.push('Valid email is required');
    }

    if (!input.password || input.password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    if (!input.firstName || input.firstName.trim().length === 0) {
      errors.push('First name is required');
    }

    if (!input.lastName || input.lastName.trim().length === 0) {
      errors.push('Last name is required');
    }

    if (input.role && !['customer', 'admin'].includes(input.role)) {
      errors.push('Role must be either "customer" or "admin"');
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  }

  private validateUpdateInput(input: UpdateUserInput): void {
    const errors: string[] = [];

    if (input.email && !this.isValidEmail(input.email)) {
      errors.push('Valid email is required');
    }

    if (input.firstName && input.firstName.trim().length === 0) {
      errors.push('First name cannot be empty');
    }

    if (input.lastName && input.lastName.trim().length === 0) {
      errors.push('Last name cannot be empty');
    }

    if (input.role && !['customer', 'admin'].includes(input.role)) {
      errors.push('Role must be either "customer" or "admin"');
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  }

  private validateAuthenticateInput(input: AuthenticateUserInput): void {
    const errors: string[] = [];

    if (!input.email || !this.isValidEmail(input.email)) {
      errors.push('Valid email is required');
    }

    if (!input.password) {
      errors.push('Password is required');
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}