






// UserModel.ts






// User entity interface
import { createHash, randomBytes } from 'crypto';
import { Pool, QueryResult } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { UserNotFoundError } from '../errors/UserNotFoundError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  passwordHash: string;
  salt: string;
  createdAt: Date;
  updatedAt: Date;
}

// User creation input interface
export interface CreateUserInput {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
}

// User update input interface
export interface UpdateUserInput {
  email?: string;
  firstName?: string;
  lastName?: string;
  password?: string;
}

// Cart item interface
export interface CartItem {
  productId: number;
  quantity: number;
  price: number;
}

export class UserModel {
  private logger: Logger;

  constructor(private dbPool: Pool) {
    this.logger = new Logger('UserModel');
  }

  /**
   * Creates a new user in the database
   */
  async create(userData: CreateUserInput): Promise<User> {
    this.logger.info('Creating new user', { email: userData.email });

    // Validate input
    this.validateUserInput(userData);

    // Check if user already exists
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ValidationError('User with this email already exists');
    }

    // Hash password
    const { salt, hash } = this.hashPassword(userData.password);

    try {
      const query = `;`
        INSERT INTO users (email, first_name, last_name, password_hash, salt)
        VALUES ($1, $2, $3, $4, $5);
        RETURNING id, email, first_name, last_name, password_hash, salt, created_at, updated_at
      `;`

      const values = [;
        userData.email,
        userData.firstName,
        userData.lastName,
        hash,
        salt
      ];

      const result: QueryResult<User> = await this.dbPool.query(query, values);

      if (result.rowCount === 0) {
        throw new DatabaseError('Failed to create user');
      }

      this.logger.info('User created successfully', { userId: result.rows[0].id });
      return result.rows[0];
    } catch (error) {
      this.logger.error('Error creating user', error);
      if (error instanceof ValidationError) throw error;
      throw new DatabaseError('Failed to create user');
    }
  }

  /**
   * Retrieves a user by ID
   */
  async read(userId: number): Promise<User> {
    this.logger.info('Fetching user by ID', { userId });

    try {
      const query = `;`
        SELECT id, email, first_name, last_name, password_hash, salt, created_at, updated_at
        FROM users
        WHERE id = $1
      `;`

      const result: QueryResult<User> = await this.dbPool.query(query, [userId]);

      if (result.rowCount === 0) {
        throw new UserNotFoundError(`User with ID ${userId} not found`);
      }

      this.logger.info('User fetched successfully', { userId });
      return result.rows[0];
    } catch (error) {
      this.logger.error('Error fetching user', error);
      if (error instanceof UserNotFoundError) throw error;
      throw new DatabaseError('Failed to fetch user');
    }
  }

  /**
   * Updates user information
   */
  async update(userId: number, updateData: UpdateUserInput): Promise<User> {
    this.logger.info('Updating user', { userId });

    // Validate input if provided
    if (updateData.email || updateData.firstName || updateData.lastName) {
      const tempUser: any = {};
      if (updateData.email) tempUser.email = updateData.email;
      if (updateData.firstName) tempUser.firstName = updateData.firstName;
      if (updateData.lastName) tempUser.lastName = updateData.lastName;
      this.validateUserInput(tempUser);
    }

    try {
      const fields = [];
      const values = [];
      let paramCount = 1;

      if (updateData.email) {
        fields.push(`email = $${paramCount}`);
        values.push(updateData.email);
        paramCount++;
      }

      if (updateData.firstName) {
        fields.push(`first_name = $${paramCount}`);
        values.push(updateData.firstName);
        paramCount++;
      }

      if (updateData.lastName) {
        fields.push(`last_name = $${paramCount}`);
        values.push(updateData.lastName);
        paramCount++;
      }

      if (updateData.password) {
        const { salt, hash } = this.hashPassword(updateData.password);
        fields.push(`password_hash = $${paramCount}`);
        values.push(hash);
        paramCount++;
        fields.push(`salt = $${paramCount}`);
        values.push(salt);
        paramCount++;
      }

      if (fields.length === 0) {
        throw new ValidationError('No valid fields provided for update');
      }

      values.push(userId);
      fields.push('updated_at = NOW()');

      const query = `;`
        UPDATE users
        SET ${fields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING id, email, first_name, last_name, password_hash, salt, created_at, updated_at
      `;`

      const result: QueryResult<User> = await this.dbPool.query(query, values);

      if (result.rowCount === 0) {
        throw new UserNotFoundError(`User with ID ${userId} not found`);
      }

      this.logger.info('User updated successfully', { userId });
      return result.rows[0];
    } catch (error) {
      this.logger.error('Error updating user', error);
      if (error instanceof ValidationError || error instanceof UserNotFoundError) throw error;
      throw new DatabaseError('Failed to update user');
    }
  }

  /**
   * Deletes a user by ID
   */
  async delete(userId: number): Promise<void> {
    this.logger.info('Deleting user', { userId });

    try {
      const query = 'DELETE FROM users WHERE id = $1';
      const result: QueryResult = await this.dbPool.query(query, [userId]);

      if (result.rowCount === 0) {
        throw new UserNotFoundError(`User with ID ${userId} not found`);
      }

      this.logger.info('User deleted successfully', { userId });
    } catch (error) {
      this.logger.error('Error deleting user', error);
      if (error instanceof UserNotFoundError) throw error;
      throw new DatabaseError('Failed to delete user');
    }
  }

  /**
   * Registers a new user (alias for create with additional business logic)
   */
  async registerUser(userData: CreateUserInput): Promise<User> {
    this.logger.info('Registering new user', { email: userData.email });

    // Additional registration-specific validation could go here
    if (!userData.password || userData.password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    return await this.create(userData);
  }

  /**
   * Gets user's shopping cart'
   */
  async getUserCart(userId: number): Promise<CartItem[]> {
    this.logger.info('Fetching user cart', { userId });

    try {
      // Verify user exists
      await this.read(userId);

      const query = `;`
        SELECT product_id, quantity, price
        FROM cart_items
        WHERE user_id = $1
      `;`

      const result: QueryResult<CartItem> = await this.dbPool.query(query, [userId]);

      this.logger.info('User cart fetched successfully', { userId, itemCount: result.rowCount });
      return result.rows;
    } catch (error) {
      this.logger.error('Error fetching user cart', error);
      if (error instanceof UserNotFoundError) throw error;
      throw new DatabaseError('Failed to fetch user cart');
    }
  }

  /**
   * Finds a user by email
   */
  private async findByEmail(email: string): Promise<User | null> {
    try {
      const query = `;`
        SELECT id, email, first_name, last_name, password_hash, salt, created_at, updated_at
        FROM users
        WHERE email = $1
      `;`

      const result: QueryResult<User> = await this.dbPool.query(query, [email]);
      return result.rowCount > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Error finding user by email', error);
      throw new DatabaseError('Database query failed');
    }
  }

  /**
   * Validates user input data
   */
  private validateUserInput(userData: Partial<CreateUserInput>): void {
    if (userData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        throw new ValidationError('Invalid email format');
      }
    }

    if (userData.firstName && userData.firstName.trim().length === 0) {
      throw new ValidationError('First name is required');
    }

    if (userData.lastName && userData.lastName.trim().length === 0) {
      throw new ValidationError('Last name is required');
    }
  }

  /**
   * Hashes a password with a salt
   */
  private hashPassword(password: string): { salt: string; hash: string } {
    const salt = randomBytes(16).toString('hex');
    const hash = createHash('sha256');
      .update(password + salt)
      .digest('hex');
    return { salt, hash };
  }
}
}