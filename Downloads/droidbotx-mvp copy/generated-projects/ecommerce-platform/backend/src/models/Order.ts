```typescript
/**
 * Order Entity Interface
 * Represents a completed purchase transaction in the e-commerce system
 */
export interface Order {
  /**
   * Unique identifier for the order
   * @format uuid
   */
  id: string;

  /**
   * Identifier of the user who placed the order
   * @format uuid
   */
  userId: string;

  /**
   * Human-readable order number
   * @example "ORD-2023-000123"
   */
  orderNumber: string;

  /**
   * Current status of the order
   * @enum {string} - Valid statuses: 'pending', 'confirmed', 'shipped', 'delivered', 'cancelled'
   */
  status: string;

  /**
   * Total amount of the order
   * @format decimal string (e.g., "99.99")
   */
  totalAmount: string;

  /**
   * Shipping address for the order
   * @maxLength 500
   */
  shippingAddress: string;

  /**
   * Payment method used for the order
   * @example "credit_card", "paypal", "bank_transfer"
   */
  paymentMethod: string;

  /**
   * Timestamp when the order was placed
   * @format ISO 8601 date-time
   */
  placedAt: string;

  /**
   * Timestamp when the order was shipped
   * @format ISO 8601 date-time
   */
  shippedAt: string;

  /**
   * Timestamp when the order was delivered
   * @format ISO 8601 date-time
   */
  deliveredAt: string;
}
```