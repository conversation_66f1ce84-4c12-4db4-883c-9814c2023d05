```typescript
/**
 * CartItem Entity
 * Represents items in a user's shopping cart
 */
export interface CartItem {
  /**
   * Unique identifier for the cart item
   * @type {string}
   * @required
   */
  id: string;

  /**
   * Identifier of the cart this item belongs to
   * @type {string}
   * @required
   */
  cartId: string;

  /**
   * Identifier of the product in the cart
   * @type {string}
   * @required
   */
  productId: string;

  /**
   * Quantity of the product in the cart
   * @type {number}
   * @required
   * @minimum 1
   * @maximum 999
   */
  quantity: number;

  /**
   * Timestamp when the item was added to the cart
   * @type {string}
   * @format ISO 8601 date-time
   * @required
   */
  addedAt: string;
}
```