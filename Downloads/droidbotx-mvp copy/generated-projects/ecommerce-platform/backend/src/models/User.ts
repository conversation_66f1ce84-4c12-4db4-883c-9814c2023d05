```typescript
/**
 * User Entity Interface
 * Represents a customer or admin user in the e-commerce system
 */
export interface User {
  /**
   * Unique identifier for the user
   * @type {string}
   * @required
   */
  id: string;

  /**
   * User's email address
   * Must be a valid email format
   * @type {string}
   * @required
   */
  email: string;

  /**
   * Hashed password for user authentication
   * @type {string}
   * @required
   */
  passwordHash: string;

  /**
   * User's first name
   * @type {string}
   * @required
   */
  firstName: string;

  /**
   * User's last name
   * @type {string}
   * @required
   */
  lastName: string;

  /**
   * User's role in the system (e.g., 'customer', 'admin', 'vendor')
   * @type {string}
   * @required
   */
  role: string;

  /**
   * Indicates if the user account is active
   * @type {boolean}
   * @required
   */
  isActive: boolean;

  /**
   * Timestamp when the user account was created
   * @type {string}
   * @required
   */
  createdAt: string;

  /**
   * Timestamp of the user's last login
   * @type {string}
   * @required
   */
  lastLoginAt: string;
}
```