```typescript
/**
 * User Entity Interface
 * Represents a customer using the e-commerce platform
 */
export interface User {
  /**
   * Unique identifier for the user
   * @type {string}
   * @required
   */
  id: string;

  /**
   * User's email address
   * Must be a valid email format
   * @type {string}
   * @required
   * @format email
   */
  email: string;

  /**
   * User's first name
   * @type {string}
   * @required
   * @minLength 1
   * @maxLength 50
   */
  firstName: string;

  /**
   * User's last name
   * @type {string}
   * @required
   * @minLength 1
   * @maxLength 50
   */
  lastName: string;

  /**
   * Timestamp when the user was created
   * @type {string}
   * @required
   * @format date-time
   */
  createdAt: string;

  /**
   * Timestamp when the user was last updated
   * @type {string}
   * @required
   * @format date-time
   */
  updatedAt: string;
}
```