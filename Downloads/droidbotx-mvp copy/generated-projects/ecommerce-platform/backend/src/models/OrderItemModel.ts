




import { Pool, QueryResult } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateOrderItemInput {
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
}

export interface UpdateOrderItemInput {
  quantity?: number;
  price?: number;
}

export class OrderItemModel {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  async create(input: CreateOrderItemInput): Promise<OrderItem> {
    this.logger.info('Creating order item', { orderId: input.orderId, productId: input.productId });

    // Validate input
    this.validateCreateInput(input);

    const client = await this.pool.connect();
    try {
      const query = `
        INSERT INTO order_items (order_id, product_id, quantity, price)
        VALUES ($1, $2, $3, $4)
        RETURNING id, order_id, product_id, quantity, price, created_at, updated_at
      `;

      const values = [
        input.orderId,
        input.productId,
        input.quantity,
        input.price
      ];

      const result: QueryResult = await client.query(query, values);
      const row = result.rows[0];

      this.logger.info('Order item created successfully', { id: row.id });

      return this.mapRowToOrderItem(row);
    } catch (error) {
      this.logger.error('Failed to create order item', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw new DatabaseError('Failed to create order item', error);
    } finally {
      client.release();
    }
  }

  async findById(id: string): Promise<OrderItem | null> {
    this.logger.info('Fetching order item by ID', { id });

    const client = await this.pool.connect();
    try {
      const query = `
        SELECT id, order_id, product_id, quantity, price, created_at, updated_at
        FROM order_items
        WHERE id = $1
      `;

      const result: QueryResult = await client.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Order item not found', { id });
        return null;
      }

      const row = result.rows[0];
      this.logger.info('Order item found', { id: row.id });

      return this.mapRowToOrderItem(row);
    } catch (error) {
      this.logger.error('Failed to fetch order item', { id, error: error instanceof Error ? error.message : 'Unknown error' });
      throw new DatabaseError('Failed to fetch order item', error);
    } finally {
      client.release();
    }
  }

  async findByOrderId(orderId: string): Promise<OrderItem[]> {
    this.logger.info('Fetching order items by order ID', { orderId });

    const client = await this.pool.connect();
    try {
      const query = `
        SELECT id, order_id, product_id, quantity, price, created_at, updated_at
        FROM order_items
        WHERE order_id = $1
        ORDER BY created_at ASC
      `;

      const result: QueryResult = await client.query(query, [orderId]);

      this.logger.info('Order items fetched', { orderId, count: result.rows.length });

      return result.rows.map(row => this.mapRowToOrderItem(row));
    } catch (error) {
      this.logger.error('Failed to fetch order items', { orderId, error: error instanceof Error ? error.message : 'Unknown error' });
      throw new DatabaseError('Failed to fetch order items', error);
    } finally {
      client.release();
    }
  }

  async update(id: string, input: UpdateOrderItemInput): Promise<OrderItem> {
    this.logger.info('Updating order item', { id });

    // Validate input
    this.validateUpdateInput(input);

    const client = await this.pool.connect();
    try {
      const fields = [];
      const values = [];
      let index = 1;

      if (input.quantity !== undefined) {
        fields.push(`quantity = $${index}`);
        values.push(input.quantity);
        index++;
      }

      if (input.price !== undefined) {
        fields.push(`price = $${index}`);
        values.push(input.price);
        index++;
      }

      if (fields.length === 0) {
        throw new ValidationError('No fields to update');
      }

      fields.push(`updated_at = NOW()`);
      values.push(id);

      const query = `
        UPDATE order_items
        SET ${fields.join(', ')}
        WHERE id = $${index}
        RETURNING id, order_id, product_id, quantity, price, created_at, updated_at
      `;

      const result: QueryResult = await client.query(query, values);

      if (result.rows.length === 0) {
        throw new DatabaseError('Order item not found');
      }

      const row = result.rows[0];
      this.logger.info('Order item updated successfully', { id: row.id });

      return this.mapRowToOrderItem(row);
    } catch (error) {
      this.logger.error('Failed to update order item', { id, error: error instanceof Error ? error.message : 'Unknown error' });
      throw error instanceof ValidationError ? error : new DatabaseError('Failed to update order item', error);
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting order item', { id });

    const client = await this.pool.connect();
    try {
      const query = 'DELETE FROM order_items WHERE id = $1';
      const result: QueryResult = await client.query(query, [id]);

      if (result.rowCount === 0) {
        throw new DatabaseError('Order item not found');
      }

      this.logger.info('Order item deleted successfully', { id });
    } catch (error) {
      this.logger.error('Failed to delete order item', { id, error: error instanceof Error ? error.message : 'Unknown error' });
      throw error instanceof DatabaseError ? error : new DatabaseError('Failed to delete order item', error);
    } finally {
      client.release();
    }
  }

  private validateCreateInput(input: CreateOrderItemInput): void {
    const errors: string[] = [];

    if (!input.orderId || input.orderId.trim() === '') {
      errors.push('Order ID is required');
    }

    if (!input.productId || input.productId.trim() === '') {
      errors.push('Product ID is required');
    }

    if (input.quantity <= 0) {
      errors.push('Quantity must be greater than zero');
    }

    if (input.price < 0) {
      errors.push('Price cannot be negative');
    }

    if (errors.length > 0) {
      throw new ValidationError('Invalid order item data', errors);
    }
  }

  private validateUpdateInput(input: UpdateOrderItemInput): void {
    const errors: string[] = [];

    if (input.quantity !== undefined && input.quantity <= 0) {
      errors.push('Quantity must be greater than zero');
    }

    if (input.price !== undefined && input.price < 0) {
      errors.push('Price cannot be negative');
    }

    if (errors.length > 0) {
      throw new ValidationError('Invalid update data', errors);
    }
  }

  private mapRowToOrderItem(row: any): OrderItem {
    return {
      id: row.id,
      orderId: row.order_id,
      productId: row.product_id,
      quantity: row.quantity,
      price: row.price,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}