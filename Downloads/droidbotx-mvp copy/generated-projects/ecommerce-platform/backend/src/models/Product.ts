```typescript
/**
 * Product Entity
 * Represents items available for purchase in the catalog
 */
export interface Product {
  /**
   * Unique identifier for the product
   * @example "prod_1234567890"
   */
  id: string;

  /**
   * Name of the product
   * @example "Wireless Bluetooth Headphones"
   * @constraints Required, Min length: 1, Max length: 255
   */
  name: string;

  /**
   * Detailed description of the product
   * @example "High-quality wireless headphones with noise cancellation"
   * @constraints Required, Min length: 1, Max length: 2000
   */
  description: string;

  /**
   * Price of the product in decimal format
   * @example "99.99"
   * @constraints Required, Must be a valid decimal number, Min: 0
   */
  price: string;

  /**
   * Category the product belongs to
   * @example "Electronics"
   * @constraints Required, Min length: 1, Max length: 100
   */
  category: string;

  /**
   * URL to the product image
   * @example "https://example.com/images/product.jpg"
   * @constraints Required, Must be a valid URL
   */
  imageUrl: string;

  /**
   * Available stock quantity
   * @example "50"
   * @constraints Required, Must be a non-negative integer
   */
  stockQuantity: string;

  /**
   * Creation timestamp in ISO 8601 format
   * @example "2023-01-15T09:30:00Z"
   * @constraints Required, Must be a valid ISO 8601 date string
   */
  createdAt: string;

  /**
   * Last update timestamp in ISO 8601 format
   * @example "2023-01-20T14:45:00Z"
   * @constraints Required, Must be a valid ISO 8601 date string
   */
  updatedAt: string;
}
```