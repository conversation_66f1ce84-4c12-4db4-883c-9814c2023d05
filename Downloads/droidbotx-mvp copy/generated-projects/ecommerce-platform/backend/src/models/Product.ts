```typescript
/**
 * Product Entity Interface
 * Represents items available for purchase in the catalog
 */
export interface Product {
  /**
   * Unique identifier for the product
   * @type {string}
   * @required
   */
  id: string;

  /**
   * Name of the product
   * @type {string}
   * @required
   * @maxLength 255
   */
  name: string;

  /**
   * Detailed description of the product
   * @type {string}
   * @required
   * @maxLength 1000
   */
  description: string;

  /**
   * Price of the product in decimal format
   * @type {string}
   * @required
   * @pattern /^\d+(\.\d{1,2})?$/
   * @example "29.99"
   */
  price: string;

  /**
   * Reference to the category this product belongs to
   * @type {string}
   * @required
   * @format uuid
   */
  categoryId: string;

  /**
   * Available stock quantity
   * @type {string}
   * @required
   * @pattern /^\d+$/
   * @minimum 0
   */
  stockQuantity: string;

  /**
   * Product availability status
   * @type {string}
   * @required
   * @enum ['true', 'false']
   */
  isActive: string;

  /**
   * Creation timestamp
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   * @example "2023-01-01T00:00:00Z"
   */
  createdAt: string;

  /**
   * Last update timestamp
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   * @example "2023-01-01T00:00:00Z"
   */
  updatedAt: string;
}
```