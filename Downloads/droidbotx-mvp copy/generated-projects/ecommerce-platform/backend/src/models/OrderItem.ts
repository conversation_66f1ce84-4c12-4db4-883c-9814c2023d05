```typescript
/**
 * OrderItem Entity
 * Represents an individual product within an order in an e-commerce application
 */
export interface OrderItem {
  /**
   * Unique identifier for the order item
   * @type {string}
   * @required
   */
  id: string;

  /**
   * Reference to the order this item belongs to
   * @type {string}
   * @required
   */
  orderId: string;

  /**
   * Reference to the product being ordered
   * @type {string}
   * @required
   */
  productId: string;

  /**
   * Quantity of the product ordered
   * @type {number}
   * @required
   * @minimum 1
   */
  quantity: number;

  /**
   * Price per unit of the product at the time of order
   * @type {number}
   * @required
   * @minimum 0
   * @format currency
   */
  unitPrice: number;

  /**
   * Total price for this order item (quantity * unitPrice)
   * @type {number}
   * @required
   * @minimum 0
   * @format currency
   */
  totalPrice: number;
}
```