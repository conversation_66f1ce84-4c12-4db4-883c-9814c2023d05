




import { Pool } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Logger } from '../utils/Logger.js';

export interface CartItem {
  id: string;
  cartId: string;
  productId: string;
  quantity: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCartItemInput {
  cartId: string;
  productId: string;
  quantity: number;
  price: number;
}

export interface UpdateCartItemInput {
  id: string;
  quantity?: number;
  price?: number;
}

export class CartItemModel {
  private logger: Logger;

  constructor(private db: Pool) {
    this.logger = new Logger('CartItemModel');
  }

  async create(input: CreateCartItemInput): Promise<CartItem> {
    this.logger.info('Creating cart item', { cartId: input.cartId, productId: input.productId });

    // Validate input
    this.validateCreateInput(input);

    try {
      const query = `
        INSERT INTO cart_items (cart_id, product_id, quantity, price)
        VALUES ($1, $2, $3, $4)
        RETURNING id, cart_id, product_id, quantity, price, created_at, updated_at
      `;

      const values = [
        input.cartId,
        input.productId,
        input.quantity,
        input.price
      ];

      const result = await this.db.query(query, values);
      const cartItem = result.rows[0];

      this.logger.info('Cart item created successfully', { id: cartItem.id });

      return this.mapToCartItem(cartItem);
    } catch (error) {
      this.logger.error('Failed to create cart item', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw new DatabaseError('Failed to create cart item');
    }
  }

  async read(id: string): Promise<CartItem | null> {
    this.logger.info('Reading cart item', { id });

    try {
      const query = `
        SELECT id, cart_id, product_id, quantity, price, created_at, updated_at
        FROM cart_items
        WHERE id = $1
      `;

      const result = await this.db.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Cart item not found', { id });
        return null;
      }

      this.logger.info('Cart item retrieved successfully', { id });
      return this.mapToCartItem(result.rows[0]);
    } catch (error) {
      this.logger.error('Failed to read cart item', { error: error instanceof Error ? error.message : 'Unknown error', id });
      throw new DatabaseError('Failed to read cart item');
    }
  }

  async update(input: UpdateCartItemInput): Promise<CartItem> {
    this.logger.info('Updating cart item', { id: input.id });

    // Validate input
    this.validateUpdateInput(input);

    try {
      const fields = [];
      const values = [];
      let index = 1;

      if (input.quantity !== undefined) {
        fields.push(`quantity = $${index}`);
        values.push(input.quantity);
        index++;
      }

      if (input.price !== undefined) {
        fields.push(`price = $${index}`);
        values.push(input.price);
        index++;
      }

      fields.push(`updated_at = NOW()`);
      values.push(input.id);

      const query = `
        UPDATE cart_items
        SET ${fields.join(', ')}
        WHERE id = $${index}
        RETURNING id, cart_id, product_id, quantity, price, created_at, updated_at
      `;

      const result = await this.db.query(query, values);

      if (result.rows.length === 0) {
        this.logger.warn('Cart item not found for update', { id: input.id });
        throw new ValidationError('Cart item not found');
      }

      this.logger.info('Cart item updated successfully', { id: input.id });
      return this.mapToCartItem(result.rows[0]);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update cart item', { error: error instanceof Error ? error.message : 'Unknown error', id: input.id });
      throw new DatabaseError('Failed to update cart item');
    }
  }

  async delete(id: string): Promise<void> {
    this.logger.info('Deleting cart item', { id });

    try {
      const query = 'DELETE FROM cart_items WHERE id = $1';
      const result = await this.db.query(query, [id]);

      if (result.rowCount === 0) {
        this.logger.warn('Cart item not found for deletion', { id });
        throw new ValidationError('Cart item not found');
      }

      this.logger.info('Cart item deleted successfully', { id });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to delete cart item', { error: error instanceof Error ? error.message : 'Unknown error', id });
      throw new DatabaseError('Failed to delete cart item');
    }
  }

  async updateItemQuantity(id: string, quantity: number): Promise<CartItem> {
    this.logger.info('Updating cart item quantity', { id, quantity });

    // Validate quantity
    if (quantity <= 0) {
      throw new ValidationError('Quantity must be greater than zero');
    }

    try {
      const query = `
        UPDATE cart_items
        SET quantity = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING id, cart_id, product_id, quantity, price, created_at, updated_at
      `;

      const result = await this.db.query(query, [quantity, id]);

      if (result.rows.length === 0) {
        this.logger.warn('Cart item not found for quantity update', { id });
        throw new ValidationError('Cart item not found');
      }

      this.logger.info('Cart item quantity updated successfully', { id, quantity });
      return this.mapToCartItem(result.rows[0]);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update cart item quantity', {
        error: error instanceof Error ? error.message : 'Unknown error',
        id,
        quantity
      });
      throw new DatabaseError('Failed to update cart item quantity');
    }
  }

  private validateCreateInput(input: CreateCartItemInput): void {
    if (!input.cartId) {
      throw new ValidationError('Cart ID is required');
    }

    if (!input.productId) {
      throw new ValidationError('Product ID is required');
    }

    if (input.quantity <= 0) {
      throw new ValidationError('Quantity must be greater than zero');
    }

    if (input.price < 0) {
      throw new ValidationError('Price cannot be negative');
    }
  }

  private validateUpdateInput(input: UpdateCartItemInput): void {
    if (!input.id) {
      throw new ValidationError('Cart item ID is required');
    }

    if (input.quantity !== undefined && input.quantity <= 0) {
      throw new ValidationError('Quantity must be greater than zero');
    }

    if (input.price !== undefined && input.price < 0) {
      throw new ValidationError('Price cannot be negative');
    }
  }

  private mapToCartItem(row: any): CartItem {
    return {
      id: row.id,
      cartId: row.cart_id,
      productId: row.product_id,
      quantity: row.quantity,
      price: row.price,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}