"use strict";
// Auto-generated standardized service
// Pattern: Express Route Service
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_js_1 = require("../middleware/validation.js");
const auth_js_1 = require("../middleware/auth.js");
const Logger_js_1 = require("../core/Logger.js");
const express_validator_1 = require("express-validator");
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_js_1 = __importDefault(require("../config/database.js"));
const router = (0, express_1.Router)();
const userRoutes = new userRoutes();
router.use(auth_js_1.authenticateToken);
router.use(validation_js_1.validateRequest);
const router = express.Router();
// Authentication middleware
const authenticate = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const token = authHeader.split(' ')[1];
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback_secret');
        req.user = decoded;
        next();
    }
    catch (error) {
        Logger_js_1.logger.error('Authentication error:', error);
        return res.status(401).json({ error: 'Invalid or expired token' });
    }
};
// Authorization middleware
const authorize = (...roles) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user || !roles.includes(user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        next();
    };
};
// Input validation middleware
const validate = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation failed',
            details: errors.array()
        });
    }
    next();
};
// GET /users - Get all users (admin only)
router.get('/', authenticate, authorize('admin'), async (req, res) => {
    try {
        const client = await database_js_1.default.connect();
        try {
            const result = await client.query();
            'SELECT id, email, first_name, last_name, role, created_at, updated_at FROM users ORDER BY id';
        }
        finally {
        }
    }
    finally {
    }
});
const users = result.rows.map(row => ({
    id: row.id,
    email: row.email,
    firstName: row.first_name,
    lastName: row.last_name,
    role: row.role,
    createdAt: row.created_at,
    updatedAt: row.updated_at
}));
res.json(users);
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Error fetching users:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
// GET /users/:id - Get user by ID
router.get('/:id', authenticate, (0, express_validator_1.param)('id').isInt({ min: 1 }).withMessage('Invalid user ID'), validate, async (req, res) => {
    try {
        const userId = parseInt(req.params.id, 10);
        const requestingUser = req.user;
        // Users can only view their own profile unless they're admin'
        if (requestingUser.userId !== userId && requestingUser.role !== 'admin') {
            return res.status(403).json({ error: 'Access denied' });
        }
        const client = await database_js_1.default.connect();
        try {
            const result = await client.query();
            'SELECT id, email, first_name, last_name, role, created_at, updated_at FROM users WHERE id = $1',
                [userId];
        }
        finally {
        }
    }
    finally {
    }
});
if (result.rows.length === 0) {
    return res.status(404).json({ error: 'User not found' });
}
const user = result.rows[0];
res.json({
    id: user.id,
    email: user.email,
    firstName: user.first_name,
    lastName: user.last_name,
    role: user.role,
    createdAt: user.created_at,
    updatedAt: user.updated_at
});
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Error fetching user:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
// POST /users - Create new user
router.post('/', (0, express_validator_1.body)('email').isEmail().normalizeEmail().withMessage('Valid email is required'), (0, express_validator_1.body)('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'), (0, express_validator_1.body)('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'), (0, express_validator_1.body)('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'), validate, async (req, res) => {
    try {
        const { email, firstName, lastName, password } = req.body;
        const client = await database_js_1.default.connect();
        try {
            // Check if user already exists
            const existingUser = await client.query();
            'SELECT id FROM users WHERE email = $1',
                [email];
        }
        finally {
        }
    }
    finally {
    }
});
if (existingUser.rows.length > 0) {
    return res.status(409).json({ error: 'User with this email already exists' });
}
// Hash password
const saltRounds = 10;
const hashedPassword = await bcrypt_1.default.hash(password, saltRounds);
// Create user
const result = await client.query();
`INSERT INTO users (email, first_name, last_name, password, role)`;
VALUES($1, $2, $3, $4, $5);
RETURNING;
id, email, first_name, last_name, role, created_at, updated_at `,`[email, firstName, lastName, hashedPassword, 'customer'];
;
const newUser = result.rows[0];
res.status(201).json({
    id: newUser.id,
    email: newUser.email,
    firstName: newUser.first_name,
    lastName: newUser.last_name,
    role: newUser.role,
    createdAt: newUser.created_at,
    updatedAt: newUser.updated_at
});
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Error creating user:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
// PUT /users/:id - Update user
router.put('/:id', authenticate, (0, express_validator_1.param)('id').isInt({ min: 1 }).withMessage('Invalid user ID'), (0, express_validator_1.body)('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required'), (0, express_validator_1.body)('firstName').optional().trim().isLength({ min: 1 }).withMessage('First name is required'), (0, express_validator_1.body)('lastName').optional().trim().isLength({ min: 1 }).withMessage('Last name is required'), validate, async (req, res) => {
    try {
        const userId = parseInt(req.params.id, 10);
        const requestingUser = req.user;
        const { email, firstName, lastName } = req.body;
        // Users can only update their own profile unless they're admin'
        if (requestingUser.userId !== userId && requestingUser.role !== 'admin') {
            return res.status(403).json({ error: 'Access denied' });
        }
        const client = await database_js_1.default.connect();
        try {
            // Check if user exists
            const existingUser = await client.query();
            'SELECT id, email FROM users WHERE id = $1',
                [userId];
        }
        finally {
        }
    }
    finally {
    }
});
if (existingUser.rows.length === 0) {
    return res.status(404).json({ error: 'User not found' });
}
// If email is being updated, check if it's already taken'
if (email && email !== existingUser.rows[0].email) {
    const emailCheck = await client.query();
    'SELECT id FROM users WHERE email = $1 AND id != $2',
        [email, userId];
    ;
    if (emailCheck.rows.length > 0) {
        return res.status(409).json({ error: 'Email is already in use' });
    }
}
// Update user
const result = await client.query();
`UPDATE users`;
SET;
email = COALESCE($1, email),
    first_name = COALESCE($2, first_name),
    last_name = COALESCE($3, last_name),
    updated_at = NOW();
WHERE;
id = $4;
RETURNING;
id, email, first_name, last_name, role, created_at, updated_at `,`[email, firstName, lastName, userId];
;
const updatedUser = result.rows[0];
res.json({
    id: updatedUser.id,
    email: updatedUser.email,
    firstName: updatedUser.first_name,
    lastName: updatedUser.last_name,
    role: updatedUser.role,
    createdAt: updatedUser.created_at,
    updatedAt: updatedUser.updated_at
});
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Error updating user:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
// DELETE /users/:id - Delete user
router.delete('/:id', authenticate, authorize('admin'), (0, express_validator_1.param)('id').isInt({ min: 1 }).withMessage('Invalid user ID'), validate, async (req, res) => {
    try {
        const userId = parseInt(req.params.id, 10);
        const requestingUser = req.user;
        // Prevent users from deleting themselves
        if (requestingUser.userId === userId) {
            return res.status(400).json({ error: 'Cannot delete your own account' });
        }
        const client = await database_js_1.default.connect();
        try {
            // Check if user exists
            const existingUser = await client.query();
            'SELECT id FROM users WHERE id = $1',
                [userId];
        }
        finally {
        }
    }
    finally {
    }
});
if (existingUser.rows.length === 0) {
    return res.status(404).json({ error: 'User not found' });
}
// Delete user
await client.query('DELETE FROM users WHERE id = $1', [userId]);
res.status(204).send();
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Error deleting user:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
// POST /users/login - User login
router.post('/login', (0, express_validator_1.body)('email').isEmail().normalizeEmail().withMessage('Valid email is required'), (0, express_validator_1.body)('password').exists().withMessage('Password is required'), validate, async (req, res) => {
    try {
        const { email, password } = req.body;
        const client = await database_js_1.default.connect();
        try {
            // Find user by email
            const result = await client.query();
            'SELECT id, email, first_name, last_name, password, role FROM users WHERE email = $1',
                [email];
        }
        finally {
        }
    }
    finally {
    }
});
if (result.rows.length === 0) {
    return res.status(401).json({ error: 'Invalid credentials' });
}
const user = result.rows[0];
// Check password
const isPasswordValid = await bcrypt_1.default.compare(password, user.password);
if (!isPasswordValid) {
    return res.status(401).json({ error: 'Invalid credentials' });
}
// Generate JWT token
const token = jsonwebtoken_1.default.sign();
{
    userId: user.id, role;
    user.role;
}
process.env.JWT_SECRET || 'fallback_secret',
    { expiresIn: '24h' };
;
res.json({
    token,
    user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role
    }
});
try { }
finally {
    client.release();
}
try { }
catch (error) {
    Logger_js_1.logger.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
}
;
exports.default = router;
const handleError = (error, req, res, next) => {
    Logger_js_1.logger.error('Route error', {
        route: req.path,
        method: req.method,
        error: error.message,
        stack: error.stack
    });
    if (error.name === 'ValidationError') {
        return res.status(400).json({ success: false, error: error.message });
    }
    if (error.name === 'UnauthorizedError') {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    res.status(500).json({ success: false, error: 'Internal server error' });
};
// Logging utilities
Logger_js_1.logger.info('Route accessed', {
    route: req.path,
    method: req.method,
    user: req.user?.id,
    timestamp: new Date().toISOString()
});
exports.default = userRoutes;
//# sourceMappingURL=userRoutes.js.map