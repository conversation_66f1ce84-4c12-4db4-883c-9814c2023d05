"use strict";
// Auto-generated standardized service
// Pattern: Express Route Service
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_js_1 = require("../middleware/validation.js");
const Logger_js_1 = require("../core/Logger.js");
const express_validator_1 = require("express-validator");
const pg_1 = require("pg");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const router = (0, express_1.Router)();
const productRoutes = new productRoutes();
router.use(authenticateToken);
router.use(validation_js_1.validateRequest);
// Initialize router
const router = express.Router();
// Database connection pool
const pool = new pg_1.Pool({
    connectionString: process.env.DATABASE_URL,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});
// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key';
// Authentication middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};
// Authorization middleware
const authorizeRole = (...roles) => {
    return (req, res, next) => {
        if (!req.user || !roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        next();
    };
};
// Rate limiting middleware
const createProductLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many product creation requests from this IP',
});
// Validation rules
const productIdValidation = (0, express_validator_1.param)('id').isInt({ min: 1 }).withMessage('Product ID must be a positive integer');
const productValidationRules = [
    (0, express_validator_1.body)('name').isString().isLength({ min: 1, max: 255 }).withMessage('Product name is required and must be less than 255 characters'),
    (0, express_validator_1.body)('description').isString().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
    (0, express_validator_1.body)('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
    (0, express_validator_1.body)('category').isString().isLength({ min: 1, max: 100 }).withMessage('Category is required and must be less than 100 characters'),
    (0, express_validator_1.body)('stock_quantity').isInt({ min: 0 }).withMessage('Stock quantity must be a non-negative integer'),
];
// GET /products - Get all products
router.get('/', async (req, res, next) => {
    try {
        const { rows } = await pool.query('SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products ORDER BY created_at DESC');
        res.json(rows);
    }
    catch (error) {
        console.error('Error fetching products:', error);
        next(error);
    }
});
// GET /products/:id - Get a specific product
router.get('/:id', productIdValidation, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const productId = parseInt(req.params.id, 10);
        const { rows } = await pool.query('SELECT id, name, description, price, category, stock_quantity, created_at, updated_at FROM products WHERE id = $1', [productId]);
        if (rows.length === 0) {
            return res.status(404).json({ error: 'Product not found' });
        }
        res.json(rows[0]);
    }
    catch (error) {
        console.error('Error fetching product:', error);
        next(error);
    }
});
// POST /products - Create a new product
router.post('/', createProductLimiter, authenticateToken, authorizeRole('admin', 'manager'), productValidationRules, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { name, description, price, category, stock_quantity } = req.body;
        const { rows } = await pool.query('INSERT INTO products (name, description, price, category, stock_quantity) VALUES ($1, $2, $3, $4, $5) RETURNING *', [name, description, price, category, stock_quantity]);
        res.status(201).json(rows[0]);
    }
    catch (error) {
        console.error('Error creating product:', error);
        next(error);
    }
});
// PUT /products/:id - Update a product
router.put('/:id', authenticateToken, authorizeRole('admin', 'manager'), productIdValidation, productValidationRules, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const productId = parseInt(req.params.id, 10);
        const { name, description, price, category, stock_quantity } = req.body;
        // Check if product exists
        const existingProduct = await pool.query('SELECT id FROM products WHERE id = $1', [productId]);
        if (existingProduct.rows.length === 0) {
            return res.status(404).json({ error: 'Product not found' });
        }
        const { rows } = await pool.query('UPDATE products SET name = $1, description = $2, price = $3, category = $4, stock_quantity = $5, updated_at = NOW() WHERE id = $6 RETURNING *', [name, description, price, category, stock_quantity, productId]);
        res.json(rows[0]);
    }
    catch (error) {
        console.error('Error updating product:', error);
        next(error);
    }
});
// DELETE /products/:id - Delete a product
router.delete('/:id', authenticateToken, authorizeRole('admin'), productIdValidation, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const productId = parseInt(req.params.id, 10);
        // Check if product exists
        const existingProduct = await pool.query('SELECT id FROM products WHERE id = $1', [productId]);
        if (existingProduct.rows.length === 0) {
            return res.status(404).json({ error: 'Product not found' });
        }
        await pool.query('DELETE FROM products WHERE id = $1', [productId]);
        res.status(204).send();
    }
    catch (error) {
        console.error('Error deleting product:', error);
        next(error);
    }
});
// Error handling middleware
router.use((error, req, res, next) => {
    console.error('Product route error:', error);
    res.status(500).json({ error: 'Internal server error' });
});
exports.default = router;
const handleError = (error, req, res, next) => {
    Logger_js_1.logger.error('Route error', {
        route: req.path,
        method: req.method,
        error: error.message,
        stack: error.stack
    });
    if (error.name === 'ValidationError') {
        return res.status(400).json({ success: false, error: error.message });
    }
    if (error.name === 'UnauthorizedError') {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    res.status(500).json({ success: false, error: 'Internal server error' });
};
// Logging utilities
Logger_js_1.logger.info('Route accessed', {
    route: req.path,
    method: req.method,
    user: req.user?.id,
    timestamp: new Date().toISOString()
});
exports.default = productRoutes;
//# sourceMappingURL=productRoutes.js.map