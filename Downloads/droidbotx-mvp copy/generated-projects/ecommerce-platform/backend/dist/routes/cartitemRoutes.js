"use strict";
// Auto-generated standardized service
// Pattern: Express Route Service
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_js_1 = require("../middleware/validation.js");
const Logger_js_1 = require("../core/Logger.js");
const express_validator_1 = require("express-validator");
const pg_1 = require("pg");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const router = (0, express_1.Router)();
const cartitemRoutes = new cartitemRoutes();
router.use(authenticateToken);
router.use(validation_js_1.validateRequest);
// Initialize router
const router = express.Router();
// Database connection pool
const pool = new pg_1.Pool({
    connectionString: process.env.DATABASE_URL,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});
// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key';
// Authentication Middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};
// Authorization Middleware
const authorizeUser = (req, res, next) => {
    const userId = req.user.id;
    const cartItemId = parseInt(req.params.id);
    // Users can only access their own cart items
    pool.query('SELECT user_id FROM cart_items WHERE id = $1', [cartItemId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Internal server error' });
        }
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Cart item not found' });
        }
        if (result.rows[0].user_id !== userId) {
            return res.status(403).json({ error: 'Access denied' });
        }
        next();
    });
};
// Validation Rules
const cartItemValidationRules = [
    (0, express_validator_1.body)('product_id')
        .isInt({ min: 1 })
        .withMessage('Product ID must be a positive integer'),
    (0, express_validator_1.body)('quantity')
        .isInt({ min: 1 })
        .withMessage('Quantity must be a positive integer'),
];
const cartItemIdValidation = [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Cart item ID must be a positive integer'),
];
// Error Formatter
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation failed',
            details: errors.array()
        });
    }
    next();
};
// GET /cartitems - Get all cart items for the authenticated user
router.get('/', authenticateToken, (req, res) => {
    const userId = req.user.id;
    pool.query('SELECT * FROM cart_items WHERE user_id = $1 ORDER BY created_at DESC', [userId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to retrieve cart items' });
        }
        res.status(200).json(result.rows);
    });
});
// GET /cartitems/:id - Get a specific cart item
router.get('/:id', authenticateToken, cartItemIdValidation, handleValidationErrors, authorizeUser, (req, res) => {
    const cartItemId = parseInt(req.params.id);
    pool.query('SELECT * FROM cart_items WHERE id = $1', [cartItemId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to retrieve cart item' });
        }
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Cart item not found' });
        }
        res.status(200).json(result.rows[0]);
    });
});
// POST /cartitems - Create a new cart item
router.post('/', authenticateToken, cartItemValidationRules, handleValidationErrors, (req, res) => {
    const userId = req.user.id;
    const { product_id, quantity } = req.body;
    // Check if item already exists in cart
    pool.query('SELECT id, quantity FROM cart_items WHERE user_id = $1 AND product_id = $2', [userId, product_id], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to check cart item' });
        }
        if (result.rows.length > 0) {
            // Update existing cart item
            const existingItemId = result.rows[0].id;
            const newQuantity = result.rows[0].quantity + quantity;
            pool.query('UPDATE cart_items SET quantity = $1, updated_at = NOW() WHERE id = $2 RETURNING *', [newQuantity, existingItemId], (updateErr, updateResult) => {
                if (updateErr) {
                    console.error('Database error:', updateErr);
                    return res.status(500).json({ error: 'Failed to update cart item' });
                }
                res.status(200).json(updateResult.rows[0]);
            });
        }
        else {
            // Create new cart item
            pool.query('INSERT INTO cart_items (user_id, product_id, quantity) VALUES ($1, $2, $3) RETURNING *', [userId, product_id, quantity], (insertErr, insertResult) => {
                if (insertErr) {
                    console.error('Database error:', insertErr);
                    return res.status(500).json({ error: 'Failed to create cart item' });
                }
                res.status(201).json(insertResult.rows[0]);
            });
        }
    });
});
// PUT /cartitems/:id - Update a cart item
router.put('/:id', authenticateToken, cartItemIdValidation, cartItemValidationRules, handleValidationErrors, authorizeUser, (req, res) => {
    const cartItemId = parseInt(req.params.id);
    const { product_id, quantity } = req.body;
    pool.query('UPDATE cart_items SET product_id = $1, quantity = $2, updated_at = NOW() WHERE id = $3 RETURNING *', [product_id, quantity, cartItemId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to update cart item' });
        }
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Cart item not found' });
        }
        res.status(200).json(result.rows[0]);
    });
});
// DELETE /cartitems/:id - Delete a cart item
router.delete('/:id', authenticateToken, cartItemIdValidation, handleValidationErrors, authorizeUser, (req, res) => {
    const cartItemId = parseInt(req.params.id);
    pool.query('DELETE FROM cart_items WHERE id = $1 RETURNING *', [cartItemId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to delete cart item' });
        }
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Cart item not found' });
        }
        res.status(200).json({ message: 'Cart item deleted successfully' });
    });
});
// DELETE /cartitems - Clear all cart items for the authenticated user
router.delete('/', authenticateToken, (req, res) => {
    const userId = req.user.id;
    pool.query('DELETE FROM cart_items WHERE user_id = $1', [userId], (err, result) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Failed to clear cart' });
        }
        res.status(200).json({ message: 'Cart cleared successfully' });
    });
});
exports.default = router;
const handleError = (error, req, res, next) => {
    Logger_js_1.logger.error('Route error', {
        route: req.path,
        method: req.method,
        error: error.message,
        stack: error.stack
    });
    if (error.name === 'ValidationError') {
        return res.status(400).json({ success: false, error: error.message });
    }
    if (error.name === 'UnauthorizedError') {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    res.status(500).json({ success: false, error: 'Internal server error' });
};
// Logging utilities
Logger_js_1.logger.info('Route accessed', {
    route: req.path,
    method: req.method,
    user: req.user?.id,
    timestamp: new Date().toISOString()
});
exports.default = cartitemRoutes;
//# sourceMappingURL=cartitemRoutes.js.map