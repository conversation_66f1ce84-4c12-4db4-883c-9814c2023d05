"use strict";
// Auto-generated standardized service
// Pattern: Express Route Service
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_js_1 = require("../middleware/validation.js");
const Logger_js_1 = require("../core/Logger.js");
const express_validator_1 = require("express-validator");
const pg_1 = require("pg");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const router = (0, express_1.Router)();
const shoppingcartRoutes = new shoppingcartRoutes();
router.use(authenticateToken);
router.use(validation_js_1.validateRequest);
// Initialize router
const router = express.Router();
// Database connection pool
const pool = new pg_1.Pool({
    connectionString: process.env.DATABASE_URL,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});
// JWT Authentication middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'secret', (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};
// Role-based authorization middleware
const authorizeRole = (roles) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user || !roles.includes(user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        next();
    };
};
// Rate limiting middleware
const createRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
});
// Apply rate limiting to all routes
router.use(createRateLimiter);
// Validation rules
const validateCartId = [
    (0, express_validator_1.param)('id').isInt().withMessage('Cart ID must be an integer'),
];
const validateCartCreation = [
    (0, express_validator_1.body)('user_id')
        .isInt()
        .withMessage('User ID must be an integer')
        .notEmpty()
        .withMessage('User ID is required'),
];
// GET /api/shoppingcarts - Get all shopping carts (admin only)
router.get('/', authenticateToken, authorizeRole(['admin']), async (req, res, next) => {
    try {
        const result = await pool.query('SELECT * FROM shopping_carts ORDER BY created_at DESC');
        res.status(200).json(result.rows);
    }
    catch (error) {
        console.error('Error fetching shopping carts:', error);
        next(error);
    }
});
// GET /api/shoppingcarts/:id - Get a specific shopping cart
router.get('/:id', authenticateToken, validateCartId, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const cartId = parseInt(req.params.id, 10);
        const userId = req.user.id;
        // Users can only access their own cart, admins can access any
        const query = `
        SELECT * FROM shopping_carts
        WHERE id = $1 AND (user_id = $2 OR $3 = 'admin')
      `;
        const result = await pool.query(query, [cartId, userId, req.user.role]);
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Shopping cart not found' });
        }
        res.status(200).json(result.rows[0]);
    }
    catch (error) {
        console.error('Error fetching shopping cart:', error);
        next(error);
    }
});
// POST /api/shoppingcarts - Create a new shopping cart
router.post('/', authenticateToken, validateCartCreation, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { user_id } = req.body;
        const requesterId = req.user.id;
        const requesterRole = req.user.role;
        // Users can only create carts for themselves unless admin
        if (requesterId !== user_id && requesterRole !== 'admin') {
            return res.status(403).json({ error: 'Cannot create cart for another user' });
        }
        const query = `
        INSERT INTO shopping_carts (user_id, created_at, updated_at)
        VALUES ($1, NOW(), NOW())
        RETURNING *
      `;
        const result = await pool.query(query, [user_id]);
        res.status(201).json(result.rows[0]);
    }
    catch (error) {
        console.error('Error creating shopping cart:', error);
        next(error);
    }
});
// PUT /api/shoppingcarts/:id - Update a shopping cart
router.put('/:id', authenticateToken, validateCartId, validateCartCreation, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const cartId = parseInt(req.params.id, 10);
        const { user_id } = req.body;
        const requesterId = req.user.id;
        const requesterRole = req.user.role;
        // Check if cart exists and user has permission
        const checkQuery = `
        SELECT user_id FROM shopping_carts
        WHERE id = $1
      `;
        const checkResult = await pool.query(checkQuery, [cartId]);
        if (checkResult.rows.length === 0) {
            return res.status(404).json({ error: 'Shopping cart not found' });
        }
        const cartOwnerId = checkResult.rows[0].user_id;
        if (requesterId !== cartOwnerId && requesterRole !== 'admin') {
            return res.status(403).json({ error: 'Cannot modify another user\'s cart' });
        }
        // Update cart
        const updateQuery = `
        UPDATE shopping_carts
        SET user_id = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;
        const result = await pool.query(updateQuery, [user_id, cartId]);
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Shopping cart not found' });
        }
        res.status(200).json(result.rows[0]);
    }
    catch (error) {
        console.error('Error updating shopping cart:', error);
        next(error);
    }
});
// DELETE /api/shoppingcarts/:id - Delete a shopping cart
router.delete('/:id', authenticateToken, validateCartId, async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const cartId = parseInt(req.params.id, 10);
        const requesterId = req.user.id;
        const requesterRole = req.user.role;
        // Check if cart exists and user has permission
        const checkQuery = `
        SELECT user_id FROM shopping_carts
        WHERE id = $1
      `;
        const checkResult = await pool.query(checkQuery, [cartId]);
        if (checkResult.rows.length === 0) {
            return res.status(404).json({ error: 'Shopping cart not found' });
        }
        const cartOwnerId = checkResult.rows[0].user_id;
        if (requesterId !== cartOwnerId && requesterRole !== 'admin') {
            return res.status(403).json({ error: 'Cannot delete another user\'s cart' });
        }
        // Delete cart
        const deleteQuery = 'DELETE FROM shopping_carts WHERE id = $1 RETURNING *';
        const result = await pool.query(deleteQuery, [cartId]);
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Shopping cart not found' });
        }
        res.status(200).json({ message: 'Shopping cart deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting shopping cart:', error);
        next(error);
    }
});
// Error handling middleware
router.use((error, req, res, next) => {
    console.error('Shopping cart route error:', error);
    res.status(500).json({ error: 'Internal server error' });
});
exports.default = router;
const handleError = (error, req, res, next) => {
    Logger_js_1.logger.error('Route error', {
        route: req.path,
        method: req.method,
        error: error.message,
        stack: error.stack
    });
    if (error.name === 'ValidationError') {
        return res.status(400).json({ success: false, error: error.message });
    }
    if (error.name === 'UnauthorizedError') {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    res.status(500).json({ success: false, error: 'Internal server error' });
};
// Logging utilities
Logger_js_1.logger.info('Route accessed', {
    route: req.path,
    method: req.method,
    user: req.user?.id,
    timestamp: new Date().toISOString()
});
exports.default = shoppingcartRoutes;
//# sourceMappingURL=shoppingcartRoutes.js.map