import { Pool } from 'pg';
import { CartItem } from '../models/CartItem.js';
import { Logger } from '../utils/Logger.js';
export declare class CartItemService {
    private db;
    private transaction;
    private queryBuilder;
    constructor();
}
interface CartItemRepository {
    findById(id: number): Promise<CartItem | null>;
    findByUserId(userId: number): Promise<CartItem[]>;
    create(cartItem: CartItem): Promise<CartItem>;
    update(cartItem: CartItem): Promise<CartItem>;
    delete(id: number): Promise<boolean>;
    deleteByUserId(userId: number): Promise<boolean>;
}
export declare class CartItemService {
    private repository;
    private logger;
    constructor(repository: CartItemRepository, logger: Logger);
    static createWithDatabase(pool: Pool, logger: Logger): CartItemService;
    getCartItemById(id: number): Promise<CartItem>;
    getCartItemsByUserId(userId: number): Promise<CartItem[]>;
    addCartItem(userId: number, productId: number, quantity: number): Promise<CartItem>;
    updateCartItemQuantity(id: number, quantity: number): Promise<CartItem>;
    removeCartItem(id: number): Promise<boolean>;
    clearUserCart(userId: number): Promise<boolean>;
    getCartTotalQuantity(userId: number): Promise<number>;
    getCartItemCount(userId: number): Promise<number>;
}
export default CartItemService;
//# sourceMappingURL=CartItemService.d.ts.map