import { User, UserCreationAttributes, UserUpdateAttributes } from '../models/User.js';
import { UserRepository } from '../repositories/UserRepository.js';
import { Logger } from '../utils/Logger.js';
export declare class UserService {
    private db;
    private transaction;
    private queryBuilder;
    constructor();
}
export interface UserServiceInterface {
    getUserById(id: number): Promise<User>;
    getAllUsers(): Promise<User[]>;
    createUser(userData: UserCreationAttributes): Promise<User>;
    updateUser(id: number, userData: UserUpdateAttributes): Promise<User>;
    deleteUser(id: number): Promise<boolean>;
}
export declare class UserService implements UserServiceInterface {
    private logger;
    private userRepository;
    constructor(userRepository: UserRepository, logger: Logger);
    getUserById(id: number): Promise<User>;
    getAllUsers(): Promise<User[]>;
    createUser(userData: UserCreationAttributes): Promise<User>;
    updateUser(id: number, userData: UserUpdateAttributes): Promise<User>;
    deleteUser(id: number): Promise<boolean>;
}
export default UserService;
//# sourceMappingURL=UserService.d.ts.map