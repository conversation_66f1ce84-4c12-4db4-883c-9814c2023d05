import { Pool } from 'pg';
import { ShoppingCart } from '../models/ShoppingCart.js';
export declare class ShoppingCartService {
    private db;
    private transaction;
    private queryBuilder;
    constructor();
}
interface ShoppingCartItem {
    productId: string;
    quantity: number;
    price: number;
}
interface CreateShoppingCartRequest {
    userId: string;
    items?: ShoppingCartItem[];
}
interface UpdateShoppingCartRequest {
    cartId: string;
    items: ShoppingCartItem[];
}
interface AddItemRequest {
    cartId: string;
    item: ShoppingCartItem;
}
interface RemoveItemRequest {
    cartId: string;
    productId: string;
}
export declare class ShoppingCartService {
    private logger;
    private repository;
    constructor(pool: Pool);
    createShoppingCart(request: CreateShoppingCartRequest): Promise<ShoppingCart>;
    getShoppingCart(cartId: string): Promise<ShoppingCart>;
    updateShoppingCart(request: UpdateShoppingCartRequest): Promise<ShoppingCart>;
    addItemToCart(request: AddItemRequest): Promise<ShoppingCart>;
    removeItemFromCart(request: RemoveItemRequest): Promise<ShoppingCart>;
    clearShoppingCart(cartId: string): Promise<ShoppingCart>;
    deleteShoppingCart(cartId: string): Promise<void>;
    getUserActiveCart(userId: string): Promise<ShoppingCart | null>;
    calculateCartTotal(cartId: string): Promise<number>;
}
export default ShoppingCartService;
//# sourceMappingURL=ShoppingCartService.d.ts.map