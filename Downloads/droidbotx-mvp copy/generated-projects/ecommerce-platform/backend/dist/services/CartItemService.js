"use strict";
// Auto-generated standardized service
// Pattern: Database Service
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemService = void 0;
const connection_js_1 = require("../database/connection.js");
const transaction_js_1 = require("../database/transaction.js");
const queryBuilder_js_1 = require("../database/queryBuilder.js");
const Logger_js_1 = require("../core/Logger.js");
const BusinessError_js_1 = require("../errors/BusinessError.js");
const CartItem_js_1 = require("../models/CartItem.js");
class CartItemService {
    constructor() {
        this.db = connection_js_1.DatabaseConnection.getInstance();
        this.transaction = new transaction_js_1.TransactionManager(this.db);
        this.queryBuilder = new queryBuilder_js_1.QueryBuilder();
    }
}
exports.CartItemService = CartItemService;
class DatabaseCartItemRepository {
    constructor(pool, logger) {
        this.pool = pool;
        this.logger = logger;
    }
    async findById(id) {
        try {
            const query = 'SELECT * FROM cart_items WHERE id = $1 AND deleted_at IS NULL';
            const result = await this.pool.query(query, [id]);
            return result.rows.length ? new CartItem_js_1.CartItem(result.rows[0]) : null;
        }
        catch (error) {
            this.logger.error('Error finding cart item by ID', { id, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to retrieve cart item');
        }
    }
    async findByUserId(userId) {
        try {
            const query = 'SELECT * FROM cart_items WHERE user_id = $1 AND deleted_at IS NULL';
            const result = await this.pool.query(query, [userId]);
            return result.rows.map(row => new CartItem_js_1.CartItem(row));
        }
        catch (error) {
            this.logger.error('Error finding cart items by user ID', { userId, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to retrieve cart items');
        }
    }
    async create(cartItem) {
        try {
            const query = `;`;
            INSERT;
            INTO;
            cart_items(user_id, product_id, quantity, created_at, updated_at);
            VALUES($1, $2, $3, NOW(), NOW());
            RETURNING *
                `;`;
            const values = [cartItem.userId, cartItem.productId, cartItem.quantity];
            const result = await this.pool.query(query, values);
            return new CartItem_js_1.CartItem(result.rows[0]);
        }
        catch (error) {
            this.logger.error('Error creating cart item', { cartItem, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to create cart item');
        }
    }
    async update(cartItem) {
        try {
            const query = `;`;
            UPDATE;
            cart_items;
            SET;
            user_id = $1, product_id = $2, quantity = $3, updated_at = NOW();
            WHERE;
            id = $4;
            AND;
            deleted_at;
            IS;
            NULL;
            RETURNING *
                `;`;
            const values = [cartItem.userId, cartItem.productId, cartItem.quantity, cartItem.id];
            const result = await this.pool.query(query, values);
            if (result.rows.length === 0) {
                throw new BusinessError_js_1.BusinessError('NOT_FOUND', 'Cart item not found');
            }
            return new CartItem_js_1.CartItem(result.rows[0]);
        }
        catch (error) {
            if (error instanceof BusinessError_js_1.BusinessError)
                throw error;
            this.logger.error('Error updating cart item', { cartItem, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to update cart item');
        }
    }
    async delete(id) {
        try {
            const query = 'UPDATE cart_items SET deleted_at = NOW() WHERE id = $1 AND deleted_at IS NULL';
            const result = await this.pool.query(query, [id]);
            return result.rowCount > 0;
        }
        catch (error) {
            this.logger.error('Error deleting cart item', { id, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to delete cart item');
        }
    }
    async deleteByUserId(userId) {
        try {
            const query = 'UPDATE cart_items SET deleted_at = NOW() WHERE user_id = $1 AND deleted_at IS NULL';
            const result = await this.pool.query(query, [userId]);
            return result.rowCount > 0;
        }
        catch (error) {
            this.logger.error('Error deleting cart items by user ID', { userId, error });
            throw new BusinessError_js_1.BusinessError('DATABASE_ERROR', 'Failed to delete cart items');
        }
    }
}
class CartItemService {
    constructor(repository, logger) {
        this.repository = repository;
        this.logger = logger;
    }
    static createWithDatabase(pool, logger) {
        const repository = new DatabaseCartItemRepository(pool, logger);
        return new CartItemService(repository, logger);
    }
    async getCartItemById(id) {
        this.logger.info('Fetching cart item by ID', { id });
        if (!id || id <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid cart item ID');
        }
        const cartItem = await this.repository.findById(id);
        if (!cartItem) {
            throw new BusinessError_js_1.BusinessError('NOT_FOUND', 'Cart item not found');
        }
        return cartItem;
    }
    async getCartItemsByUserId(userId) {
        this.logger.info('Fetching cart items by user ID', { userId });
        if (!userId || userId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid user ID');
        }
        return await this.repository.findByUserId(userId);
    }
    async addCartItem(userId, productId, quantity) {
        this.logger.info('Adding item to cart', { userId, productId, quantity });
        // Validate inputs
        if (!userId || userId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid user ID');
        }
        if (!productId || productId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid product ID');
        }
        if (!quantity || quantity <= 0) {
            throw new BusinessError_js_1.ValidationError('Quantity must be greater than zero');
        }
        // Business rule: Check if item already exists in cart
        const existingItems = await this.repository.findByUserId(userId);
        const existingItem = existingItems.find(item => item.productId === productId);
        if (existingItem) {
            // Update quantity if item already exists
            existingItem.quantity += quantity;
            return await this.repository.update(existingItem);
        }
        // Create new cart item
        const cartItem = new CartItem_js_1.CartItem({
            userId,
            productId,
            quantity
        });
        return await this.repository.create(cartItem);
    }
    async updateCartItemQuantity(id, quantity) {
        this.logger.info('Updating cart item quantity', { id, quantity });
        if (!id || id <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid cart item ID');
        }
        if (!quantity || quantity <= 0) {
            throw new BusinessError_js_1.ValidationError('Quantity must be greater than zero');
        }
        const cartItem = await this.repository.findById(id);
        if (!cartItem) {
            throw new BusinessError_js_1.BusinessError('NOT_FOUND', 'Cart item not found');
        }
        cartItem.quantity = quantity;
        return await this.repository.update(cartItem);
    }
    async removeCartItem(id) {
        this.logger.info('Removing cart item', { id });
        if (!id || id <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid cart item ID');
        }
        const result = await this.repository.delete(id);
        if (!result) {
            throw new BusinessError_js_1.BusinessError('NOT_FOUND', 'Cart item not found');
        }
        return result;
    }
    async clearUserCart(userId) {
        this.logger.info('Clearing user cart', { userId });
        if (!userId || userId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid user ID');
        }
        return await this.repository.deleteByUserId(userId);
    }
    async getCartTotalQuantity(userId) {
        this.logger.info('Calculating cart total quantity', { userId });
        if (!userId || userId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid user ID');
        }
        const cartItems = await this.repository.findByUserId(userId);
        return cartItems.reduce((total, item) => total + item.quantity, 0);
    }
    async getCartItemCount(userId) {
        this.logger.info('Getting cart item count', { userId });
        if (!userId || userId <= 0) {
            throw new BusinessError_js_1.ValidationError('Invalid user ID');
        }
        const cartItems = await this.repository.findByUserId(userId);
        return cartItems.length;
    }
}
exports.CartItemService = CartItemService;
async;
handleDatabaseError(error, Error, operation, string, data ?  : any);
Promise < never > {
    logger: Logger_js_1.logger, : .error('Database operation failed', {
        service: 'CartItemService',
        operation,
        error: error.message,
        data: JSON.stringify(data),
        stack: error.stack
    }),
    if(error) { }, : .message.includes('duplicate key')
};
{
    throw new Error('Record already exists');
}
if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
}
if (error.message.includes('not null')) {
    throw new Error('Required field missing');
}
throw new Error('Database operation failed');
// Logging utilities
Logger_js_1.logger.info('Database operation', {
    service: 'CartItemService',
    operation: '{operation}',
    duration: Date.now() - startTime,
    recordsAffected: result.rowCount,
    timestamp: new Date().toISOString()
});
exports.default = CartItemService;
//# sourceMappingURL=CartItemService.js.map