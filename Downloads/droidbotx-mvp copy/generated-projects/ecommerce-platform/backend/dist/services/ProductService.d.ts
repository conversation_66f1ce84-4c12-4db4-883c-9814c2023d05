import { Pool } from 'pg';
import { Product } from '../models/Product.js';
export declare class ProductService {
    private db;
    private transaction;
    private queryBuilder;
    constructor();
}
export interface ProductServiceInterface {
    getAllProducts(): Promise<Product[]>;
    getProductById(id: number): Promise<Product | null>;
    createProduct(product: Product): Promise<Product>;
    updateProduct(id: number, product: Product): Promise<Product>;
    deleteProduct(id: number): Promise<boolean>;
}
export declare class ProductService implements ProductServiceInterface {
    private productRepository;
    private logger;
    constructor(pool: Pool);
    getAllProducts(): Promise<Product[]>;
    getProductById(id: number): Promise<Product | null>;
    createProduct(product: Product): Promise<Product>;
    updateProduct(id: number, product: Product): Promise<Product>;
    deleteProduct(id: number): Promise<boolean>;
    private validateProduct;
}
export default ProductService;
//# sourceMappingURL=ProductService.d.ts.map