"use strict";
// Auto-generated standardized service
// Pattern: Database Service
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShoppingCartService = void 0;
const connection_js_1 = require("../database/connection.js");
const transaction_js_1 = require("../database/transaction.js");
const queryBuilder_js_1 = require("../database/queryBuilder.js");
const Logger_js_1 = require("../core/Logger.js");
const DatabaseError_js_1 = require("../errors/DatabaseError.js");
const ValidationError_js_1 = require("../errors/ValidationError.js");
const ShoppingCart_js_1 = require("../models/ShoppingCart.js");
const ShoppingCartRepository_js_1 = require("../repositories/ShoppingCartRepository.js");
const Logger_js_2 = require("../utils/Logger.js");
class ShoppingCartService {
    constructor() {
        this.db = connection_js_1.DatabaseConnection.getInstance();
        this.transaction = new transaction_js_1.TransactionManager(this.db);
        this.queryBuilder = new queryBuilder_js_1.QueryBuilder();
    }
}
exports.ShoppingCartService = ShoppingCartService;
class ShoppingCartService {
    constructor(pool) {
        this.logger = new Logger_js_2.Logger('ShoppingCartService');
        this.repository = new ShoppingCartRepository_js_1.ShoppingCartRepository(pool);
    }
    async createShoppingCart(request) {
        try {
            this.logger.info('Creating new shopping cart', { userId: request.userId });
            // Validate input
            if (!request.userId) {
                throw new ValidationError_js_1.ValidationError('User ID is required');
            }
            // Business rule: Check if user already has an active cart
            const existingCart = await this.repository.findByUserId(request.userId);
            if (existingCart) {
                this.logger.info('User already has an active cart', { userId: request.userId, cartId: existingCart.id });
                return existingCart;
            }
            // Create new cart
            const cart = new ShoppingCart_js_1.ShoppingCart({
                userId: request.userId,
                items: request.items || [],
                createdAt: new Date(),
                updatedAt: new Date()
            });
            const result = await this.repository.create(cart);
            this.logger.info('Shopping cart created successfully', { cartId: result.id });
            return result;
        }
        catch (error) {
            this.logger.error('Failed to create shopping cart', { error: error.message });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to create shopping cart');
        }
    }
    async getShoppingCart(cartId) {
        try {
            this.logger.info('Retrieving shopping cart', { cartId });
            if (!cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            const cart = await this.repository.findById(cartId);
            if (!cart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            this.logger.info('Shopping cart retrieved successfully', { cartId });
            return cart;
        }
        catch (error) {
            this.logger.error('Failed to retrieve shopping cart', { error: error.message, cartId });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to retrieve shopping cart');
        }
    }
    async updateShoppingCart(request) {
        try {
            this.logger.info('Updating shopping cart', { cartId: request.cartId });
            // Validate input
            if (!request.cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            if (!request.items) {
                throw new ValidationError_js_1.ValidationError('Items are required');
            }
            // Validate items
            for (const item of request.items) {
                if (!item.productId || item.quantity <= 0 || item.price < 0) {
                    throw new ValidationError_js_1.ValidationError('Invalid item data');
                }
            }
            // Retrieve existing cart
            const existingCart = await this.repository.findById(request.cartId);
            if (!existingCart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            // Update cart
            existingCart.items = request.items;
            existingCart.updatedAt = new Date();
            const result = await this.repository.update(existingCart);
            this.logger.info('Shopping cart updated successfully', { cartId: result.id });
            return result;
        }
        catch (error) {
            this.logger.error('Failed to update shopping cart', { error: error.message });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to update shopping cart');
        }
    }
    async addItemToCart(request) {
        try {
            this.logger.info('Adding item to shopping cart', { cartId: request.cartId, productId: request.item.productId });
            // Validate input
            if (!request.cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            if (!request.item.productId || request.item.quantity <= 0 || request.item.price < 0) {
                throw new ValidationError_js_1.ValidationError('Invalid item data');
            }
            // Retrieve existing cart
            const cart = await this.repository.findById(request.cartId);
            if (!cart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            // Check if item already exists in cart
            const existingItemIndex = cart.items.findIndex(item => item.productId === request.item.productId);
            if (existingItemIndex >= 0) {
                // Update quantity of existing item
                cart.items[existingItemIndex].quantity += request.item.quantity;
            }
            else {
                // Add new item
                cart.items.push(request.item);
            }
            cart.updatedAt = new Date();
            const result = await this.repository.update(cart);
            this.logger.info('Item added to shopping cart successfully', { cartId: result.id });
            return result;
        }
        catch (error) {
            this.logger.error('Failed to add item to shopping cart', { error: error.message });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to add item to shopping cart');
        }
    }
    async removeItemFromCart(request) {
        try {
            this.logger.info('Removing item from shopping cart', { cartId: request.cartId, productId: request.productId });
            // Validate input
            if (!request.cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            if (!request.productId) {
                throw new ValidationError_js_1.ValidationError('Product ID is required');
            }
            // Retrieve existing cart
            const cart = await this.repository.findById(request.cartId);
            if (!cart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            // Remove item
            cart.items = cart.items.filter(item => item.productId !== request.productId);
            cart.updatedAt = new Date();
            const result = await this.repository.update(cart);
            this.logger.info('Item removed from shopping cart successfully', { cartId: result.id });
            return result;
        }
        catch (error) {
            this.logger.error('Failed to remove item from shopping cart', { error: error.message });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to remove item from shopping cart');
        }
    }
    async clearShoppingCart(cartId) {
        try {
            this.logger.info('Clearing shopping cart', { cartId });
            if (!cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            // Retrieve existing cart
            const cart = await this.repository.findById(cartId);
            if (!cart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            // Clear items
            cart.items = [];
            cart.updatedAt = new Date();
            const result = await this.repository.update(cart);
            this.logger.info('Shopping cart cleared successfully', { cartId });
            return result;
        }
        catch (error) {
            this.logger.error('Failed to clear shopping cart', { error: error.message, cartId });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to clear shopping cart');
        }
    }
    async deleteShoppingCart(cartId) {
        try {
            this.logger.info('Deleting shopping cart', { cartId });
            if (!cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            await this.repository.deleteById(cartId);
            this.logger.info('Shopping cart deleted successfully', { cartId });
        }
        catch (error) {
            this.logger.error('Failed to delete shopping cart', { error: error.message, cartId });
            throw new DatabaseError_js_1.DatabaseError('Failed to delete shopping cart');
        }
    }
    async getUserActiveCart(userId) {
        try {
            this.logger.info('Retrieving user active cart', { userId });
            if (!userId) {
                throw new ValidationError_js_1.ValidationError('User ID is required');
            }
            const cart = await this.repository.findByUserId(userId);
            this.logger.info('User active cart retrieved', { userId, cartId: cart?.id });
            return cart;
        }
        catch (error) {
            this.logger.error('Failed to retrieve user active cart', { error: error.message, userId });
            throw new DatabaseError_js_1.DatabaseError('Failed to retrieve user active cart');
        }
    }
    async calculateCartTotal(cartId) {
        try {
            this.logger.info('Calculating cart total', { cartId });
            if (!cartId) {
                throw new ValidationError_js_1.ValidationError('Cart ID is required');
            }
            const cart = await this.repository.findById(cartId);
            if (!cart) {
                throw new ValidationError_js_1.ValidationError('Shopping cart not found');
            }
            const total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            this.logger.info('Cart total calculated', { cartId, total });
            return total;
        }
        catch (error) {
            this.logger.error('Failed to calculate cart total', { error: error.message, cartId });
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            throw new DatabaseError_js_1.DatabaseError('Failed to calculate cart total');
        }
    }
}
exports.ShoppingCartService = ShoppingCartService;
async;
handleDatabaseError(error, Error, operation, string, data ?  : any);
Promise < never > {
    logger: Logger_js_1.logger, : .error('Database operation failed', {
        service: 'ShoppingCartService',
        operation,
        error: error.message,
        data: JSON.stringify(data),
        stack: error.stack
    }),
    if(error) { }, : .message.includes('duplicate key')
};
{
    throw new Error('Record already exists');
}
if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
}
if (error.message.includes('not null')) {
    throw new Error('Required field missing');
}
throw new Error('Database operation failed');
// Logging utilities
Logger_js_1.logger.info('Database operation', {
    service: 'ShoppingCartService',
    operation: '{operation}',
    duration: Date.now() - startTime,
    recordsAffected: result.rowCount,
    timestamp: new Date().toISOString()
});
exports.default = ShoppingCartService;
//# sourceMappingURL=ShoppingCartService.js.map