"use strict";
// Auto-generated standardized service
// Pattern: Database Service
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const connection_js_1 = require("../database/connection.js");
const transaction_js_1 = require("../database/transaction.js");
const queryBuilder_js_1 = require("../database/queryBuilder.js");
const Logger_js_1 = require("../core/Logger.js");
const CustomErrors_js_1 = require("../utils/CustomErrors.js");
class UserService {
    constructor() {
        this.db = connection_js_1.DatabaseConnection.getInstance();
        this.transaction = new transaction_js_1.TransactionManager(this.db);
        this.queryBuilder = new queryBuilder_js_1.QueryBuilder();
    }
}
exports.UserService = UserService;
class UserService {
    constructor(userRepository, logger) {
        this.userRepository = userRepository;
        this.logger = logger;
    }
    async getUserById(id) {
        this.logger.info('Fetching user by ID', { userId: id });
        if (!id || id <= 0) {
            throw new CustomErrors_js_1.ValidationError('User ID must be a positive integer');
        }
        try {
            const user = await this.userRepository.findById(id);
            if (!user) {
                throw new CustomErrors_js_1.BusinessError(`User with ID ${id} not found`);
            }
            return user;
        }
        catch (error) {
            if (error instanceof CustomErrors_js_1.BusinessError) {
                throw error;
            }
            this.logger.error('Error fetching user by ID', { userId: id, error: error.message });
            throw new CustomErrors_js_1.BusinessError('Failed to fetch user');
        }
    }
    async getAllUsers() {
        this.logger.info('Fetching all users');
        try {
            const users = await this.userRepository.findAll();
            this.logger.info(`Retrieved ${users.length} users`);
            return users;
        }
        catch (error) {
            this.logger.error('Error fetching all users', { error: error.message });
            throw new CustomErrors_js_1.BusinessError('Failed to fetch users');
        }
    }
    async createUser(userData) {
        this.logger.info('Creating new user', { email: userData.email });
        // Validate required fields
        if (!userData.email) {
            throw new CustomErrors_js_1.ValidationError('Email is required');
        }
        if (!userData.firstName) {
            throw new CustomErrors_js_1.ValidationError('First name is required');
        }
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userData.email)) {
            throw new CustomErrors_js_1.ValidationError('Invalid email format');
        }
        try {
            // Check if user already exists
            const existingUser = await this.userRepository.findByEmail(userData.email);
            if (existingUser) {
                throw new CustomErrors_js_1.ValidationError('User with this email already exists');
            }
            const user = await this.userRepository.create(userData);
            this.logger.info('User created successfully', { userId: user.id, email: user.email });
            return user;
        }
        catch (error) {
            if (error instanceof CustomErrors_js_1.ValidationError) {
                throw error;
            }
            this.logger.error('Error creating user', { email: userData.email, error: error.message });
            throw new CustomErrors_js_1.BusinessError('Failed to create user');
        }
    }
    async updateUser(id, userData) {
        this.logger.info('Updating user', { userId: id });
        if (!id || id <= 0) {
            throw new CustomErrors_js_1.ValidationError('User ID must be a positive integer');
        }
        // Validate email format if provided
        if (userData.email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(userData.email)) {
                throw new CustomErrors_js_1.ValidationError('Invalid email format');
            }
        }
        try {
            // Check if user exists
            const existingUser = await this.userRepository.findById(id);
            if (!existingUser) {
                throw new CustomErrors_js_1.BusinessError(`User with ID ${id} not found`);
            }
            // Check email uniqueness if email is being updated
            if (userData.email && userData.email !== existingUser.email) {
                const userWithSameEmail = await this.userRepository.findByEmail(userData.email);
                if (userWithSameEmail) {
                    throw new CustomErrors_js_1.ValidationError('User with this email already exists');
                }
            }
            const user = await this.userRepository.update(id, userData);
            this.logger.info('User updated successfully', { userId: id });
            return user;
        }
        catch (error) {
            if (error instanceof CustomErrors_js_1.ValidationError || error instanceof CustomErrors_js_1.BusinessError) {
                throw error;
            }
            this.logger.error('Error updating user', { userId: id, error: error.message });
            throw new CustomErrors_js_1.BusinessError('Failed to update user');
        }
    }
    async deleteUser(id) {
        this.logger.info('Deleting user', { userId: id });
        if (!id || id <= 0) {
            throw new CustomErrors_js_1.ValidationError('User ID must be a positive integer');
        }
        try {
            // Check if user exists
            const existingUser = await this.userRepository.findById(id);
            if (!existingUser) {
                throw new CustomErrors_js_1.BusinessError(`User with ID ${id} not found`);
            }
            await this.userRepository.delete(id);
            this.logger.info('User deleted successfully', { userId: id });
            return true;
        }
        catch (error) {
            if (error instanceof CustomErrors_js_1.BusinessError) {
                throw error;
            }
            this.logger.error('Error deleting user', { userId: id, error: error.message });
            throw new CustomErrors_js_1.BusinessError('Failed to delete user');
        }
    }
}
exports.UserService = UserService;
async;
handleDatabaseError(error, Error, operation, string, data ?  : any);
Promise < never > {
    logger: Logger_js_1.logger, : .error('Database operation failed', {
        service: 'UserService',
        operation,
        error: error.message,
        data: JSON.stringify(data),
        stack: error.stack
    }),
    if(error) { }, : .message.includes('duplicate key')
};
{
    throw new Error('Record already exists');
}
if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
}
if (error.message.includes('not null')) {
    throw new Error('Required field missing');
}
throw new Error('Database operation failed');
// Logging utilities
Logger_js_1.logger.info('Database operation', {
    service: 'UserService',
    operation: '{operation}',
    duration: Date.now() - startTime,
    recordsAffected: result.rowCount,
    timestamp: new Date().toISOString()
});
exports.default = UserService;
//# sourceMappingURL=UserService.js.map