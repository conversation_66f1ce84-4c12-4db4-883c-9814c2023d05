import { Pool } from 'pg';
export interface User {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    passwordHash: string;
    salt: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateUserInput {
    email: string;
    firstName: string;
    lastName: string;
    password: string;
}
export interface UpdateUserInput {
    email?: string;
    firstName?: string;
    lastName?: string;
    password?: string;
}
export interface CartItem {
    productId: number;
    quantity: number;
    price: number;
}
export declare class UserModel {
    private dbPool;
    private logger;
    constructor(dbPool: Pool);
    /**
     * Creates a new user in the database
     */
    create(userData: CreateUserInput): Promise<User>;
    /**
     * Retrieves a user by ID
     */
    read(userId: number): Promise<User>;
    /**
     * Updates user information
     */
    update(userId: number, updateData: UpdateUserInput): Promise<User>;
    /**
     * Deletes a user by ID
     */
    delete(userId: number): Promise<void>;
    /**
     * Registers a new user (alias for create with additional business logic)
     */
    registerUser(userData: CreateUserInput): Promise<User>;
    /**
     * Gets user's shopping cart'
     */
    getUserCart(userId: number): Promise<CartItem[]>;
    /**
     * Finds a user by email
     */
    private findByEmail;
    /**
     * Validates user input data
     */
    private validateUserInput;
    /**
     * Hashes a password with a salt
     */
    private hashPassword;
}
//# sourceMappingURL=UserModel.d.ts.map