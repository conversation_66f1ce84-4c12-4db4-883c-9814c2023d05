import { Pool } from 'pg';
import { BusinessError, ValidationError } from '../utils/CustomErrors.js';
interface CartItem {
    productId: number;
    quantity: number;
    price: number;
}
interface ShoppingCart {
    id?: number;
    userId: number;
    items: CartItem[];
    createdAt: Date;
    updatedAt: Date;
}
declare class CartNotFoundError extends BusinessError {
    constructor(cartId: number);
}
declare class InvalidCartDataError extends ValidationError {
    constructor(message: string);
}
declare class ShoppingCartModel {
    private logger;
    private repository;
    constructor(dbPool: Pool);
    /**
     * Validates shopping cart data
     */
    private validateCartData;
    /**
     * Creates a new shopping cart
     */
    createCart(userId: number): Promise<ShoppingCart>;
    /**
     * Gets the contents of a shopping cart
     */
    getCartContents(cartId: number): Promise<ShoppingCart>;
    /**
     * Clears all items from a shopping cart
     */
    clearCart(cartId: number): Promise<ShoppingCart>;
    /**
     * Adds an item to the shopping cart
     */
    addItemToCart(cartId: number, item: CartItem): Promise<ShoppingCart>;
    /**
     * Removes an item from the shopping cart
     */
    removeItemFromCart(cartId: number, productId: number): Promise<ShoppingCart>;
    /**
     * Updates the quantity of an item in the shopping cart
     */
    updateItemQuantity(cartId: number, productId: number, quantity: number): Promise<ShoppingCart>;
    /**
     * Gets a user's shopping cart'
     */
    getUserCart(userId: number): Promise<ShoppingCart | null>;
}
export { ShoppingCart, CartItem, ShoppingCartModel, CartNotFoundError, InvalidCartDataError };
//# sourceMappingURL=ShoppingCartModel.d.ts.map