import { Pool } from 'pg';
import { Logger } from '../utils/Logger.js';
export interface Product {
    id?: number;
    name: string;
    description: string;
    price: number;
    category: string;
    stockQuantity: number;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface CreateProductInput {
    name: string;
    description: string;
    price: number;
    category: string;
    stockQuantity: number;
}
export interface UpdateProductInput {
    id: number;
    name?: string;
    description?: string;
    price?: number;
    category?: string;
    stockQuantity?: number;
}
export declare class ProductModel {
    private pool;
    private logger;
    constructor(pool: Pool, logger: Logger);
    /**
     * Create a new product
     */
    create(productData: CreateProductInput): Promise<Product>;
    /**
     * Retrieve a product by ID
     */
    read(id: number): Promise<Product | null>;
    /**
     * Update an existing product
     */
    update(productData: UpdateProductInput): Promise<Product>;
    /**
     * Delete a product by ID
     */
    delete(id: number): Promise<boolean>;
    /**
     * Search products with optional filters
     */
    searchProducts(filters?: {
        category?: string;
        minPrice?: number;
        maxPrice?: number;
        name?: string;
    }, limit?: number, offset?: number): Promise<Product[]>;
    /**
     * Validate product data according to business rules
     */
    private validateProductData;
}
//# sourceMappingURL=ProductModel.d.ts.map