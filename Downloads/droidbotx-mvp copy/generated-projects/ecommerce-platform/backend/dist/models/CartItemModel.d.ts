import { Pool } from 'pg';
import { CartItem } from '../types/CartItem.js';
import { Logger } from '../utils/Logger.js';
export interface CartItemRepository {
    create(cartItem: CartItem): Promise<CartItem>;
    findById(id: number): Promise<CartItem | null>;
    update(id: number, cartItem: Partial<CartItem>): Promise<CartItem>;
    delete(id: number): Promise<boolean>;
    findByCartAndProduct(cartId: number, productId: number): Promise<CartItem | null>;
    findByCartId(cartId: number): Promise<CartItem[]>;
}
export declare class CartItemModel implements CartItemRepository {
    private readonly logger;
    private readonly pool;
    constructor(pool: Pool, logger: Logger);
    create(cartItem: CartItem): Promise<CartItem>;
    findById(id: number): Promise<CartItem | null>;
    update(id: number, cartItem: Partial<CartItem>): Promise<CartItem>;
    delete(id: number): Promise<boolean>;
    findByCartAndProduct(cartId: number, productId: number): Promise<CartItem | null>;
    findByCartId(cartId: number): Promise<CartItem[]>;
    addToCart(cartId: number, productId: number, quantity: number): Promise<CartItem>;
    updateQuantity(cartId: number, productId: number, quantity: number): Promise<CartItem>;
    removeFromCart(cartId: number, productId: number): Promise<boolean>;
}
//# sourceMappingURL=CartItemModel.d.ts.map