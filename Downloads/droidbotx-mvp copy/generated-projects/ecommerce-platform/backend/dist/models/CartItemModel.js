"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemModel = void 0;
const DatabaseError_js_1 = require("../errors/DatabaseError.js");
const ValidationError_js_1 = require("../errors/ValidationError.js");
class CartItemModel {
    constructor(pool, logger) {
        this.pool = pool;
        this.logger = logger;
    }
    async create(cartItem) {
        this.logger.debug('Creating cart item', { cartItem });
        // Validate required fields
        if (!cartItem.cartId) {
            throw new ValidationError_js_1.ValidationError('Cart ID is required');
        }
        if (!cartItem.productId) {
            throw new ValidationError_js_1.ValidationError('Product ID is required');
        }
        if (cartItem.quantity <= 0) {
            throw new ValidationError_js_1.ValidationError('Quantity must be greater than zero');
        }
        const client = await this.pool.connect();
        try {
            const query = `
        INSERT INTO cart_items (cart_id, product_id, quantity, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
        RETURNING *
      `;
            const values = [
                cartItem.cartId,
                cartItem.productId,
                cartItem.quantity
            ];
            const result = await client.query(query, values);
            const createdCartItem = result.rows[0];
            this.logger.info('Cart item created successfully', {
                cartItemId: createdCartItem.id,
                cartId: createdCartItem.cart_id,
                productId: createdCartItem.product_id
            });
            return {
                id: createdCartItem.id,
                cartId: createdCartItem.cart_id,
                productId: createdCartItem.product_id,
                quantity: createdCartItem.quantity,
                createdAt: createdCartItem.created_at,
                updatedAt: createdCartItem.updated_at
            };
        }
        catch (error) {
            this.logger.error('Failed to create cart item', { error, cartItem });
            throw new DatabaseError_js_1.DatabaseError('Failed to create cart item', error);
        }
        finally {
            client.release();
        }
    }
    async findById(id) {
        this.logger.debug('Finding cart item by ID', { id });
        if (!id || id <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart item ID is required');
        }
        const client = await this.pool.connect();
        try {
            const query = `
        SELECT id, cart_id, product_id, quantity, created_at, updated_at
        FROM cart_items
        WHERE id = $1
      `;
            const result = await client.query(query, [id]);
            if (result.rows.length === 0) {
                this.logger.debug('Cart item not found', { id });
                return null;
            }
            const row = result.rows[0];
            const cartItem = {
                id: row.id,
                cartId: row.cart_id,
                productId: row.product_id,
                quantity: row.quantity,
                createdAt: row.created_at,
                updatedAt: row.updated_at
            };
            this.logger.debug('Cart item found', { id, cartItem });
            return cartItem;
        }
        catch (error) {
            this.logger.error('Failed to find cart item by ID', { error, id });
            throw new DatabaseError_js_1.DatabaseError('Failed to find cart item', error);
        }
        finally {
            client.release();
        }
    }
    async update(id, cartItem) {
        this.logger.debug('Updating cart item', { id, cartItem });
        if (!id || id <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart item ID is required');
        }
        if (cartItem.quantity !== undefined && cartItem.quantity <= 0) {
            throw new ValidationError_js_1.ValidationError('Quantity must be greater than zero');
        }
        const client = await this.pool.connect();
        try {
            const fields = [];
            const values = [];
            let paramCount = 1;
            if (cartItem.quantity !== undefined) {
                fields.push(`quantity = $${paramCount}`);
                values.push(cartItem.quantity);
                paramCount++;
            }
            if (fields.length === 0) {
                throw new ValidationError_js_1.ValidationError('No valid fields to update');
            }
            fields.push(`updated_at = NOW()`);
            values.push(id);
            const query = `
        UPDATE cart_items
        SET ${fields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `;
            const result = await client.query(query, values);
            if (result.rows.length === 0) {
                throw new ValidationError_js_1.ValidationError(`Cart item with ID ${id} not found`);
            }
            const updatedRow = result.rows[0];
            const updatedCartItem = {
                id: updatedRow.id,
                cartId: updatedRow.cart_id,
                productId: updatedRow.product_id,
                quantity: updatedRow.quantity,
                createdAt: updatedRow.created_at,
                updatedAt: updatedRow.updated_at
            };
            this.logger.info('Cart item updated successfully', { id, updatedCartItem });
            return updatedCartItem;
        }
        catch (error) {
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            this.logger.error('Failed to update cart item', { error, id, cartItem });
            throw new DatabaseError_js_1.DatabaseError('Failed to update cart item', error);
        }
        finally {
            client.release();
        }
    }
    async delete(id) {
        this.logger.debug('Deleting cart item', { id });
        if (!id || id <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart item ID is required');
        }
        const client = await this.pool.connect();
        try {
            const query = 'DELETE FROM cart_items WHERE id = $1';
            const result = await client.query(query, [id]);
            const deleted = result.rowCount > 0;
            if (deleted) {
                this.logger.info('Cart item deleted successfully', { id });
            }
            else {
                this.logger.debug('Cart item not found for deletion', { id });
            }
            return deleted;
        }
        catch (error) {
            this.logger.error('Failed to delete cart item', { error, id });
            throw new DatabaseError_js_1.DatabaseError('Failed to delete cart item', error);
        }
        finally {
            client.release();
        }
    }
    async findByCartAndProduct(cartId, productId) {
        this.logger.debug('Finding cart item by cart and product', { cartId, productId });
        if (!cartId || cartId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart ID is required');
        }
        if (!productId || productId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid product ID is required');
        }
        const client = await this.pool.connect();
        try {
            const query = `
        SELECT id, cart_id, product_id, quantity, created_at, updated_at
        FROM cart_items
        WHERE cart_id = $1 AND product_id = $2
      `;
            const result = await client.query(query, [cartId, productId]);
            if (result.rows.length === 0) {
                this.logger.debug('Cart item not found for cart and product', { cartId, productId });
                return null;
            }
            const row = result.rows[0];
            const cartItem = {
                id: row.id,
                cartId: row.cart_id,
                productId: row.product_id,
                quantity: row.quantity,
                createdAt: row.created_at,
                updatedAt: row.updated_at
            };
            this.logger.debug('Cart item found for cart and product', { cartItem });
            return cartItem;
        }
        catch (error) {
            this.logger.error('Failed to find cart item by cart and product', { error, cartId, productId });
            throw new DatabaseError_js_1.DatabaseError('Failed to find cart item', error);
        }
        finally {
            client.release();
        }
    }
    async findByCartId(cartId) {
        this.logger.debug('Finding cart items by cart ID', { cartId });
        if (!cartId || cartId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart ID is required');
        }
        const client = await this.pool.connect();
        try {
            const query = `
        SELECT id, cart_id, product_id, quantity, created_at, updated_at
        FROM cart_items
        WHERE cart_id = $1
        ORDER BY created_at DESC
      `;
            const result = await client.query(query, [cartId]);
            const cartItems = result.rows.map(row => ({
                id: row.id,
                cartId: row.cart_id,
                productId: row.product_id,
                quantity: row.quantity,
                createdAt: row.created_at,
                updatedAt: row.updated_at
            }));
            this.logger.debug('Cart items found by cart ID', { cartId, count: cartItems.length });
            return cartItems;
        }
        catch (error) {
            this.logger.error('Failed to find cart items by cart ID', { error, cartId });
            throw new DatabaseError_js_1.DatabaseError('Failed to find cart items', error);
        }
        finally {
            client.release();
        }
    }
    async addToCart(cartId, productId, quantity) {
        this.logger.debug('Adding item to cart', { cartId, productId, quantity });
        if (!cartId || cartId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart ID is required');
        }
        if (!productId || productId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid product ID is required');
        }
        if (quantity <= 0) {
            throw new ValidationError_js_1.ValidationError('Quantity must be greater than zero');
        }
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            // Check if item already exists in cart
            const existingItem = await this.findByCartAndProduct(cartId, productId);
            let cartItem;
            if (existingItem) {
                // Update quantity if item exists
                cartItem = await this.update(existingItem.id, {
                    quantity: existingItem.quantity + quantity
                });
            }
            else {
                // Create new cart item
                cartItem = await this.create({
                    cartId,
                    productId,
                    quantity
                });
            }
            await client.query('COMMIT');
            this.logger.info('Item added to cart successfully', {
                cartId,
                productId,
                quantity,
                cartItemId: cartItem.id
            });
            return cartItem;
        }
        catch (error) {
            await client.query('ROLLBACK');
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            this.logger.error('Failed to add item to cart', { error, cartId, productId, quantity });
            throw new DatabaseError_js_1.DatabaseError('Failed to add item to cart', error);
        }
        finally {
            client.release();
        }
    }
    async updateQuantity(cartId, productId, quantity) {
        this.logger.debug('Updating cart item quantity', { cartId, productId, quantity });
        if (!cartId || cartId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart ID is required');
        }
        if (!productId || productId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid product ID is required');
        }
        if (quantity <= 0) {
            throw new ValidationError_js_1.ValidationError('Quantity must be greater than zero');
        }
        const client = await this.pool.connect();
        try {
            // Find the cart item
            const existingItem = await this.findByCartAndProduct(cartId, productId);
            if (!existingItem) {
                throw new ValidationError_js_1.ValidationError('Cart item not found');
            }
            // Update the quantity
            const updatedItem = await this.update(existingItem.id, { quantity });
            this.logger.info('Cart item quantity updated successfully', {
                cartId,
                productId,
                quantity,
                cartItemId: existingItem.id
            });
            return updatedItem;
        }
        catch (error) {
            if (error instanceof ValidationError_js_1.ValidationError) {
                throw error;
            }
            this.logger.error('Failed to update cart item quantity', { error, cartId, productId, quantity });
            throw new DatabaseError_js_1.DatabaseError('Failed to update cart item quantity', error);
        }
        finally {
            client.release();
        }
    }
    async removeFromCart(cartId, productId) {
        this.logger.debug('Removing item from cart', { cartId, productId });
        if (!cartId || cartId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid cart ID is required');
        }
        if (!productId || productId <= 0) {
            throw new ValidationError_js_1.ValidationError('Valid product ID is required');
        }
        const client = await this.pool.connect();
        try {
            // Find the cart item
            const existingItem = await this.findByCartAndProduct(cartId, productId);
            if (!existingItem) {
                this.logger.debug('Cart item not found for removal', { cartId, productId });
                return false;
            }
            // Delete the cart item
            const deleted = await this.delete(existingItem.id);
            if (deleted) {
                this.logger.info('Item removed from cart successfully', {
                    cartId,
                    productId,
                    cartItemId: existingItem.id
                });
            }
            return deleted;
        }
        catch (error) {
            this.logger.error('Failed to remove item from cart', { error, cartId, productId });
            throw new DatabaseError_js_1.DatabaseError('Failed to remove item from cart', error);
        }
        finally {
            client.release();
        }
    }
}
exports.CartItemModel = CartItemModel;
//# sourceMappingURL=CartItemModel.js.map