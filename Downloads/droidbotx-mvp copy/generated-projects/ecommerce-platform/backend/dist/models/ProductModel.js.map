{"version": 3, "file": "ProductModel.js", "sourceRoot": "", "sources": ["../../src/models/ProductModel.ts"], "names": [], "mappings": ";;;AAMA,0DAAuF;AAiCvF,MAAa,YAAY;IAIvB,YAAY,IAAU,EAAE,MAAc;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,WAA+B;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAE5E,iBAAiB;QACjB,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;OAIb,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,WAAW,CAAC,IAAI;gBAChB,WAAW,CAAC,WAAW;gBACvB,WAAW,CAAC,KAAK;gBACjB,WAAW,CAAC,QAAQ;gBACpB,WAAW,CAAC,aAAa;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAgB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5E,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YACnF,MAAM,IAAI,6BAAa,CAAC,0BAA0B,CAAC,CAAC;QACtD,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,EAAU;QACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;QAE1D,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,+BAAe,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;OAIb,CAAC;YAEF,MAAM,MAAM,GAAgB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5D,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;gBACzD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC9E,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACvF,MAAM,IAAI,6BAAa,CAAC,4BAA4B,CAAC,CAAC;QACxD,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,WAA+B;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,+BAAe,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,6BAAa,CAAC,mBAAmB,CAAC,CAAC;YAC/C,CAAC;YAED,sDAAsD;YACtD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,EAAE,EAAE,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,WAAW,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,kBAAkB,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACvC,CAAC;YAED,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACpC,6BAA6B;gBAC7B,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,+BAAe,CAAC,0BAA0B,CAAC,CAAC;gBACxD,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,YAAY,UAAU,EAAE,EAAE,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YAED,IAAI,WAAW,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,eAAe,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC3C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,WAAW,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC5C,sCAAsC;gBACtC,IAAI,WAAW,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;oBAClC,MAAM,IAAI,+BAAe,CAAC,mCAAmC,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,qBAAqB,UAAU,EAAE,EAAE,CAAC,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YACzC,CAAC;YAED,yCAAyC;YACzC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAElC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,mCAAmC;gBAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC/E,OAAO,eAAe,CAAC;YACzB,CAAC;YAED,MAAM,KAAK,GAAG;;cAEN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;sBACT,UAAU;;OAEzB,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAE5B,MAAM,MAAM,GAAgB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,cAAc,GAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YACnF,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,+BAAe,IAAI,KAAK,YAAY,6BAAa,EAAE,CAAC;gBACvE,MAAM,KAAK,CAAC;YACd,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YACnF,MAAM,IAAI,6BAAa,CAAC,0BAA0B,CAAC,CAAC;QACtD,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;QAExD,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,+BAAe,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,iDAAiD,CAAC;YAChE,MAAM,MAAM,GAAgB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5D,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;gBACtE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACrF,MAAM,IAAI,6BAAa,CAAC,0BAA0B,CAAC,CAAC;QACtD,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,OAKC,EACD,QAAgB,EAAE,EAClB,SAAiB,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAEnE,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,+BAAe,CAAC,uCAAuC,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,IAAI,KAAK,GAAG;;;;OAIX,CAAC;YAEF,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;gBACtB,KAAK,IAAI,oBAAoB,UAAU,EAAE,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,OAAO,EAAE,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACpC,KAAK,IAAI,kBAAkB,UAAU,EAAE,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,OAAO,EAAE,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACpC,KAAK,IAAI,kBAAkB,UAAU,EAAE,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;gBAClB,KAAK,IAAI,oBAAoB,UAAU,EAAE,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;YACnC,CAAC;YAED,KAAK,IAAI,oCAAoC,UAAU,EAAE,YAAY,UAAU,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE3B,MAAM,MAAM,GAAgB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAc,MAAM,CAAC,IAAI,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAChF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YACjF,MAAM,IAAI,6BAAa,CAAC,2BAA2B,CAAC,CAAC;QACvD,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,WAAoD;QAC9E,IAAI,MAAM,IAAI,WAAW,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACzF,MAAM,IAAI,+BAAe,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,aAAa,IAAI,WAAW,IAAI,CAAC,CAAC,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9G,MAAM,IAAI,+BAAe,CAAC,iCAAiC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,OAAO,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,SAAS,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACzF,MAAM,IAAI,+BAAe,CAAC,6CAA6C,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACrG,MAAM,IAAI,+BAAe,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,eAAe,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,aAAa,KAAK,SAAS,IAAI,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC;YACjH,MAAM,IAAI,+BAAe,CAAC,8CAA8C,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;CACF;AApSD,oCAoSC"}