"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemController = void 0;
const BusinessError_js_1 = require("../errors/BusinessError.js");
const ValidationError_js_1 = require("../errors/ValidationError.js");
const Logger_js_1 = require("../utils/Logger.js");
class CartItemController {
    constructor(cartItemService) {
        this.cartItemService = cartItemService;
        this.logger = Logger_js_1.Logger.getInstance();
        /**
         * Get all cart items for a user
         */
        this.getAllCartItems = async (req, res, next) => {
            try {
                const userId = req.params.userId;
                if (!userId) {
                    throw new ValidationError_js_1.ValidationError('User ID is required');
                }
                this.logger.info(`Fetching all cart items for user: ${userId}`);
                const cartItems = await this.cartItemService.getAllCartItems(userId);
                res.status(200).json({
                    success: true,
                    data: cartItems
                });
            }
            catch (error) {
                this.logger.error(`Error fetching cart items: ${error.message}`, error);
                next(error);
            }
        };
        /**
         * Get a specific cart item by ID
         */
        this.getCartItemById = async (req, res, next) => {
            try {
                const cartItemId = req.params.id;
                if (!cartItemId) {
                    throw new ValidationError_js_1.ValidationError('Cart item ID is required');
                }
                this.logger.info(`Fetching cart item with ID: ${cartItemId}`);
                const cartItem = await this.cartItemService.getCartItemById(cartItemId);
                if (!cartItem) {
                    throw new BusinessError_js_1.BusinessError('Cart item not found', 404);
                }
                res.status(200).json({
                    success: true,
                    data: cartItem
                });
            }
            catch (error) {
                this.logger.error(`Error fetching cart item: ${error.message}`, error);
                next(error);
            }
        };
        /**
         * Add item to cart
         */
        this.addCartItem = async (req, res, next) => {
            try {
                const cartItemData = req.body;
                // Validate required fields
                if (!cartItemData.userId || !cartItemData.productId || !cartItemData.quantity) {
                    throw new ValidationError_js_1.ValidationError('User ID, Product ID, and Quantity are required');
                }
                this.logger.info(`Adding item to cart for user: ${cartItemData.userId}`);
                const cartItem = await this.cartItemService.addCartItem(cartItemData);
                res.status(201).json({
                    success: true,
                    data: cartItem
                });
            }
            catch (error) {
                this.logger.error(`Error adding cart item: ${error.message}`, error);
                next(error);
            }
        };
        /**
         * Update cart item quantity
         */
        this.updateCartItem = async (req, res, next) => {
            try {
                const cartItemId = req.params.id;
                const updateData = req.body;
                if (!cartItemId) {
                    throw new ValidationError_js_1.ValidationError('Cart item ID is required');
                }
                this.logger.info(`Updating cart item with ID: ${cartItemId}`);
                const cartItem = await this.cartItemService.updateCartItem(cartItemId, updateData);
                res.status(200).json({
                    success: true,
                    data: cartItem
                });
            }
            catch (error) {
                this.logger.error(`Error updating cart item: ${error.message}`, error);
                next(error);
            }
        };
        /**
         * Remove item from cart
         */
        this.removeCartItem = async (req, res, next) => {
            try {
                const cartItemId = req.params.id;
                if (!cartItemId) {
                    throw new ValidationError_js_1.ValidationError('Cart item ID is required');
                }
                this.logger.info(`Removing cart item with ID: ${cartItemId}`);
                await this.cartItemService.removeCartItem(cartItemId);
                res.status(200).json({
                    success: true,
                    message: 'Cart item removed successfully'
                });
            }
            catch (error) {
                this.logger.error(`Error removing cart item: ${error.message}`, error);
                next(error);
            }
        };
        /**
         * Clear all items from user's cart'
         */
        this.clearCart = async (req, res, next) => {
            try {
                const userId = req.params.userId;
                if (!userId) {
                    throw new ValidationError_js_1.ValidationError('User ID is required');
                }
                this.logger.info(`Clearing cart for user: ${userId}`);
                await this.cartItemService.clearCart(userId);
                res.status(200).json({
                    success: true,
                    message: 'Cart cleared successfully'
                });
            }
            catch (error) {
                this.logger.error(`Error clearing cart: ${error.message}`, error);
                next(error);
            }
        };
    }
}
exports.CartItemController = CartItemController;
//# sourceMappingURL=CartItemController.js.map