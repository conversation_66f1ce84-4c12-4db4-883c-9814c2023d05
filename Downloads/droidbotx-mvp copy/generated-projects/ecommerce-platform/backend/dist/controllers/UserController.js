"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const NotFoundError_js_1 = require("../errors/NotFoundError.js");
const ValidationError_js_1 = require("../errors/ValidationError.js");
const Logger_js_1 = require("../utils/Logger.js");
class UserController {
    constructor(userService) {
        this.getAllUsers = async (req, res, next) => {
            try {
                this.logger.info('Fetching all users');
                const users = await this.userService.getAllUsers();
                res.status(200).json({
                    success: true,
                    data: users,
                    count: users.length
                });
            }
            catch (error) {
                this.logger.error('Error fetching all users', error);
                next(error);
            }
        };
        this.getUserById = async (req, res, next) => {
            try {
                const userId = parseInt(req.params.id, 10);
                if (isNaN(userId)) {
                    throw new ValidationError_js_1.ValidationError('Invalid user ID provided');
                }
                this.logger.info(`Fetching user with ID: ${userId}`);
                const user = await this.userService.getUserById(userId);
                if (!user) {
                    throw new NotFoundError_js_1.NotFoundError(`User with ID ${userId} not found`);
                }
                res.status(200).json({
                    success: true,
                    data: user
                });
            }
            catch (error) {
                this.logger.error(`Error fetching user with ID: ${req.params.id}`, error);
                next(error);
            }
        };
        this.createUser = async (req, res, next) => {
            try {
                this.logger.info('Creating new user');
                // Validate request body
                const userData = req.body;
                if (!userData.email || !userData.firstName || !userData.lastName) {
                    throw new ValidationError_js_1.ValidationError('Missing required fields: email, firstName, lastName');
                }
                const newUser = await this.userService.createUser(userData);
                res.status(201).json({
                    success: true,
                    data: newUser,
                    message: 'User created successfully'
                });
            }
            catch (error) {
                this.logger.error('Error creating user', error);
                next(error);
            }
        };
        this.updateUser = async (req, res, next) => {
            try {
                const userId = parseInt(req.params.id, 10);
                if (isNaN(userId)) {
                    throw new ValidationError_js_1.ValidationError('Invalid user ID provided');
                }
                this.logger.info(`Updating user with ID: ${userId}`);
                const userData = req.body;
                const updatedUser = await this.userService.updateUser(userId, userData);
                res.status(200).json({
                    success: true,
                    data: updatedUser,
                    message: 'User updated successfully'
                });
            }
            catch (error) {
                this.logger.error(`Error updating user with ID: ${req.params.id}`, error);
                next(error);
            }
        };
        this.deleteUser = async (req, res, next) => {
            try {
                const userId = parseInt(req.params.id, 10);
                if (isNaN(userId)) {
                    throw new ValidationError_js_1.ValidationError('Invalid user ID provided');
                }
                this.logger.info(`Deleting user with ID: ${userId}`);
                await this.userService.deleteUser(userId);
                res.status(200).json({
                    success: true,
                    message: 'User deleted successfully'
                });
            }
            catch (error) {
                this.logger.error(`Error deleting user with ID: ${req.params.id}`, error);
                next(error);
            }
        };
        this.getUserProfile = async (req, res, next) => {
            try {
                // Assuming user ID is available in request (from auth middleware)
                const userId = req.user.id;
                this.logger.info(`Fetching profile for user ID: ${userId}`);
                const user = await this.userService.getUserById(userId);
                if (!user) {
                    throw new NotFoundError_js_1.NotFoundError('User profile not found');
                }
                res.status(200).json({
                    success: true,
                    data: user
                });
            }
            catch (error) {
                this.logger.error('Error fetching user profile', error);
                next(error);
            }
        };
        this.userService = userService;
        this.logger = new Logger_js_1.Logger('UserController');
    }
}
exports.UserController = UserController;
//# sourceMappingURL=UserController.js.map