"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const errorHandler_js_1 = require("../middleware/errorHandler.js");
const logger_js_1 = require("../utils/logger.js");
class ProductController {
    constructor(productService) {
        /**
         * Get all products
         */
        this.getAllProducts = async (req, res, next) => {
            try {
                logger_js_1.logger.info('Fetching all products');
                const products = await this.productService.getAllProducts();
                res.status(200).json({
                    success: true,
                    data: products,
                    count: products.length
                });
            }
            catch (error) {
                logger_js_1.logger.error('Error fetching all products:', error);
                next(error);
            }
        };
        /**
         * Get product by ID
         */
        this.getProductById = async (req, res, next) => {
            try {
                const productId = parseInt(req.params.id, 10);
                if (isNaN(productId)) {
                    throw new errorHandler_js_1.AppError('Invalid product ID', 400);
                }
                logger_js_1.logger.info(`Fetching product with ID: ${productId}`);
                const product = await this.productService.getProductById(productId);
                if (!product) {
                    throw new errorHandler_js_1.AppError('Product not found', 404);
                }
                res.status(200).json({
                    success: true,
                    data: product
                });
            }
            catch (error) {
                logger_js_1.logger.error(`Error fetching product by ID:`, error);
                next(error);
            }
        };
        /**
         * Create a new product
         */
        this.createProduct = async (req, res, next) => {
            try {
                const productData = req.body;
                logger_js_1.logger.info('Creating new product', { productData });
                // Validate required fields
                if (!productData.name || !productData.price) {
                    throw new errorHandler_js_1.AppError('Name and price are required', 400);
                }
                const newProduct = await this.productService.createProduct(productData);
                res.status(201).json({
                    success: true,
                    data: newProduct
                });
            }
            catch (error) {
                logger_js_1.logger.error('Error creating product:', error);
                next(error);
            }
        };
        /**
         * Update product
         */
        this.updateProduct = async (req, res, next) => {
            try {
                const productId = parseInt(req.params.id, 10);
                const productData = req.body;
                if (isNaN(productId)) {
                    throw new errorHandler_js_1.AppError('Invalid product ID', 400);
                }
                logger_js_1.logger.info(`Updating product with ID: ${productId}`, { productData });
                const updatedProduct = await this.productService.updateProduct(productId, productData);
                if (!updatedProduct) {
                    throw new errorHandler_js_1.AppError('Product not found', 404);
                }
                res.status(200).json({
                    success: true,
                    data: updatedProduct
                });
            }
            catch (error) {
                logger_js_1.logger.error('Error updating product:', error);
                next(error);
            }
        };
        /**
         * Delete product
         */
        this.deleteProduct = async (req, res, next) => {
            try {
                const productId = parseInt(req.params.id, 10);
                if (isNaN(productId)) {
                    throw new errorHandler_js_1.AppError('Invalid product ID', 400);
                }
                logger_js_1.logger.info(`Deleting product with ID: ${productId}`);
                const deleted = await this.productService.deleteProduct(productId);
                if (!deleted) {
                    throw new errorHandler_js_1.AppError('Product not found', 404);
                }
                res.status(200).json({
                    success: true,
                    message: 'Product deleted successfully'
                });
            }
            catch (error) {
                logger_js_1.logger.error('Error deleting product:', error);
                next(error);
            }
        };
        this.productService = productService;
    }
}
exports.ProductController = ProductController;
//# sourceMappingURL=ProductController.js.map