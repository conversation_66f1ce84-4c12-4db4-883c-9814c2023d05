import { NextFunction, Request, Response } from 'express';
import { ShoppingCartService } from '../services/ShoppingCartService.js';
import { Logger } from '../utils/Logger.js';
export declare class ShoppingCartController {
    private shoppingCartService;
    private logger;
    constructor(shoppingCartService: ShoppingCartService, logger: Logger);
    /**
     * Get shopping cart by ID
     */
    getCart: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Create a new shopping cart
     */
    createCart: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Add item to shopping cart
     */
    addItem: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Remove item from shopping cart
     */
    removeItem: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Update item quantity in shopping cart
     */
    updateItemQuantity: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Clear shopping cart
     */
    clearCart: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Get cart summary
     */
    getCartSummary: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=ShoppingCartController.d.ts.map