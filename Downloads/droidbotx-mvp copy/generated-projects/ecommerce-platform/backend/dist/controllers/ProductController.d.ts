import { NextFunction, Request, Response } from 'express';
import { ProductService } from '../services/ProductService.js';
export declare class ProductController {
    private productService;
    constructor(productService: ProductService);
    /**
     * Get all products
     */
    getAllProducts: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Get product by ID
     */
    getProductById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Create a new product
     */
    createProduct: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Update product
     */
    updateProduct: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Delete product
     */
    deleteProduct: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=ProductController.d.ts.map