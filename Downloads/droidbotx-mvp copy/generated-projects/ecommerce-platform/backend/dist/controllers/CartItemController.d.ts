import { NextFunction, Request, Response } from 'express';
import { CartItemService } from '../services/CartItemService.js';
export declare class CartItemController {
    private cartItemService;
    private logger;
    constructor(cartItemService: CartItemService);
    /**
     * Get all cart items for a user
     */
    getAllCartItems: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Get a specific cart item by ID
     */
    getCartItemById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Add item to cart
     */
    addCartItem: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Update cart item quantity
     */
    updateCartItem: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Remove item from cart
     */
    removeCartItem: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Clear all items from user's cart'
     */
    clearCart: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=CartItemController.d.ts.map