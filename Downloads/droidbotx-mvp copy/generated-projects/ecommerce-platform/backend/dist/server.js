"use strict";
// Import routes
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pool = void 0;
// Load environment variables
const pg_1 = require("pg");
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const auth_js_1 = __importDefault(require("./routes/auth.js"));
const cart_js_1 = __importDefault(require("./routes/cart.js"));
const products_js_1 = __importDefault(require("./routes/products.js"));
const users_js_1 = __importDefault(require("./routes/users.js"));
dotenv_1.default.config();
// Create Express app
const app = express();
const PORT = parseInt(process.env.PORT, 10) || 3000;
// Database connection
exports.pool = new pg_1.Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: parseInt(process.env.DB_PORT, 10) || 5432,
});
// Middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use(express.json());
// Rate limiting
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);
// Routes
app.use('/api/products', products_js_1.default);
app.use('/api/cart', cart_js_1.default);
app.use('/api/users', users_js_1.default);
app.use('/api/auth', auth_js_1.default);
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});
app.use((err, req, res, next) => {
    console.error(err.stack);
    const errorResponse = {
        status: err.status || 500,
        message: err.message || 'Internal Server Error',
    };
    if (process.env.NODE_ENV === 'development') {
        errorResponse.details = err.stack;
    }
    res.status(errorResponse.status).json(errorResponse);
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ status: 404, message: 'Route not found' });
});
// Start server
const startServer = async () => {
    try {
        // Test database connection
        const client = await exports.pool.connect();
        console.log('Database connected successfully');
        client.release();
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
exports.default = app;
`;
//# sourceMappingURL=server.js.map