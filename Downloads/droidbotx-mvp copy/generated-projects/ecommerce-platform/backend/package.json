{"name": "ecommerce-platform-backend", "version": "1.0.0", "description": "Backend for E-commerce Platform - Manage products, orders, inventory, and customer accounts", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typeorm": "typeorm-ts-node-commonjs", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/data-source.ts", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/data-source.ts", "seed:run": "ts-node src/seeds/run-seeds.ts"}, "keywords": ["ecommerce", "express", "typescript", "postgresql", "jwt", "shopping-cart", "inventory-management"], "author": "E-commerce Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "typeorm": "^0.3.17", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "stripe": "^14.4.0", "nodemailer": "^6.9.7", "winston": "^3.11.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.16", "@types/multer": "^1.4.10", "@types/nodemailer": "^6.4.14", "typescript": "^5.2.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0"}}