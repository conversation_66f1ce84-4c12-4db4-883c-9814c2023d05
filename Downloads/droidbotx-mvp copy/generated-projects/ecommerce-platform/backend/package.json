{"name": "ecommerce-platform-backend", "version": "1.0.0", "description": "Backend for E-commerce Platform - RESTful API with Express.js, PostgreSQL, and JWT authentication", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typeorm": "typeorm-ts-node-commonjs", "migrate": "typeorm-ts-node-commonjs migration:run -d src/data-source.ts", "migrate:revert": "typeorm-ts-node-commonjs migration:revert -d src/data-source.ts"}, "keywords": ["ecommerce", "express", "typescript", "postgresql", "jwt", "rest-api"], "author": "E-commerce Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "pg": "^8.11.1", "typeorm": "^0.3.17", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.4", "stripe": "^12.12.0", "nodemailer": "^6.9.4", "winston": "^3.10.0", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/node": "^20.4.5", "@types/express": "^4.17.17", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/cors": "^2.8.13", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "typescript": "^5.1.6", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.6.2", "@types/jest": "^29.5.3", "supertest": "^6.3.3", "@types/supertest": "^2.0.12", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}