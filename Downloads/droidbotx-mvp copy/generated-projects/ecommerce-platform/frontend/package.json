{"name": "ecommerce-platform-frontend", "version": "1.0.0", "description": "Frontend for E-commerce Platform", "main": "src/index.tsx", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "format": "prettier --write src/**/*.{ts,tsx,json,css}"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.0", "axios": "^1.4.0", "formik": "^2.4.2", "yup": "^1.2.0", "react-query": "^3.39.3", "styled-components": "^6.0.0", "react-icons": "^4.10.1", "jwt-decode": "^3.1.2", "react-toastify": "^9.1.3"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.26", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "eslint": "^8.43.0", "eslint-config-react-app": "^7.0.1", "prettier": "^2.8.8", "typescript": "^5.1.3", "react-scripts": "5.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}