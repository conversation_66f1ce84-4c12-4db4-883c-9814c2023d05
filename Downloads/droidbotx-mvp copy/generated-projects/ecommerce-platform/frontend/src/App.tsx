



import { jwtDecode } from 'jwt-decode';
import { BrowserRouter as Router, Link, Navigate, Route, Routes } from 'react-router-dom';
import React from 'react';

tsx`



// Types
interface User {
  id: string;
  name: string;
  email: string;
  role: 'customer' | 'admin';
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl: string;
  stock: number;
}

interface CartItem {
  productId: string;
  quantity: number;
  product: Product;
}

interface ShoppingCart {
  items: CartItem[];
  total: number;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

interface AppState {
  auth: AuthState;
  cart: ShoppingCart;
}

// Actions
type AuthAction = 
  | { type: 'LOGIN'; payload: { user: User; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_SESSION' };

type CartAction = 
  | { type: 'ADD_TO_CART'; payload: CartItem }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'UPDATE_QUANTITY'; payload: { productId: string; quantity: number } }
  | { type: 'CLEAR_CART' };

type AppAction = AuthAction | CartAction;

// Initial state
const initialAuthState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false
};

const initialCartState: ShoppingCart = {
  items: [],
  total: 0
};

const initialState: AppState = {
  auth: initialAuthState,
  cart: initialCartState
};

// Reducers
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN':
      localStorage.setItem('token', action.payload.token);
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true
      };
    case 'LOGOUT':
      localStorage.removeItem('token');
      return initialAuthState;
    case 'RESTORE_SESSION':
      return state;
    default:
      return state;
  }
};

const cartReducer = (state: ShoppingCart, action: CartAction): ShoppingCart => {
  switch (action.type) {
    case 'ADD_TO_CART':
      const existingItem = state.items.find(item => item.productId === action.payload.productId);
      if (existingItem) {
        return {
          ...state,
          items: state.items.map(item => {
  // TODO: Implement function body
}
            item.productId === action.payload.productId
              ? { ...item, quantity: item.quantity + action.payload.quantity }
              : item
          ),
          total: state.total + (action.payload.product.price * action.payload.quantity)
        };
      }
      return {
        ...state,
        items: [...state.items, action.payload],
        total: state.total + (action.payload.product.price * action.payload.quantity)
      };
    case 'REMOVE_FROM_CART':
      const itemToRemove = state.items.find(item => item.productId === action.payload);
      if (!itemToRemove) return state;
      return {
        ...state,
        items: state.items.filter(item => item.productId !== action.payload),
        total: state.total - (itemToRemove.product.price * itemToRemove.quantity)
      };
    case 'UPDATE_QUANTITY':
      const itemToUpdate = state.items.find(item => item.productId === action.payload.productId);
      if (!itemToUpdate) return state;
      const quantityDiff = action.payload.quantity - itemToUpdate.quantity;
      return {
        ...state,
        items: state.items.map(item => {
  // TODO: Implement function body
}
          item.productId === action.payload.productId
            ? { ...item, quantity: action.payload.quantity }
            : item
        ),
        total: state.total + (itemToUpdate.product.price * quantityDiff)
      };
    case 'CLEAR_CART':
      return initialCartState;
    default:
      return state;
  }
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  return {
    auth: authReducer(state.auth, action as AuthAction),
    cart: cartReducer(state.cart, action as CartAction)
  };
};

// Contexts
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}>({
  state: initialState,
  dispatch: () => null
});

// Custom hooks
const useAppContext = () => useContext(AppContext);

const useAuth = () => {
  const { state, dispatch } = useAppContext();
  
  const login = (token: string) => {
    try {
      const decoded: any = jwtDecode(token);
      const user: User = {
        id: decoded.userId,
        name: decoded.name,
        email: decoded.email,
        role: decoded.role
      };
      dispatch({ type: 'LOGIN', payload: { user, token } });
    } catch (error) {
      console.error('Invalid token', error);
    }
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  return { ...state.auth, login, logout };
};

// Components
const Header: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const { cart } = useAppContext().state;

  return (;
    <header className="bg-white shadow">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="text-2xl font-bold text-indigo-600">E-Shop</Link>
        
        <nav className="flex space-x-6">
          <Link to="/" className="text-gray-700 hover:text-indigo-600">Home</Link>
          <Link to="/products" className="text-gray-700 hover:text-indigo-600">Products</Link>
          {isAuthenticated && (
            <Link to="/cart" className="text-gray-700 hover:text-indigo-600">
              Cart ({cart.items.length});
            </Link>
          )}
        </nav>
        
        <div className="flex items-center space-x-4">
          {isAuthenticated ? (
            <>
              <span className="text-gray-700">Hello, {user?.name}</span>
              {user?.role === 'admin' && (
                <Link to="/admin" className="text-gray-700 hover:text-indigo-600">Admin</Link>
              )}
              <button 
                onClick={logout}
                className="text-gray-700 hover:text-indigo-600"
              >
                Logout
              </button>
            </>
          ) : (
            <Link to="/login" className="text-gray-700 hover:text-indigo-600">Login</Link>
          )}
        </div>
      </div>
    </header>
  );
};

const HomePage: React.FC = () => {
  return (;
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold text-center mb-8">Welcome to E-Shop</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-2xl font-semibold mb-4">Featured Products</h2>
          <p className="text-gray-600">Check out our latest collection</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-2xl font-semibold mb-4">Special Offers</h2>
          <p className="text-gray-600">Limited time discounts</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-2xl font-semibold mb-4">New Arrivals</h2>
          <p className="text-gray-600">Fresh products just for you</p>
        </div>
      </div>
    </div>
  );
};

const ProductsPage: React.FC = () => {
  // In a real app, this would fetch from API
  const products: Product[] = [;
    {
      id: '1',
      name: 'Premium Headphones',
      description: 'Noise-cancelling wireless headphones',
      price: 199.99,
      category: 'Electronics',
      imageUrl: 'https://via.placeholder.com/300',
      stock: 25
    },
    {
      id: '2',
      name: 'Smart Watch',
      description: 'Fitness tracker with heart rate monitor',
      price: 149.99,
      category: 'Electronics',
      imageUrl: 'https://via.placeholder.com/300',
      stock: 15
    }
  ];

  const { dispatch } = useAppContext();

  const handleAddToCart = (product: Product) => {
    dispatch({
      type: 'ADD_TO_CART',
      payload: {
        productId: product.id,
        quantity: 1,
        product
      }
    });
  };

  return (;
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Products</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {products.map(product => (
          <div key={product.id} className="bg-white rounded-lg shadow overflow-hidden">
            <img 
              src={product.imageUrl} 
              alt={product.name} 
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h2 className="text-xl font-semibold">{product.name}</h2>
              <p className="text-gray-600 mt-2">{product.description}</p>
              <div className="mt-4 flex justify-between items-center">
                <span className="text-lg font-bold">${product.price.toFixed(2)}</span>
                <button
                  onClick={() => handleAddToCart(product)}
                  className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
                >
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CartPage: React.FC = () => {
  const { cart } = useAppContext().state;
  const dispatch = useAppContext().dispatch;

  const handleRemoveItem = (productId: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: productId });
  };

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    if (quantity < 1) return;
    dispatch({ 
      type: 'UPDATE_QUANTITY', 
      payload: { productId, quantity } 
    });
  };

  if (cart.items.length === 0) {
    return (;
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-3xl font-bold mb-4">Your Cart</h1>
        <p className="text-gray-600">Your cart is empty</p>
        <Link to="/products" className="mt-4 inline-block bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
          Continue Shopping
        </Link>
      </div>
    );
  }

  return (;
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Your Cart</h1>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {cart.items.map(item => (
              <tr key={item.productId}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <img 
                        className="h-10 w-10 rounded-full" 
                        src={item.product.imageUrl} 
                        alt={item.product.name} 
                      />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{item.product.name}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${item.product.price.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <button 
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1)}
                      className="bg-gray-200 rounded-l px-3 py-1"
                    >
                      -