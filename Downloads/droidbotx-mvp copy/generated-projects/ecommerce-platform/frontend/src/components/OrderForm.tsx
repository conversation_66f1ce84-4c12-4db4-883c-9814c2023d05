

interface Order {
  id: string;
  customerId: string;
  productId: string;
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
}

interface OrderFormProps {
  orderId?: string;
  onSubmit: (order: Order) => void;
  onCancel: () => void;
}

const OrderForm: React.FC<OrderFormProps> = ({ orderId, onSubmit, onCancel }) => {
  const [order, setOrder] = useState<Order>({
    id: '',
    customerId: '',
    productId: '',
    quantity: 1,
    status: 'pending',
    totalAmount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Fetch order data if editing
  useEffect(() => {
    if (orderId) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        // In a real app, this would be an API call
        const mockOrder: Order = {
          id: orderId,
          customerId: 'customer-123',
          productId: 'product-456',
          quantity: 2,
          status: 'pending',
          totalAmount: 99.99,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setOrder(mockOrder);
        setLoading(false);
      }, 500);
    }
  }, [orderId]);

  const validateForm = useCallback((): boolean => {
    const errors: Record<string, string> = {};

    if (!order.customerId.trim()) {
      errors.customerId = 'Customer ID is required';
    }

    if (!order.productId.trim()) {
      errors.productId = 'Product ID is required';
    }

    if (order.quantity <= 0) {
      errors.quantity = 'Quantity must be greater than 0';
    }

    if (order.totalAmount < 0) {
      errors.totalAmount = 'Total amount cannot be negative';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [order]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    setOrder(prev => ({
      ...prev,
      [name]: name === 'quantity' || name === 'totalAmount'
        ? Number(value)
        : value
    }));

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSubmit(order);
      setSuccess(true);
    } catch (err) {
      setError('Failed to save order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && orderId) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        {orderId ? 'Edit Order' : 'Create New Order'}
      </h2>

      {success && (
        <div className="mb-6 p-4 bg-green-100 text-green-700 rounded-md">
          Order saved successfully!
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md" role="alert">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="customerId" className="block text-sm font-medium text-gray-700 mb-1">
            Customer ID
          </label>
          <input
            type="text"
            id="customerId"
            name="customerId"
            value={order.customerId}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
              validationErrors.customerId
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            aria-invalid={!!validationErrors.customerId}
            aria-describedby={validationErrors.customerId ? "customerId-error" : undefined}
          />
          {validationErrors.customerId && (
            <p id="customerId-error" className="mt-1 text-sm text-red-600">
              {validationErrors.customerId}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="productId" className="block text-sm font-medium text-gray-700 mb-1">
            Product ID
          </label>
          <input
            type="text"
            id="productId"
            name="productId"
            value={order.productId}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
              validationErrors.productId
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            aria-invalid={!!validationErrors.productId}
            aria-describedby={validationErrors.productId ? "productId-error" : undefined}
          />
          {validationErrors.productId && (
            <p id="productId-error" className="mt-1 text-sm text-red-600">
              {validationErrors.productId}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            min="1"
            value={order.quantity}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
              validationErrors.quantity
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            aria-invalid={!!validationErrors.quantity}
            aria-describedby={validationErrors.quantity ? "quantity-error" : undefined}
          />
          {validationErrors.quantity && (
            <p id="quantity-error" className="mt-1 text-sm text-red-600">
              {validationErrors.quantity}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="totalAmount" className="block text-sm font-medium text-gray-700 mb-1">
            Total Amount ($)
          </label>
          <input
            type="number"
            id="totalAmount"
            name="totalAmount"
            min="0"
            step="0.01"
            value={order.totalAmount}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
              validationErrors.totalAmount
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            aria-invalid={!!validationErrors.totalAmount}
            aria-describedby={validationErrors.totalAmount ? "totalAmount-error" : undefined}
          />
          {validationErrors.totalAmount && (
            <p id="totalAmount-error" className="mt-1 text-sm text-red-600">
              {validationErrors.totalAmount}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            id="status"
            name="status"
            value={order.status}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="pending">Pending</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div className="flex justify-end space-x-4 pt-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Saving...' : (orderId ? 'Update Order' : 'Create Order')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default OrderForm;