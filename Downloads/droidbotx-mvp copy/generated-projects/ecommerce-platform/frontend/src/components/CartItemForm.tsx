

interface CartItem {
  id?: string;
  productId: string;
  quantity: number;
  price: number;
}

interface CartItemFormProps {
  initialCartItem?: CartItem;
  onSubmit: (cartItem: CartItem) => Promise<void>;
  onCancel: () => void;
}

const CartItemForm: React.FC<CartItemFormProps> = ({
  initialCartItem,
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState<CartItem>({
    productId: initialCartItem?.productId || '',
    quantity: initialCartItem?.quantity || 1,
    price: initialCartItem?.price || 0
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const validate = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.productId) {
      newErrors.productId = 'Product is required';
    }

    if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be at least 1';
    }

    if (formData.price < 0) {
      newErrors.price = 'Price cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: name === 'quantity' || name === 'price' ?
        (name === 'quantity' ? parseInt(value, 10) : parseFloat(value)) :
        value
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitSuccess(false);
    setSubmitError(null);

    if (!validate()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      setSubmitSuccess(true);
    } catch (error) {
      setSubmitError(error.message || 'An error occurred while saving');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        {initialCartItem ? 'Edit Cart Item' : 'Add New Item'}
      </h2>

      {submitError && (
        <div
          className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"
          role="alert"
        >
          {submitError}
        </div>
      )}

      {submitSuccess && (
        <div
          className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded"
          role="alert"
        >
          Cart item saved successfully!
        </div>
      )}

      <form onSubmit={handleSubmit} aria-label="Cart item form">
        <div className="mb-4">
          <label
            htmlFor="productId"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Product
          </label>
          <select
            id="productId"
            name="productId"
            value={formData.productId}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md ${
              errors.productId ? 'border-red-500' : 'border-gray-300'
            }`}
            aria-invalid={!!errors.productId}
            aria-describedby={errors.productId ? "productId-error" : undefined}
          >
            <option value="">Select a product</option>
            <option value="product-1">Product 1</option>
            <option value="product-2">Product 2</option>
            <option value="product-3">Product 3</option>
          </select>
          {errors.productId && (
            <p id="productId-error" className="mt-1 text-sm text-red-600">
              {errors.productId}
            </p>
          )}
        </div>

        <div className="mb-4">
          <label
            htmlFor="quantity"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            min="1"
            value={formData.quantity}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md ${
              errors.quantity ? 'border-red-500' : 'border-gray-300'
            }`}
            aria-invalid={!!errors.quantity}
            aria-describedby={errors.quantity ? "quantity-error" : undefined}
          />
          {errors.quantity && (
            <p id="quantity-error" className="mt-1 text-sm text-red-600">
              {errors.quantity}
            </p>
          )}
        </div>

        <div className="mb-6">
          <label
            htmlFor="price"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Price
          </label>
          <input
            type="number"
            id="price"
            name="price"
            min="0"
            step="0.01"
            value={formData.price}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md ${
              errors.price ? 'border-red-500' : 'border-gray-300'
            }`}
            aria-invalid={!!errors.price}
            aria-describedby={errors.price ? "price-error" : undefined}
          />
          {errors.price && (
            <p id="price-error" className="mt-1 text-sm text-red-600">
              {errors.price}
            </p>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Saving...' : 'Save Item'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CartItemForm;