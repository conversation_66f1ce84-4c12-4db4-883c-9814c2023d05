



import { Clock, DollarSign, Package as ProductIcon, ShoppingCart as ShoppingCartIcon, ShoppingCartPlus, TrendingUp, Users as UsersIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import React from 'react';

tsx`



  ShoppingCart as ShoppingCartIcon,
  Package as ProductIcon,
  Users as UsersIcon,
  TrendingUp,
  DollarSign,
  ShoppingCartPlus,
  Clock
} from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  change?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, change }) => (;
  <div className="bg-white rounded-lg shadow p-6">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold mt-1">{value}</p>
        {change && (
          <p className={`text-sm mt-1 ${change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
            {change} from last month
          </p>
        )}
      </div>
      <div className={`p-3 rounded-full ${color}`}>
        {icon}
      </div>
    </div>
  </div>
);

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalUsers: 0,
    totalRevenue: 0,
    totalOrders: 0
  });
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, this would be API calls
    setTimeout(() => {
      setStats({
        totalProducts: 124,
        totalUsers: 342,
        totalRevenue: 24560,
        totalOrders: 89
      });
      
      setRecentActivity([;
        { id: 1, action: 'New order placed', user: 'John Doe', time: '2 minutes ago' },
        { id: 2, action: 'Product added', user: 'Admin', time: '15 minutes ago' },
        { id: 3, action: 'User registered', user: 'Jane Smith', time: '1 hour ago' },
        { id: 4, action: 'Order shipped', user: 'Admin', time: '3 hours ago' },
        { id: 5, action: 'Inventory updated', user: 'Manager', time: '5 hours ago' }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  const quickActions = [;
    { 
      title: 'Manage Products', 
      icon: <ProductIcon size={24} />, 
      color: 'bg-blue-100 text-blue-600',
      path: '/products'
    },
    { 
      title: 'View Orders', 
      icon: <ShoppingCartIcon size={24} />, 
      color: 'bg-green-100 text-green-600',
      path: '/orders'
    },
    { 
      title: 'User Management', 
      icon: <UsersIcon size={24} />, 
      color: 'bg-purple-100 text-purple-600',
      path: '/users'
    }
  ];

  if (loading) {
    return (;
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (;
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to your ecommerce platform dashboard</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          title="Total Products" 
          value={stats.totalProducts} 
          icon={<ProductIcon size={24} />}
          color="bg-blue-100 text-blue-600"
          change="+12%"
        />
        <StatCard 
          title="Total Users" 
          value={stats.totalUsers} 
          icon={<UsersIcon size={24} />}
          color="bg-green-100 text-green-600"
          change="+8%"
        />
        <StatCard 
          title="Total Revenue" 
          value={`$${stats.totalRevenue.toLocaleString()}`} 
          icon={<DollarSign size={24} />}
          color="bg-yellow-100 text-yellow-600"
          change="+15%"
        />
        <StatCard 
          title="Orders This Month" 
          value={stats.totalOrders} 
          icon={<ShoppingCartPlus size={24} />}
          color="bg-purple-100 text-purple-600"
          change="+22%"
        />
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {quickActions.map((action, index) => (
          <div 
            key={index}
            className="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => navigate(action.path)}
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-full ${action.color} mr-4`}>
                {action.icon}
              </div>
              <h3 className="text-lg font-medium text-gray-900">{action.title}</h3>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Clock className="mr-2" size={20} />
              Recent Activity
            </h2>
          </div>
          <div className="divide-y divide-gray-200">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="px-6 py-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-500">{activity.user} • {activity.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Charts Placeholder */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Sales Overview</h2>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <p className="text-gray-500">Sales chart visualization would appear here</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
`