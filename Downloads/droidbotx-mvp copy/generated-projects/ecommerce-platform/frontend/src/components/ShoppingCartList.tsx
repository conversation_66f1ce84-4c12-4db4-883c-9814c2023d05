

interface ShoppingCart {
  id: string;
  userId: string;
  items: CartItem[];
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'completed' | 'abandoned';
}

interface CartItem {
  productId: string;
  quantity: number;
  price: number;
}

const ShoppingCartList: React.FC = () => {
  // State management
  const [carts, setCarts] = useState<ShoppingCart[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [itemsPerPage] = useState<number>(10);

  // Fetch shopping carts
  useEffect(() => {
    const fetchCarts = async () => {
      try {
        setLoading(true);
        // In a real app, this would be an API call
        // const response = await fetchCartsAPI({ page: currentPage, searchTerm, status: statusFilter });
        // Mock data for demonstration
        const mockCarts: ShoppingCart[] = Array.from({ length: 25 }, (_, i) => ({
          id: `cart-${i + 1}`,
          userId: `user-${i + 1}`,
          items: [
            { productId: `product-${i + 1}`, quantity: 2, price: 29.99 },
            { productId: `product-${i + 2}`, quantity: 1, price: 49.99 }
          ],
          createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          status: ['active', 'completed', 'abandoned'][Math.floor(Math.random() * 3)] as 'active' | 'completed' | 'abandoned'
        }));

        setCarts(mockCarts);
        setTotalPages(Math.ceil(mockCarts.length / itemsPerPage));
        setError(null);
      } catch (err) {
        setError('Failed to load shopping carts');
        console.error('Error fetching carts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCarts();
  }, [currentPage, searchTerm, statusFilter, itemsPerPage]);

  // Filter and search carts
  const filteredCarts = carts.filter(cart => {
    const matchesSearch = cart.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          cart.userId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || cart.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Pagination
  const paginatedCarts = filteredCarts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // CRUD Operations
  const handleView = (id: string) => {
    console.log(`View cart ${id}`);
    // Implement view logic
  };

  const handleEdit = (id: string) => {
    console.log(`Edit cart ${id}`);
    // Implement edit logic
  };

  const handleDelete = (id: string) => {
    if (window.confirm(`Are you sure you want to delete cart ${id}?`)) {
      console.log(`Delete cart ${id}`);
      // Implement delete logic
    }
  };

  const handleCreate = () => {
    console.log('Create new cart');
    // Implement create logic
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle status filter
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
        role="alert"
        aria-live="polite"
      >
        <strong className="font-bold">Error: </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Shopping Carts</h1>
        <button
          onClick={handleCreate}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          aria-label="Create new shopping cart"
        >
          Create Cart
        </button>
      </div>

      {/* Search and Filter Controls */}
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <label htmlFor="search" className="sr-only">Search carts</label>
          <input
            id="search"
            type="text"
            placeholder="Search by cart ID or user ID..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Search shopping carts"
          />
        </div>
        <div>
          <label htmlFor="status-filter" className="sr-only">Filter by status</label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={handleStatusFilterChange}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Filter carts by status"
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="abandoned">Abandoned</option>
          </select>
        </div>
      </div>

      {/* Shopping Cart List */}
      {paginatedCarts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No shopping carts found</p>
        </div>
      ) : (
        <>
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200" role="table">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cart ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Items
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedCarts.map((cart) => (
                  <tr key={cart.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {cart.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {cart.userId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {cart.items.length} items
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${cart.status === 'active' ? 'bg-green-100 text-green-800' :
                          cart.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'}`}>
                        {cart.status.charAt(0).toUpperCase() + cart.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(cart.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleView(cart.id)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        aria-label={`View details for cart ${cart.id}`}
                      >
                        View
                      </button>
                      <button
                        onClick={() => handleEdit(cart.id)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                        aria-label={`Edit cart ${cart.id}`}
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(cart.id)}
                        className="text-red-600 hover:text-red-900"
                        aria-label={`Delete cart ${cart.id}`}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
              <span className="font-medium">
                {Math.min(currentPage * itemsPerPage, filteredCarts.length)}
              </span>{' '}
              of <span className="font-medium">{filteredCarts.length}</span> results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                aria-label="Previous page"
                aria-disabled={currentPage === 1}
              >
                Previous
              </button>
              <div className="flex space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 rounded-md ${
                      currentPage === page
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                    aria-label={`Go to page ${page}`}
                    aria-current={currentPage === page ? 'page' : undefined}
                  >
                    {page}
                  </button>
                ))}
              </div>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                aria-label="Next page"
                aria-disabled={currentPage === totalPages}
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ShoppingCartList;