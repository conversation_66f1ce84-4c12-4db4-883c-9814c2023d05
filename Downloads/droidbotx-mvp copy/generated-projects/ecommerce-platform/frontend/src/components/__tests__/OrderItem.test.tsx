




import * as orderService from '../services/orderService';

import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { OrderItemDetail } from '../components/order/OrderItemDetail.js';
import { OrderItemForm } from '../components/order/OrderItemForm.js';
import { OrderItemList } from '../components/order/OrderItemList.js';
import React from 'react';

Looking at the components you've mentioned (OrderItemList, OrderItemForm, OrderItemDetail), I'll generate React Testing Library tests for an e-commerce application. Here's a complete TypeScript test file:'









// Mock the order service
jest.mock('../services/orderService');

// Mock data
const mockOrderItems = [;
  {
    id: 1,
    orderId: 101,
    productId: 1,
    productName: 'Wireless Headphones',
    quantity: 2,
    unitPrice: 99.99,
    totalPrice: 199.98,
    status: 'pending'
  },
  {
    id: 2,
    orderId: 101,
    productId: 2,
    productName: 'Smartphone Case',
    quantity: 1,
    unitPrice: 24.99,
    totalPrice: 24.99,
    status: 'shipped'
  },
  {
    id: 3,
    orderId: 102,
    productId: 3,
    productName: 'USB Cable',
    quantity: 5,
    unitPrice: 12.50,
    totalPrice: 62.50,
    status: 'delivered'
  }
];

const mockOrderItem = {
  id: 1,
  orderId: 101,
  productId: 1,
  productName: 'Wireless Headphones',
  quantity: 2,
  unitPrice: 99.99,
  totalPrice: 199.98,
  status: 'pending'
};

describe('OrderItem Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('OrderItemList', () => {
    it('renders order items list correctly', async () => {
      (orderService.getOrderItems as jest.Mock).mockResolvedValue(mockOrderItems);

      render(<OrderItemList orderId={101} />);

      // Check loading state
      expect(screen.getByText(/loading/i)).toBeInTheDocument();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });

      // Check that items are rendered
      expect(screen.getByText('Wireless Headphones')).toBeInTheDocument();
      expect(screen.getByText('Smartphone Case')).toBeInTheDocument();
      expect(screen.getByText('$99.99')).toBeInTheDocument();
      expect(screen.getByText('$24.99')).toBeInTheDocument();
    });

    it('handles API error gracefully', async () => {
      (orderService.getOrderItems as jest.Mock).mockRejectedValue(new Error('Failed to fetch'));

      render(<OrderItemList orderId={101} />);

      await waitFor(() => {
        expect(screen.getByText(/failed to load order items/i)).toBeInTheDocument();
      });
    });

    it('displays empty state when no items', async () => {
      (orderService.getOrderItems as jest.Mock).mockResolvedValue([]);

      render(<OrderItemList orderId={999} />);

      await waitFor(() => {
        expect(screen.getByText(/no items found/i)).toBeInTheDocument();
      });
    });

    it('allows filtering by status', async () => {
      (orderService.getOrderItems as jest.Mock).mockResolvedValue(mockOrderItems);

      render(<OrderItemList orderId={101} />);

      await waitFor(() => {
        expect(screen.getByText('Wireless Headphones')).toBeInTheDocument();
      });

      // Filter by shipped status
      const filterSelect = screen.getByRole('combobox', { name: /filter by status/i });
      fireEvent.change(filterSelect, { target: { value: 'shipped' } });

      expect(screen.queryByText('Wireless Headphones')).not.toBeInTheDocument();
      expect(screen.getByText('Smartphone Case')).toBeInTheDocument();
    });

    it('handles refresh action', async () => {
      (orderService.getOrderItems as jest.Mock).mockResolvedValueOnce([])
        .mockResolvedValueOnce(mockOrderItems);

      render(<OrderItemList orderId={101} />);

      await waitFor(() => {
        expect(screen.getByText(/no items found/i)).toBeInTheDocument();
      });

      // Simulate refresh
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(screen.getByText('Wireless Headphones')).toBeInTheDocument();
      });
    });
  });

  describe('OrderItemForm', () => {
    const mockOnSubmit = jest.fn();
    const mockOnCancel = jest.fn();

    it('renders form fields correctly', () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      expect(screen.getByRole('textbox', { name: /product name/i})).toBeInTheDocument();
      expect(screen.getByRole('spinbutton', { name: /quantity/i})).toBeInTheDocument();
      expect(screen.getByRole('spinbutton', { name: /unit price/i})).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /submit/i})).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i})).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const submitButton = screen.getByRole('button', { name: /submit/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/product name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/quantity is required/i)).toBeInTheDocument();
        expect(screen.getByText(/unit price is required/i)).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('validates quantity and price are positive numbers', async () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      const priceInput = screen.getByRole('spinbutton', { name: /unit price/i });

      fireEvent.change(quantityInput, { target: { value: '-1' } });
      fireEvent.change(priceInput, { target: { value: '-50' } });

      const submitButton = screen.getByRole('button', { name: /submit/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/quantity must be greater than 0/i)).toBeInTheDocument();
        expect(screen.getByText(/unit price must be greater than 0/i)).toBeInTheDocument();
      });
    });

    it('submits form with valid data', async () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const productNameInput = screen.getByRole('textbox', { name: /product name/i });
      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      const priceInput = screen.getByRole('spinbutton', { name: /unit price/i });

      fireEvent.change(productNameInput, { target: { value: 'Bluetooth Speaker' } });
      fireEvent.change(quantityInput, { target: { value: '3' } });
      fireEvent.change(priceInput, { target: { value: '79.99' } });

      const submitButton = screen.getByRole('button', { name: /submit/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          productName: 'Bluetooth Speaker',
          quantity: 3,
          unitPrice: 79.99,
          totalPrice: 239.97
        });
      });
    });

    it('handles API submission error', async () => {
      (orderService.createOrderItem as jest.Mock).mockRejectedValueOnce({
        response: { data: { message: 'Product not found' } }
      });

      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const productNameInput = screen.getByRole('textbox', { name: /product name/i });
      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      const priceInput = screen.getByRole('spinbutton', { name: /unit price/i });

      fireEvent.change(productNameInput, { target: { value: 'Invalid Product' } });
      fireEvent.change(quantityInput, { target: { value: '1' } });
      fireEvent.change(priceInput, { target: { value: '29.99' } });

      const submitButton = screen.getByRole('button', { name: /submit/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/product not found/i)).toBeInTheDocument();
      });
    });

    it('calculates total price automatically', async () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      const priceInput = screen.getByRole('spinbutton', { name: /unit price/i });

      fireEvent.change(quantityInput, { target: { value: '2' } });
      fireEvent.change(priceInput, { target: { value: '49.99' } });

      const totalPriceDisplay = screen.getByText(/total: \$99.98/i);
      expect(totalPriceDisplay).toBeInTheDocument();
    });

    it('calls onCancel when cancel button is clicked', () => {
      render(<OrderItemForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('OrderItemDetail', () => {
    it('renders order item details correctly', () => {
      render(<OrderItemDetail orderItem={mockOrderItem} />);

      expect(screen.getByText('Wireless Headphones')).toBeInTheDocument();
      expect(screen.getByText('Quantity: 2')).toBeInTheDocument();
      expect(screen.getByText('$99.99')).toBeInTheDocument();
      expect(screen.getByText('$199.98')).toBeInTheDocument();
      expect(screen.getByText('Status: pending')).toBeInTheDocument();
    });

    it('displays loading state when order item is null', () => {
      render(<OrderItemDetail orderItem={null} />);

      expect(screen.getByText(/loading order item details/i)).toBeInTheDocument();
    });

    it('handles update action', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({ ...mockOrderItem, status: 'shipped' });
      (orderService.updateOrderItem as jest.Mock).mockImplementation(mockUpdate);

      render(<OrderItemDetail orderItem={mockOrderItem} />);

      const updateButton = screen.getByRole('button', { name: /update status/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(mockUpdate).toHaveBeenCalledWith(1, { status: 'shipped' });
      });
    });

    it('handles delete action with confirmation', async () => {
      const mockDelete = jest.fn().mockResolvedValue({});
      (orderService.deleteOrderItem as jest.Mock).mockImplementation(mockDelete);

      render(<OrderItemDetail orderItem={mockOrderItem} />);

      // Click delete button
      const deleteButton = screen.getByRole('button', { name: /delete item/i });
      fireEvent.click(deleteButton);

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm delete/i });
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockDelete).toHaveBeenCalledWith(1);
      });
    });

    it('shows error message on API failure', async () => {
      const mockUpdate = jest.fn().mockRejectedValue(new Error('Update failed'));
      (orderService.updateOrderItem as jest.Mock).mockImplementation(mockUpdate);

      render(<OrderItemDetail orderItem={mockOrderItem} />);

      const updateButton = screen.getByRole('button', { name: /update status/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to update order item/i)).toBeInTheDocument();
      });
    });

    it('allows quantity adjustment for pending items', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({ ...mockOrderItem, quantity: 5 });
      (orderService.updateOrderItem as jest.Mock).mockImplementation(mockUpdate);

      render(<OrderItemDetail orderItem={mockOrderItem} />);

      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      fireEvent.change(quantityInput, { target: { value: '5' } });

      const saveButton = screen.getByRole('button', { name: /save quantity/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockUpdate).toHaveBeenCalledWith(1, { quantity: 5 });
      });
    });

    it('disables quantity adjustment for shipped items', () => {
      const shippedItem = { ...mockOrderItem, status: 'shipped' };
      render(<OrderItemDetail orderItem={shippedItem} />);

      const quantityInput = screen.getByRole('spinbutton', { name: /quantity/i });
      expect(quantityInput).toBeDisabled();

      const saveButton = screen.queryByRole('button', { name: /save quantity/i });
      expect(saveButton).not.toBeInTheDocument();
    });
  });
});
`

This test file includes:

1. **Component rendering tests** - Verifying that all components render their expected content
2. **User interactions** - Testing form submissions, button clicks, and input changes
3. **Form validation** - Checking required fields and numeric validation rules
4. **API calls** - Mocking service calls and verifying they're made with correct parameters'
5. **Error states** - Testing how components handle API failures and validation errors
6. **Loading