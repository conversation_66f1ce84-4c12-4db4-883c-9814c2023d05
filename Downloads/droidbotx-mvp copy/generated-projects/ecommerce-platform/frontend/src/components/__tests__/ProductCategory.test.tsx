



import * as categoryApi from '../../api/categoryApi';




import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import React from 'react';
import ProductCategoryDetail from '../components/ProductCategoryDetail.js';
import ProductCategoryForm from '../components/ProductCategoryForm.js';
import ProductCategoryList from '../components/ProductCategoryList.js';

```typescript
// ProductCategory.test.tsx








// Mock API calls
jest.mock('../../api/categoryApi');
const mockedCategoryApi = categoryApi as jest.Mocked<typeof categoryApi>;

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({ id: '1' })
}));

// Mock data
const mockCategories = [
  { id: 1, name: 'Electronics', description: 'Electronic devices', isActive: true },
  { id: 2, name: 'Clothing', description: 'Apparel and accessories', isActive: true },
  { id: 3, name: 'Books', description: 'Educational materials', isActive: false }
];

const mockCategory = {
  id: 1,
  name: 'Electronics',
  description: 'Electronic devices',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z'
};

describe('ProductCategory Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ProductCategoryList', () => {
    it('renders loading state initially', () => {
      mockedCategoryApi.getCategories.mockResolvedValueOnce(new Promise(() => {}));
      render(<ProductCategoryList />, { wrapper: BrowserRouter });
      
      expect(screen.getByText(/loading categories/i)).toBeInTheDocument();
    });

    it('renders categories list after successful API call', async () => {
      mockedCategoryApi.getCategories.mockResolvedValueOnce(mockCategories);
      render(<ProductCategoryList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
        expect(screen.getByText('Clothing')).toBeInTheDocument();
        expect(screen.getByText('Books')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      mockedCategoryApi.getCategories.mockRejectedValueOnce(new Error('API Error'));
      render(<ProductCategoryList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load categories/i)).toBeInTheDocument();
      });
    });

    it('allows user to delete a category', async () => {
      mockedCategoryApi.getCategories.mockResolvedValueOnce(mockCategories);
      mockedCategoryApi.deleteCategory.mockResolvedValueOnce();
      
      render(<ProductCategoryList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
      });
      
      const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
      fireEvent.click(deleteButton);
      
      expect(screen.getByText(/are you sure/i)).toBeInTheDocument();
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(mockedCategoryApi.deleteCategory).toHaveBeenCalledWith(1);
      });
    });

    it('handles delete error', async () => {
      mockedCategoryApi.getCategories.mockResolvedValueOnce(mockCategories);
      mockedCategoryApi.deleteCategory.mockRejectedValueOnce(new Error('Delete failed'));
      
      render(<ProductCategoryList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
      });
      
      const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
      fireEvent.click(deleteButton);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to delete category/i)).toBeInTheDocument();
      });
    });
  });

  describe('ProductCategoryForm', () => {
    it('renders form with empty fields for new category', () => {
      render(<ProductCategoryForm />, { wrapper: BrowserRouter });
      
      expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/active/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
    });

    it('renders form with existing category data for edit', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      
      render(<ProductCategoryForm categoryId={1} />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByLabelText(/name/i)).toHaveValue('Electronics');
        expect(screen.getByLabelText(/description/i)).toHaveValue('Electronic devices');
        expect(screen.getByLabelText(/active/i)).toBeChecked();
      });
    });

    it('validates required fields', async () => {
      render(<ProductCategoryForm />, { wrapper: BrowserRouter });
      
      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      });
    });

    it('submits form for new category', async () => {
      mockedCategoryApi.createCategory.mockResolvedValueOnce(mockCategory);
      
      render(<ProductCategoryForm />, { wrapper: BrowserRouter });
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'New Category' }
      });
      
      fireEvent.change(screen.getByLabelText(/description/i), {
        target: { value: 'New category description' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /save/i }));
      
      await waitFor(() => {
        expect(mockedCategoryApi.createCategory).toHaveBeenCalledWith({
          name: 'New Category',
          description: 'New category description',
          isActive: true
        });
        expect(mockNavigate).toHaveBeenCalledWith('/categories');
      });
    });

    it('submits form for updating existing category', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      mockedCategoryApi.updateCategory.mockResolvedValueOnce({
        ...mockCategory,
        name: 'Updated Electronics'
      });
      
      render(<ProductCategoryForm categoryId={1} />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByLabelText(/name/i)).toHaveValue('Electronics');
      });
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'Updated Electronics' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /save/i }));
      
      await waitFor(() => {
        expect(mockedCategoryApi.updateCategory).toHaveBeenCalledWith(1, {
          name: 'Updated Electronics',
          description: 'Electronic devices',
          isActive: true
        });
        expect(mockNavigate).toHaveBeenCalledWith('/categories');
      });
    });

    it('handles form submission error', async () => {
      mockedCategoryApi.createCategory.mockRejectedValueOnce(new Error('Submission failed'));
      
      render(<ProductCategoryForm />, { wrapper: BrowserRouter });
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'New Category' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /save/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/failed to save category/i)).toBeInTheDocument();
      });
    });

    it('cancels form and navigates back', async () => {
      render(<ProductCategoryForm />, { wrapper: BrowserRouter });
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/categories');
    });
  });

  describe('ProductCategoryDetail', () => {
    it('renders loading state initially', () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(new Promise(() => {}));
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      expect(screen.getByText(/loading category details/i)).toBeInTheDocument();
    });

    it('renders category details after successful API call', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
        expect(screen.getByText('Electronic devices')).toBeInTheDocument();
        expect(screen.getByText('Active')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      mockedCategoryApi.getCategory.mockRejectedValueOnce(new Error('API Error'));
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load category/i)).toBeInTheDocument();
      });
    });

    it('navigates to edit form when edit button is clicked', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
      });
      
      const editButton = screen.getByRole('button', { name: /edit/i });
      fireEvent.click(editButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/categories/edit/1');
    });

    it('deletes category and navigates back to list', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      mockedCategoryApi.deleteCategory.mockResolvedValueOnce();
      
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
      });
      
      const deleteButton = screen.getByRole('button', { name: /delete/i });
      fireEvent.click(deleteButton);
      
      expect(screen.getByText(/are you sure/i)).toBeInTheDocument();
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(mockedCategoryApi.deleteCategory).toHaveBeenCalledWith(1);
        expect(mockNavigate).toHaveBeenCalledWith('/categories');
      });
    });

    it('handles delete error in detail view', async () => {
      mockedCategoryApi.getCategory.mockResolvedValueOnce(mockCategory);
      mockedCategoryApi.deleteCategory.mockRejectedValueOnce(new Error('Delete failed'));
      
      render(<ProductCategoryDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('Electronics')).toBeInTheDocument();
      });
      
      const deleteButton = screen.getByRole('button', { name: /delete/i });
      fireEvent.click(deleteButton);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to delete category/i)).toBeInTheDocument();
      });
    });
  });
});
```