


import * as cartApi from '../api/cartApi';





import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Cart, CartItem } from '../types/cart.js';
import { ShoppingCartDetail } from './ShoppingCartDetail.js';
import { ShoppingCartForm } from './ShoppingCartForm.js';
import { ShoppingCartList } from './ShoppingCartList.js';
import React from 'react';

```typescript
// ShoppingCart.test.tsx








// Mock API functions
jest.mock('../api/cartApi');

// Mock data
const mockCartItems: CartItem[] = [
  {
    id: '1',
    productId: 'p1',
    productName: 'Laptop',
    price: 999.99,
    quantity: 2,
    image: '/images/laptop.jpg',
    stock: 10
  },
  {
    id: '2',
    productId: 'p2',
    productName: 'Mouse',
    price: 29.99,
    quantity: 1,
    image: '/images/mouse.jpg',
    stock: 50
  }
];

const mockCart: Cart = {
  id: 'cart1',
  items: mockCartItems,
  total: 2029.97,
  itemCount: 3
};

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('ShoppingCart Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ShoppingCartList', () => {
    it('renders cart items correctly', async () => {
      (cartApi.getCartItems as jest.Mock).mockResolvedValue(mockCartItems);

      renderWithRouter(<ShoppingCartList />);

      expect(screen.getByText('Loading cart...')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
        expect(screen.getByText('Mouse')).toBeInTheDocument();
        expect(screen.getByText('$999.99')).toBeInTheDocument();
        expect(screen.getByText('$29.99')).toBeInTheDocument();
      });
    });

    it('handles empty cart state', async () => {
      (cartApi.getCartItems as jest.Mock).mockResolvedValue([]);

      renderWithRouter(<ShoppingCartList />);

      await waitFor(() => {
        expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
        expect(screen.getByText('Continue Shopping')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      (cartApi.getCartItems as jest.Mock).mockRejectedValue(new Error('Failed to load cart'));

      renderWithRouter(<ShoppingCartList />);

      await waitFor(() => {
        expect(screen.getByText('Failed to load cart items')).toBeInTheDocument();
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
    });

    it('allows removing items from cart', async () => {
      (cartApi.getCartItems as jest.Mock).mockResolvedValue(mockCartItems);
      (cartApi.removeItemFromCart as jest.Mock).mockResolvedValue({ success: true });

      renderWithRouter(<ShoppingCartList />);

      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });

      const removeButtons = screen.getAllByRole('button', { name: /remove/i });
      fireEvent.click(removeButtons[0]);

      await waitFor(() => {
        expect(cartApi.removeItemFromCart).toHaveBeenCalledWith('1');
      });
    });

    it('updates item quantity', async () => {
      (cartApi.getCartItems as jest.Mock).mockResolvedValue(mockCartItems);
      (cartApi.updateItemQuantity as jest.Mock).mockResolvedValue({ success: true });

      renderWithRouter(<ShoppingCartList />);

      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });

      const quantityInput = screen.getByDisplayValue('2');
      fireEvent.change(quantityInput, { target: { value: '3' } });

      await waitFor(() => {
        expect(cartApi.updateItemQuantity).toHaveBeenCalledWith('1', 3);
      });
    });
  });

  describe('ShoppingCartForm', () => {
    const mockOnSubmit = jest.fn();

    beforeEach(() => {
      mockOnSubmit.mockClear();
    });

    it('renders form fields correctly', () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);

      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/city/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/postal code/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/country/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/payment method/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /place order/i })).toBeInTheDocument();
    });

    it('shows validation errors for required fields', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);

      const submitButton = screen.getByRole('button', { name: /place order/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/address is required/i)).toBeInTheDocument();
        expect(screen.getByText(/city is required/i)).toBeInTheDocument();
        expect(screen.getByText(/postal code is required/i)).toBeInTheDocument();
        expect(screen.getByText(/country is required/i)).toBeInTheDocument();
        expect(screen.getByText(/payment method is required/i)).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('validates email format', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);

      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });

      const submitButton = screen.getByRole('button', { name: /place order/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/enter a valid email/i)).toBeInTheDocument();
      });
    });

    it('submits form with valid data', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);

      // Fill in all required fields
      fireEvent.change(screen.getByLabelText(/first name/i), { target: { value: 'John' } });
      fireEvent.change(screen.getByLabelText(/last name/i), { target: { value: 'Doe' } });
      fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/address/i), { target: { value: '123 Main St' } });
      fireEvent.change(screen.getByLabelText(/city/i), { target: { value: 'New York' } });
      fireEvent.change(screen.getByLabelText(/postal code/i), { target: { value: '10001' } });
      fireEvent.change(screen.getByLabelText(/country/i), { target: { value: 'USA' } });
      fireEvent.change(screen.getByLabelText(/payment method/i), { target: { value: 'credit' } });

      const submitButton = screen.getByRole('button', { name: /place order/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          address: '123 Main St',
          city: 'New York',
          postalCode: '10001',
          country: 'USA',
          paymentMethod: 'credit'
        });
      });
    });

    it('shows loading state during submission', () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={true} />);

      expect(screen.getByRole('button', { name: /placing order/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /placing order/i })).toBeDisabled();
    });
  });

  describe('ShoppingCartDetail', () => {
    it('renders cart details correctly', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue(mockCart);

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
        expect(screen.getByText('3 items')).toBeInTheDocument();
        expect(screen.getByText('$2,029.97')).toBeInTheDocument();
      });

      // Check if items are rendered
      expect(screen.getByText('Laptop')).toBeInTheDocument();
      expect(screen.getByText('Mouse')).toBeInTheDocument();
    });

    it('shows empty cart message', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue({ ...mockCart, items: [], total: 0, itemCount: 0 });

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
      });
    });

    it('handles API error in cart detail', async () => {
      (cartApi.getCart as jest.Mock).mockRejectedValue(new Error('Failed to load cart'));

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Failed to load cart')).toBeInTheDocument();
      });
    });

    it('navigates to checkout when button is clicked', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue(mockCart);

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Proceed to Checkout')).toBeInTheDocument();
      });

      const checkoutButton = screen.getByRole('button', { name: /proceed to checkout/i });
      fireEvent.click(checkoutButton);

      // In a real test, we would check navigation, but here we verify the button exists
      expect(checkoutButton).toBeInTheDocument();
    });

    it('calculates and displays correct totals', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue(mockCart);

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        const totalElement = screen.getByText('$2,029.97');
        expect(totalElement).toBeInTheDocument();
        
        // Check individual item prices
        expect(screen.getByText('$999.99')).toBeInTheDocument();
        expect(screen.getByText('$29.99')).toBeInTheDocument();
        
        // Check quantities
        expect(screen.getByText('2')).toBeInTheDocument();
        expect(screen.getByText('1')).toBeInTheDocument();
      });
    });

    it('allows updating item quantities in cart detail', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue(mockCart);
      (cartApi.updateItemQuantity as jest.Mock).mockResolvedValue({ success: true });

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });

      // Find the quantity input for the first item
      const laptopItem = screen.getByText('Laptop').closest('.cart-item');
      if (laptopItem) {
        const quantityInput = within(laptopItem as HTMLElement).getByDisplayValue('2');
        fireEvent.change(quantityInput, { target: { value: '1' } });

        await waitFor(() => {
          expect(cartApi.updateItemQuantity).toHaveBeenCalledWith('1', 1);
        });
      }
    });

    it('allows removing items in cart detail', async () => {
      (cartApi.getCart as jest.Mock).mockResolvedValue(mockCart);
      (cartApi.removeItemFromCart as jest.Mock).mockResolvedValue({ success: true });

      renderWithRouter(<ShoppingCartDetail />);

      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });

      // Find the remove button for the first item
      const laptopItem = screen.getByText('Laptop').closest('.cart-item');
      if (laptopItem) {
        const removeButton = within(laptopItem as HTMLElement).getByRole('button', { name: /remove/i });
        fireEvent.click(removeButton);

        await waitFor(() => {
          expect(cartApi.removeItemFromCart).toHaveBeenCalledWith('1');
        });
      }
    });
  });
});
```