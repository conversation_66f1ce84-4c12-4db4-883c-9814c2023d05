


import * as cartApi from '../api/cartApi';



import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Cart, CartItem } from '../types/cart.js';
import { ShoppingCartDetail, ShoppingCartForm, ShoppingCartList } from './ShoppingCart.js';
import React from 'react';

```typescript
// ShoppingCart.test.tsx






// Mock the API calls
jest.mock('../api/cartApi');
const mockedCartApi = cartApi as jest.Mocked<typeof cartApi>;

// Mock cart data
const mockCartItems: CartItem[] = [
  {
    id: '1',
    productId: 'prod-1',
    productName: 'Laptop',
    price: 999.99,
    quantity: 2,
    image: '/images/laptop.jpg',
    stock: 10
  },
  {
    id: '2',
    productId: 'prod-2',
    productName: 'Mouse',
    price: 29.99,
    quantity: 1,
    image: '/images/mouse.jpg',
    stock: 50
  }
];

const mockCart: Cart = {
  id: 'cart-1',
  items: mockCartItems,
  totalItems: 3,
  totalPrice: 1029.98,
  currency: 'USD'
};

// Wrapper component for routing
const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('ShoppingCart Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ShoppingCartList', () => {
    it('renders cart items correctly', async () => {
      mockedCartApi.getCartItems.mockResolvedValue(mockCartItems);
      
      renderWithRouter(<ShoppingCartList />);
      
      // Check loading state
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
      
      // Check items are rendered
      expect(screen.getByText('Laptop')).toBeInTheDocument();
      expect(screen.getByText('Mouse')).toBeInTheDocument();
      expect(screen.getByText('$999.99')).toBeInTheDocument();
      expect(screen.getByText('$29.99')).toBeInTheDocument();
    });

    it('handles empty cart state', async () => {
      mockedCartApi.getCartItems.mockResolvedValue([]);
      
      renderWithRouter(<ShoppingCartList />);
      
      await waitFor(() => {
        expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      mockedCartApi.getCartItems.mockRejectedValue(new Error('API Error'));
      
      renderWithRouter(<ShoppingCartList />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load cart/i)).toBeInTheDocument();
      });
    });

    it('allows removing items from cart', async () => {
      mockedCartApi.getCartItems.mockResolvedValue(mockCartItems);
      mockedCartApi.removeItemFromCart.mockResolvedValue({ ...mockCart, items: [mockCartItems[1]] });
      
      renderWithRouter(<ShoppingCartList />);
      
      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });
      
      const removeButtons = screen.getAllByRole('button', { name: /remove/i });
      fireEvent.click(removeButtons[0]);
      
      await waitFor(() => {
        expect(mockedCartApi.removeItemFromCart).toHaveBeenCalledWith('1');
        expect(screen.queryByText('Laptop')).not.toBeInTheDocument();
      });
    });

    it('updates item quantities', async () => {
      mockedCartApi.getCartItems.mockResolvedValue(mockCartItems);
      mockedCartApi.updateItemQuantity.mockResolvedValue({
        ...mockCart,
        items: [{ ...mockCartItems[0], quantity: 3 }, mockCartItems[1]]
      });
      
      renderWithRouter(<ShoppingCartList />);
      
      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });
      
      const quantityInput = screen.getByDisplayValue('2');
      fireEvent.change(quantityInput, { target: { value: '3' } });
      fireEvent.blur(quantityInput);
      
      await waitFor(() => {
        expect(mockedCartApi.updateItemQuantity).toHaveBeenCalledWith('1', 3);
      });
    });
  });

  describe('ShoppingCartForm', () => {
    const mockOnSubmit = jest.fn();
    
    beforeEach(() => {
      mockOnSubmit.mockClear();
    });

    it('renders form fields correctly', () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);
      
      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/city/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/postal code/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/country/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /checkout/i })).toBeInTheDocument();
    });

    it('shows validation errors for required fields', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);
      
      const submitButton = screen.getByRole('button', { name: /checkout/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/address is required/i)).toBeInTheDocument();
        expect(screen.getByText(/city is required/i)).toBeInTheDocument();
        expect(screen.getByText(/postal code is required/i)).toBeInTheDocument();
        expect(screen.getByText(/country is required/i)).toBeInTheDocument();
      });
      
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('validates email format', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);
      
      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      
      const submitButton = screen.getByRole('button', { name: /checkout/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/enter a valid email/i)).toBeInTheDocument();
      });
    });

    it('submits form with valid data', async () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={false} />);
      
      // Fill in form
      fireEvent.change(screen.getByLabelText(/first name/i), { target: { value: 'John' } });
      fireEvent.change(screen.getByLabelText(/last name/i), { target: { value: 'Doe' } });
      fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/address/i), { target: { value: '123 Main St' } });
      fireEvent.change(screen.getByLabelText(/city/i), { target: { value: 'New York' } });
      fireEvent.change(screen.getByLabelText(/postal code/i), { target: { value: '10001' } });
      fireEvent.change(screen.getByLabelText(/country/i), { target: { value: 'USA' } });
      
      const submitButton = screen.getByRole('button', { name: /checkout/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          address: '123 Main St',
          city: 'New York',
          postalCode: '10001',
          country: 'USA'
        });
      });
    });

    it('shows loading state during submission', () => {
      render(<ShoppingCartForm onSubmit={mockOnSubmit} loading={true} />);
      
      expect(screen.getByRole('button', { name: /processing/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /processing/i })).toBeDisabled();
    });
  });

  describe('ShoppingCartDetail', () => {
    it('renders cart summary correctly', async () => {
      mockedCartApi.getCart.mockResolvedValue(mockCart);
      
      renderWithRouter(<ShoppingCartDetail />);
      
      await waitFor(() => {
        expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
      });
      
      // Check item details
      expect(screen.getByText('Laptop')).toBeInTheDocument();
      expect(screen.getByText('Mouse')).toBeInTheDocument();
      
      // Check summary
      expect(screen.getByText('Subtotal')).toBeInTheDocument();
      expect(screen.getByText('$1,029.98')).toBeInTheDocument();
      expect(screen.getByText('3 items')).toBeInTheDocument();
    });

    it('shows empty cart message', async () => {
      mockedCartApi.getCart.mockResolvedValue({ ...mockCart, items: [], totalItems: 0, totalPrice: 0 });
      
      renderWithRouter(<ShoppingCartDetail />);
      
      await waitFor(() => {
        expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
      });
    });

    it('handles API error in detail view', async () => {
      mockedCartApi.getCart.mockRejectedValue(new Error('Failed to load cart'));
      
      renderWithRouter(<ShoppingCartDetail />);
      
      await waitFor(() => {
        expect(screen.getByText(/error loading cart/i)).toBeInTheDocument();
      });
    });

    it('navigates to checkout when button is clicked', async () => {
      mockedCartApi.getCart.mockResolvedValue(mockCart);
      
      renderWithRouter(<ShoppingCartDetail />);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /proceed to checkout/i })).toBeInTheDocument();
      });
      
      const checkoutButton = screen.getByRole('button', { name: /proceed to checkout/i });
      fireEvent.click(checkoutButton);
      
      // In a real test, we would check navigation, but here we just ensure the button works
      expect(checkoutButton).toBeInTheDocument();
    });

    it('updates item quantities in detail view', async () => {
      mockedCartApi.getCart.mockResolvedValue(mockCart);
      mockedCartApi.updateItemQuantity.mockResolvedValue({
        ...mockCart,
        items: [{ ...mockCartItems[0], quantity: 5 }, mockCartItems[1]]
      });
      
      renderWithRouter(<ShoppingCartDetail />);
      
      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });
      
      // Find the quantity input within the specific item row
      const laptopItem = screen.getByText('Laptop').closest('.cart-item');
      const quantityInput = within(laptopItem!).getByDisplayValue('2');
      
      fireEvent.change(quantityInput, { target: { value: '5' } });
      fireEvent.blur(quantityInput);
      
      await waitFor(() => {
        expect(mockedCartApi.updateItemQuantity).toHaveBeenCalledWith('1', 5);
      });
    });
  });
});
```