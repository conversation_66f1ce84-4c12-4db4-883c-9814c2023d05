






import * as orderService from '../services/orderService';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import React from 'react';
import OrderDetail from '../components/OrderDetail.js';
import OrderForm from '../components/OrderForm.js';
import OrderList from '../components/OrderList.js';

Here's a comprehensive test suite for Order components using React Testing Library:'

tsx`
// Order.test.tsx








// Mock the order service
jest.mock('../services/orderService');

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock useAuth
jest.mock('../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 1, role: 'customer' },
  }),
}));

// Test data
const mockOrders = [;
  {
    id: 1,
    customerId: 1,
    status: 'pending',
    totalAmount: 100.0,
    createdAt: '2023-01-01T00:00:00Z',
    items: [
      { id: 1, productId: 1, quantity: 2, price: 50.0 },
    ],
  },
  {
    id: 2,
    customerId: 1,
    status: 'completed',
    totalAmount: 250.0,
    createdAt: '2023-01-02T00:00:00Z',
    items: [
      { id: 2, productId: 2, quantity: 1, price: 250.0 },
    ],
  },
];

const mockOrder = {
  id: 1,
  customerId: 1,
  status: 'pending',
  totalAmount: 100.0,
  createdAt: '2023-01-01T00:00:00Z',
  items: [
    { id: 1, productId: 1, quantity: 2, price: 50.0 },
  ],
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(;
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Order Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
  });

  describe('OrderList', () => {
    test('renders loading state initially', () => {
      (orderService.getOrders as jest.Mock).mockResolvedValueOnce([]);
      renderWithProviders(<OrderList />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    test('renders orders list', async () => {
      (orderService.getOrders as jest.Mock).mockResolvedValueOnce(mockOrders);
      renderWithProviders(<OrderList />);
      
      await waitFor(() => {
        expect(screen.getByText('Order #1')).toBeInTheDocument();
        expect(screen.getByText('Order #2')).toBeInTheDocument();
      });
    });

    test('handles API error', async () => {
      (orderService.getOrders as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
      renderWithProviders(<OrderList />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load orders/i)).toBeInTheDocument();
      });
    });

    test('shows empty state when no orders', async () => {
      (orderService.getOrders as jest.Mock).mockResolvedValueOnce([]);
      renderWithProviders(<OrderList />);
      
      await waitFor(() => {
        expect(screen.getByText(/no orders found/i)).toBeInTheDocument();
      });
    });

    test('navigates to order detail on click', async () => {
      (orderService.getOrders as jest.Mock).mockResolvedValueOnce(mockOrders);
      renderWithProviders(<OrderList />);
      
      await waitFor(async () => {
        const orderItem = screen.getByText('Order #1');
        fireEvent.click(orderItem);
        expect(mockNavigate).toHaveBeenCalledWith('/orders/1');
      });
    });
  });

  describe('OrderDetail', () => {
    test('renders loading state initially', () => {
      (orderService.getOrder as jest.Mock).mockResolvedValueOnce(null);
      renderWithProviders(<OrderDetail orderId={1} />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    test('renders order details', async () => {
      (orderService.getOrder as jest.Mock).mockResolvedValueOnce(mockOrder);
      renderWithProviders(<OrderDetail orderId={1} />);
      
      await waitFor(() => {
        expect(screen.getByText('Order #1')).toBeInTheDocument();
        expect(screen.getByText('$100.00')).toBeInTheDocument();
        expect(screen.getByText('2 x Product #1')).toBeInTheDocument();
      });
    });

    test('handles API error', async () => {
      (orderService.getOrder as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
      renderWithProviders(<OrderDetail orderId={1} />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load order/i)).toBeInTheDocument();
      });
    });

    test('shows not found when order does not exist', async () => {
      (orderService.getOrder as jest.Mock).mockResolvedValueOnce(null);
      renderWithProviders(<OrderDetail orderId={999} />);
      
      await waitFor(() => {
        expect(screen.getByText(/order not found/i)).toBeInTheDocument();
      });
    });

    test('allows status update for admin users', async () => {
      // Mock admin user
      jest.mock('../hooks/useAuth', () => ({
        useAuth: () => ({
          user: { id: 1, role: 'admin' },
        }),
      }));
      
      (orderService.getOrder as jest.Mock).mockResolvedValueOnce(mockOrder);
      (orderService.updateOrderStatus as jest.Mock).mockResolvedValueOnce({
        ...mockOrder,
        status: 'shipped',
      });
      
      renderWithProviders(<OrderDetail orderId={1} />);
      
      await waitFor(async () => {
        const shipButton = screen.getByRole('button', { name: /ship order/i });
        fireEvent.click(shipButton);
        
        await waitFor(() => {
          expect(orderService.updateOrderStatus).toHaveBeenCalledWith(1, 'shipped');
          expect(screen.getByText('shipped')).toBeInTheDocument();
        });
      });
    });
  });

  describe('OrderForm', () => {
    const mockOnSubmit = jest.fn();

    beforeEach(() => {
      mockOnSubmit.mockClear();
    });

    test('renders form fields', () => {
      renderWithProviders(<OrderForm onSubmit={mockOnSubmit} />);
      
      expect(screen.getByLabelText(/product/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/quantity/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
    });

    test('shows validation errors for empty fields', async () => {
      renderWithProviders(<OrderForm onSubmit={mockOnSubmit} />);
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/product is required/i)).toBeInTheDocument();
        expect(screen.getByText(/quantity is required/i)).toBeInTheDocument();
      });
    });

    test('submits form with valid data', async () => {
      (orderService.createOrder as jest.Mock).mockResolvedValueOnce(mockOrder);
      renderWithProviders(<OrderForm onSubmit={mockOnSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/product/i), {
        target: { value: '1' },
      });
      fireEvent.change(screen.getByLabelText(/quantity/i), {
        target: { value: '2' },
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(orderService.createOrder).toHaveBeenCalledWith({
          productId: 1,
          quantity: 2,
        });
        expect(mockOnSubmit).toHaveBeenCalledWith(mockOrder);
      });
    });

    test('shows API error message', async () => {
      (orderService.createOrder as jest.Mock).mockRejectedValueOnce(
        new Error('Insufficient inventory')
      );
      
      renderWithProviders(<OrderForm onSubmit={mockOnSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/product/i), {
        target: { value: '1' },
      });
      fireEvent.change(screen.getByLabelText(/quantity/i), {
        target: { value: '100' },
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/insufficient inventory/i)).toBeInTheDocument();
      });
    });

    test('displays loading state during submission', async () => {
      (orderService.createOrder as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockOrder), 100))
      );
      
      renderWithProviders(<OrderForm onSubmit={mockOnSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/product/i), {
        target: { value: '1' },
      });
      fireEvent.change(screen.getByLabelText(/quantity/i), {
        target: { value: '2' },
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      expect(screen.getByRole('button', { name: /submitting/i })).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
      });
    });
  });
});
`

This test suite covers:

1. **Component Rendering**:
   - Loading states for all components
   - Proper display of order data
   - Empty states and error messages

2. **User Interactions**:
   - Navigation between components
   - Form submissions
   - Status updates
   - Clicking order items

3. **Form Validation**:
   - Required field validation
   - Error message display
   - Submission prevention with invalid data

4. **API Calls**:
   - Mocked service responses
   - Success and error scenarios
   - Loading states during API calls

5. **Error States**:
   - API error handling
   - Order not found scenarios
   - Validation errors

6. **Loading States**:
   - Initial loading indicators
   - Submission loading states
   - Data fetching states

Key features of the implementation:

- Uses React Testing Library best practices
- Mocks all external dependencies (API calls, navigation)
- Tests both happy paths and error scenarios
- Comprehensive coverage of user interactions
- Proper async/await handling for React Query
- Role-based testing (admin/customer)
- Form validation testing
- Loading and error state verification

The tests ensure that:
- Components render correctly under different states
- User interactions trigger appropriate actions
- Form validation works as expected
- API errors are properly handled
- Loading states provide good UX
- Business logic is correctly implemented