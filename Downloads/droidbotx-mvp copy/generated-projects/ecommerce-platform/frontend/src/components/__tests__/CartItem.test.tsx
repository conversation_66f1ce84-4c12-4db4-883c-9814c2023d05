

import * as api from '../api/cartApi';



import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { CartItem } from '../types.js';
import { CartItemDetail, CartItemForm, CartItemList } from './CartItem.js';
import React from 'react';

Here's a comprehensive test suite for the CartItem components using React Testing Library:'

tsx`
// CartItem.test.tsx





// Mock API functions
jest.mock('../api/cartApi', () => ({
  fetchCartItems: jest.fn(),
  updateCartItem: jest.fn(),
  deleteCartItem: jest.fn(),
  fetchCartItem: jest.fn()
}));

const mockCartItems: CartItem[] = [;
  { id: 1, name: 'Product 1', price: 100, quantity: 2, productId: 101 },
  { id: 2, name: 'Product 2', price: 200, quantity: 1, productId: 102 }
];

const mockCartItem: CartItem = {
  id: 1,
  name: 'Product 1',
  price: 100,
  quantity: 2,
  productId: 101
};

describe('CartItemList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    (api.fetchCartItems as jest.Mock).mockResolvedValueOnce([]);
    render(<CartItemList />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('renders cart items after loading', async () => {
    (api.fetchCartItems as jest.Mock).mockResolvedValueOnce(mockCartItems);
    
    render(<CartItemList />);
    
    await waitFor(() => {
      expect(screen.getByText('Product 1')).toBeInTheDocument();
      expect(screen.getByText('Product 2')).toBeInTheDocument();
    });
  });

  it('handles API error gracefully', async () => {
    (api.fetchCartItems as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
    
    render(<CartItemList />);
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
    });
  });

  it('handles empty cart state', async () => {
    (api.fetchCartItems as jest.Mock).mockResolvedValueOnce([]);
    
    render(<CartItemList />);
    
    await waitFor(() => {
      expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
    });
  });

  it('allows item removal', async () => {
    (api.fetchCartItems as jest.Mock).mockResolvedValueOnce(mockCartItems);
    (api.deleteCartItem as jest.Mock).mockResolvedValueOnce({ success: true });
    
    render(<CartItemList />);
    
    await waitFor(() => {
      expect(screen.getByText('Product 1')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getAllByText(/remove/i)[0]);
    
    await waitFor(() => {
      expect(api.deleteCartItem).toHaveBeenCalledWith(1);
    });
  });
});

describe('CartItemForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders form with initial values', () => {
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    expect(screen.getByDisplayValue('Product 1')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2')).toBeInTheDocument();
  });

  it('updates quantity when user types', () => {
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    const quantityInput = screen.getByLabelText(/quantity/i) as HTMLInputElement;
    fireEvent.change(quantityInput, { target: { value: '5' } });
    
    expect(quantityInput.value).toBe('5');
  });

  it('submits form with valid data', async () => {
    (api.updateCartItem as jest.Mock).mockResolvedValueOnce({ ...mockCartItem, quantity: 5 });
    
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    const quantityInput = screen.getByLabelText(/quantity/i);
    fireEvent.change(quantityInput, { target: { value: '5' } });
    
    fireEvent.click(screen.getByText(/update/i));
    
    await waitFor(() => {
      expect(api.updateCartItem).toHaveBeenCalledWith({ ...mockCartItem, quantity: 5 });
      expect(mockOnSubmit).toHaveBeenCalled();
    });
  });

  it('shows validation errors for invalid quantity', async () => {
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    const quantityInput = screen.getByLabelText(/quantity/i);
    fireEvent.change(quantityInput, { target: { value: '0' } });
    
    fireEvent.click(screen.getByText(/update/i));
    
    await waitFor(() => {
      expect(screen.getByText(/quantity must be at least 1/i)).toBeInTheDocument();
    });
  });

  it('handles API submission error', async () => {
    (api.updateCartItem as jest.Mock).mockRejectedValueOnce(new Error('Update failed'));
    
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    fireEvent.click(screen.getByText(/update/i));
    
    await waitFor(() => {
      expect(screen.getByText(/failed to update/i)).toBeInTheDocument();
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(;
      <CartItemForm 
        item={mockCartItem} 
        onSubmit={mockOnSubmit} 
        onCancel={mockOnCancel} 
      />
    );
    
    fireEvent.click(screen.getByText(/cancel/i));
    
    expect(mockOnCancel).toHaveBeenCalled();
  });
});

describe('CartItemDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    (api.fetchCartItem as jest.Mock).mockResolvedValueOnce(null);
    render(<CartItemDetail itemId={1} />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('renders item details after loading', async () => {
    (api.fetchCartItem as jest.Mock).mockResolvedValueOnce(mockCartItem);
    
    render(<CartItemDetail itemId={1} />);
    
    await waitFor(() => {
      expect(screen.getByText('Product 1')).toBeInTheDocument();
      expect(screen.getByText('$100.00')).toBeInTheDocument();
    });
  });

  it('handles API error gracefully', async () => {
    (api.fetchCartItem as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
    
    render(<CartItemDetail itemId={1} />);
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load item details/i)).toBeInTheDocument();
    });
  });

  it('shows not found message for invalid item', async () => {
    (api.fetchCartItem as jest.Mock).mockResolvedValueOnce(null);
    
    render(<CartItemDetail itemId={999} />);
    
    await waitFor(() => {
      expect(screen.getByText(/item not found/i)).toBeInTheDocument();
    });
  });
});
`

Key features of these tests:

1. **Component Rendering**:
   - Tests initial loading states
   - Verifies correct rendering of content
   - Handles empty/invalid states

2. **User Interactions**:
   - Form input changes
   - Button clicks
   - Quantity updates

3. **Form Validation**:
   - Tests validation error messages
   - Ensures proper validation logic

4. **API Calls**:
   - Mocks API functions using jest.mock
   - Tests successful API responses
   - Handles API errors gracefully

5. **Error States**:
   - API failure scenarios
   - Validation errors
   - Not found states

6. **Loading States**:
   - Initial loading indicators
   - Loading during API operations

Additional setup needed:

1. Create a `__mocks__` directory with mock implementations:
ts`
// __mocks__/cartApi.ts
export const fetchCartItems = jest.fn();
export const updateCartItem = jest.fn();
export const deleteCartItem = jest.fn();
export const fetchCartItem = jest.fn();
`

2. Create a `types.ts` file:
ts`
export interface CartItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  productId: number;
}
`

3. Install testing dependencies:
bash`
npm install --save-dev @testing-library/react @testing-library/jest-dom jest-fetch-mock
`

4. Configure Jest setup:
ts`
// jest.setup.ts

`

These tests ensure all components handle their responsibilities correctly:
- CartItemList manages the list of items with proper loading/error states
- CartItemForm handles form validation and submission
- CartItemDetail displays individual item information with proper error handling