


import * as api from '../api/cartApi';



import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { CartItem } from '../types/cart.js';
import { CartItemDetail, CartItemForm, CartItemList } from './CartItem.js';
import userEvent from '@testing-library/user-event';
import React from 'react';

I'll create comprehensive React Testing Library tests for CartItem components. Since the application type wasn't specified, I'll assume it's an e-commerce application with typical cart functionality.


// CartItem.test.tsx






// Mock the API calls
jest.mock('../api/cartApi');

// Mock data
const mockCartItems: CartItem[] = [;
  {
    id: '1',
    productId: 'p1',
    name: 'Product 1',
    price: 29.99,
    quantity: 2,
    image: 'product1.jpg',
    description: 'Description for product 1'
  },
  {
    id: '2',
    productId: 'p2',
    name: 'Product 2',
    price: 49.99,
    quantity: 1,
    image: 'product2.jpg',
    description: 'Description for product 2'
  }
];

const mockCartItem: CartItem = {
  id: '1',
  productId: 'p1',
  name: 'Product 1',
  price: 29.99,
  quantity: 2,
  image: 'product1.jpg',
  description: 'Description for product 1'
};

describe('CartItemList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders cart items correctly', async () => {
    (api.getCartItems as jest.Mock).mockResolvedValue(mockCartItems);

    render(<CartItemList />);

    // Check loading state
    expect(screen.getByText(/loading/i)).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });

    // Check that items are rendered
    expect(screen.getByText('Product 1')).toBeInTheDocument();
    expect(screen.getByText('Product 2')).toBeInTheDocument();
    expect(screen.getByText('$29.99')).toBeInTheDocument();
    expect(screen.getByText('$49.99')).toBeInTheDocument();
  });

  test('handles API error gracefully', async () => {
    (api.getCartItems as jest.Mock).mockRejectedValue(new Error('API Error'));

    render(<CartItemList />);

    await waitFor(() => {
      expect(screen.getByText(/failed to load cart items/i)).toBeInTheDocument();
    });
  });

  test('displays empty state when no items', async () => {
    (api.getCartItems as jest.Mock).mockResolvedValue([]);

    render(<CartItemList />);

    await waitFor(() => {
      expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
    });
  });

  test('handles item removal', async () => {
    (api.getCartItems as jest.Mock).mockResolvedValue(mockCartItems);
    (api.removeCartItem as jest.Mock).mockResolvedValue({ success: true });

    render(<CartItemList />);

    await waitFor(() => {
      expect(screen.getByText('Product 1')).toBeInTheDocument();
    });

    const removeButtons = screen.getAllByRole('button', { name: /remove/i });
    fireEvent.click(removeButtons[0]);

    await waitFor(() => {
      expect(api.removeCartItem).toHaveBeenCalledWith('1');
    });
  });
});

describe('CartItemForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders form fields correctly', () => {
    render(<CartItemForm onSubmit={mockOnSubmit} />);

    expect(screen.getByLabelText(/product name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/price/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/quantity/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add to cart/i })).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    render(<CartItemForm onSubmit={mockOnSubmit} />);

    const submitButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/product name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/price is required/i)).toBeInTheDocument();
      expect(screen.getByText(/quantity is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  test('validates price and quantity are numbers', async () => {
    render(<CartItemForm onSubmit={mockOnSubmit} />);

    const nameInput = screen.getByLabelText(/product name/i);
    const priceInput = screen.getByLabelText(/price/i);
    const quantityInput = screen.getByLabelText(/quantity/i);

    await userEvent.type(nameInput, 'Test Product');
    await userEvent.type(priceInput, 'invalid');
    await userEvent.type(quantityInput, 'invalid');

    const submitButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/price must be a valid number/i)).toBeInTheDocument();
      expect(screen.getByText(/quantity must be a valid number/i)).toBeInTheDocument();
    });
  });

  test('submits form with valid data', async () => {
    (api.addCartItem as jest.Mock).mockResolvedValue({ success: true });

    render(<CartItemForm onSubmit={mockOnSubmit} />);

    const nameInput = screen.getByLabelText(/product name/i);
    const priceInput = screen.getByLabelText(/price/i);
    const quantityInput = screen.getByLabelText(/quantity/i);
    const descriptionInput = screen.getByLabelText(/description/i);

    await userEvent.type(nameInput, 'Test Product');
    await userEvent.type(priceInput, '29.99');
    await userEvent.type(quantityInput, '2');
    await userEvent.type(descriptionInput, 'Test description');

    const submitButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(api.addCartItem).toHaveBeenCalledWith({
        name: 'Test Product',
        price: 29.99,
        quantity: 2,
        description: 'Test description'
      });
    });
  });

  test('shows error message on API failure', async () => {
    (api.addCartItem as jest.Mock).mockRejectedValue(new Error('API Error'));

    render(<CartItemForm onSubmit={mockOnSubmit} />);

    const nameInput = screen.getByLabelText(/product name/i);
    const priceInput = screen.getByLabelText(/price/i);
    const quantityInput = screen.getByLabelText(/quantity/i);

    await userEvent.type(nameInput, 'Test Product');
    await userEvent.type(priceInput, '29.99');
    await userEvent.type(quantityInput, '2');

    const submitButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/failed to add item to cart/i)).toBeInTheDocument();
    });
  });

  test('resets form after successful submission', async () => {
    (api.addCartItem as jest.Mock).mockResolvedValue({ success: true });

    render(<CartItemForm onSubmit={mockOnSubmit} />);

    const nameInput = screen.getByLabelText(/product name/i);
    const priceInput = screen.getByLabelText(/price/i);
    const quantityInput = screen.getByLabelText(/quantity/i);

    await userEvent.type(nameInput, 'Test Product');
    await userEvent.type(priceInput, '29.99');
    await userEvent.type(quantityInput, '2');

    const submitButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(nameInput).toHaveValue('');
      expect(priceInput).toHaveValue('');
      expect(quantityInput).toHaveValue('');
    });
  });
});

describe('CartItemDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders cart item details correctly', () => {
    render(<CartItemDetail item={mockCartItem} />);

    expect(screen.getByText('Product 1')).toBeInTheDocument();
    expect(screen.getByText('$29.99')).toBeInTheDocument();
    expect(screen.getByText('Quantity: 2')).toBeInTheDocument();
    expect(screen.getByText('Description for product 1')).toBeInTheDocument();
    expect(screen.getByAltText('Product 1')).toBeInTheDocument();
  });

  test('calculates and displays total price', () => {
    render(<CartItemDetail item={mockCartItem} />);

    const totalPrice = 29.99 * 2;
    expect(screen.getByText(`Total: $${totalPrice.toFixed(2)}`)).toBeInTheDocument();
  });

  test('handles quantity update', async () => {
    (api.updateCartItemQuantity as jest.Mock).mockResolvedValue({ success: true });

    render(<CartItemDetail item={mockCartItem} />);

    const quantityInput = screen.getByLabelText(/quantity/i);
    await userEvent.clear(quantityInput);
    await userEvent.type(quantityInput, '5');

    const updateButton = screen.getByRole('button', { name: /update quantity/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(api.updateCartItemQuantity).toHaveBeenCalledWith('1', 5);
    });
  });

  test('shows error when quantity update fails', async () => {
    (api.updateCartItemQuantity as jest.Mock).mockRejectedValue(new Error('API Error'));

    render(<CartItemDetail item={mockCartItem} />);

    const quantityInput = screen.getByLabelText(/quantity/i);
    await userEvent.clear(quantityInput);
    await userEvent.type(quantityInput, '5');

    const updateButton = screen.getByRole('button', { name: /update quantity/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText(/failed to update quantity/i)).toBeInTheDocument();
    });
  });

  test('validates quantity input', async () => {
    render(<CartItemDetail item={mockCartItem} />);

    const quantityInput = screen.getByLabelText(/quantity/i);
    await userEvent.clear(quantityInput);
    await userEvent.type(quantityInput, '0');

    const updateButton = screen.getByRole('button', { name: /update quantity/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText(/quantity must be at least 1/i)).toBeInTheDocument();
    });
  });

  test('shows loading state during API call', async () => {
    (api.updateCartItemQuantity as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
    );

    render(<CartItemDetail item={mockCartItem} />);

    const quantityInput = screen.getByLabelText(/quantity/i);
    await userEvent.clear(quantityInput);
    await userEvent.type(quantityInput, '3');

    const updateButton = screen.getByRole('button', { name: /update quantity/i });
    fireEvent.click(updateButton);

    expect(screen.getByText(/updating/i)).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.queryByText(/updating/i)).not.toBeInTheDocument();
    });
  });
});
`

This test file includes:

1. **CartItemList Tests**:
   - Rendering with loading state
   - Handling API errors
   - Displaying empty state
   - Item removal functionality

2. **CartItemForm Tests**:
   - Form field rendering
   - Required field validation
   - Number validation for price/quantity
   - Form submission with valid data
   - Error handling for API failures
   - Form reset after successful submission

3. **CartItemDetail Tests**:
   - Rendering item details
   - Total price calculation
   - Quantity update functionality
   - Error handling for quantity updates
   - Input validation
   - Loading states during API calls

The tests use:
- Mocked API functions for testing without real network calls
- Comprehensive user interactions with React Testing Library
- Proper async/await handling for API calls
- Validation testing for form inputs
- Error state testing
- Loading state testing

Note: This assumes the existence of:
- A `cartApi` module with functions like `getCartItems`, `addCartItem`, `removeCartItem`, and `updateCartItemQuantity`
- A `CartItem` type definition
- The actual components being tested (`CartItemList`, `CartItemForm`, `CartItemDetail`)