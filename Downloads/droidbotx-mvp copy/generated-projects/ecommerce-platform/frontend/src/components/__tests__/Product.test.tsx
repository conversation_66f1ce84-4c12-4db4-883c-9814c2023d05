








import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { ProductDetail } from '../components/ProductDetail.js';
import { ProductForm } from '../components/ProductForm.js';
import { ProductList } from '../components/ProductList.js';
import { Product } from '../types.js';
import React from 'react';

Here's a comprehensive test suite for the Product components using React Testing Library:'

tsx`
// src/__tests__/ProductComponents.test.tsx








// Mock API handlers
const products: Product[] = [;
  { id: 1, name: 'Test Product 1', price: 100, description: 'Description 1' },
  { id: 2, name: 'Test Product 2', price: 200, description: 'Description 2' },
];

const server = setupServer(;
  // GET /products
  rest.get('/api/products', (req, res, ctx) => {
    return res(ctx.json(products));
  }),

  // GET /products/:id
  rest.get('/api/products/:id', (req, res, ctx) => {
    const { id } = req.params;
    const product = products.find(p => p.id === Number(id));
    if (product) {
      return res(ctx.json(product));
    }
    return res(ctx.status(404));
  }),

  // POST /products
  rest.post('/api/products', (req, res, ctx) => {
    const newProduct = {
      id: 3,
      name: 'New Product',
      price: 300,
      description: 'New Description'
    };
    return res(ctx.json(newProduct), ctx.status(201));
  }),

  // PUT /products/:id
  rest.put('/api/products/:id', (req, res, ctx) => {
    return res(ctx.json({ message: 'Product updated' }), ctx.status(200));
  })
);

// Setup and teardown
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Product Components', () => {
  describe('ProductList', () => {
    it('renders loading state initially', async () => {
      render(<ProductList />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
      await waitFor(() => {
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
      });
    });

    it('renders product items after loading', async () => {
      render(<ProductList />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
        expect(screen.getByText('Test Product 2')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      server.use(
        rest.get('/api/products', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ message: 'Server error' }));
        })
      );
      
      render(<ProductList />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });
  });

  describe('ProductForm', () => {
    const product = {
      id: 1,
      name: 'Test Product',
      price: 100,
      description: 'Test Description'
    };

    it('renders form with initial values', () => {
      render(<ProductForm product={product} />);
      
      expect(screen.getByLabelText(/name/i)).toHaveValue('Test Product');
      expect(screen.getByLabelText(/price/i)).toHaveValue(100);
      expect(screen.getByLabelText(/description/i)).toHaveValue('Test Description');
    });

    it('shows validation errors for required fields', async () => {
      render(<ProductForm />);
      
      const submitButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/price is required/i)).toBeInTheDocument();
      });
    });

    it('submits form data successfully', async () => {
      const onSubmit = jest.fn();
      render(<ProductForm onSubmit={onSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'New Product' }
      });
      fireEvent.change(screen.getByLabelText(/price/i), {
        target: { value: '299' }
      });
      fireEvent.change(screen.getByLabelText(/description/i), {
        target: { value: 'New Description' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /save/i }));
      
      await waitFor(() => {
        expect(onSubmit).toHaveBeenCalledWith({
          name: 'New Product',
          price: 299,
          description: 'New Description'
        });
      });
    });

    it('handles API error during submission', async () => {
      server.use(
        rest.post('/api/products', (req, res, ctx) => {
          return res(ctx.status(400), ctx.json({ message: 'Validation error' }));
        })
      );
      
      render(<ProductForm />);
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'New Product' }
      });
      fireEvent.change(screen.getByLabelText(/price/i), {
        target: { value: '299' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /save/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/validation error/i)).toBeInTheDocument();
      });
    });
  });

  describe('ProductDetail', () => {
    it('renders loading state initially', async () => {
      render(<ProductDetail productId="1" />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
      await waitFor(() => {
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
      });
    });

    it('renders product details after loading', async () => {
      render(<ProductDetail productId="1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
        expect(screen.getByText('Description 1')).toBeInTheDocument();
        expect(screen.getByText('$100')).toBeInTheDocument();
      });
    });

    it('handles product not found', async () => {
      server.use(
        rest.get('/api/products/:id', (req, res, ctx) => {
          return res(ctx.status(404));
        })
      );
      
      render(<ProductDetail productId="999" />);
      
      await waitFor(() => {
        expect(screen.getByText(/not found/i)).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      server.use(
        rest.get('/api/products/:id', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ message: 'Server error' }));
        })
      );
      
      render(<ProductDetail productId="1" />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });
  });
});
`

This test suite includes:

1. **Component Rendering Tests**:
   - ProductList renders loading state and then product items
   - ProductForm renders with initial values
   - ProductDetail renders loading state and then product details

2. **User Interaction Tests**:
   - Form submission with valid data
   - Form validation for required fields
   - Navigation handling

3. **Form Validation Tests**:
   - Required field validation
   - Error message display

4. **API Call Tests**:
   - Successful data fetching for lists and details
   - POST/PUT request handling
   - Error responses from API

5. **Error State Tests**:
   - API error handling for lists and details
   - Product not found scenarios
   - Validation error display

6. **Loading State Tests**:
   - Initial loading states for lists and details
   - Loading state transitions

Key features of the implementation:
- Uses MSW (Mock Service Worker) for API mocking
- Comprehensive test coverage for all specified components
- Proper async/await handling with waitFor
- Realistic mock data and API responses
- Jest mocking for navigation
- TypeScript type safety throughout
- Proper cleanup and setup/teardown

To run these tests, you'll need to install the required dependencies:'
bash`
npm install --save-dev @testing-library/react @testing-library/jest-dom msw
`

The tests assume a standard React component structure with:
- ProductList fetching from `/api/products`
- ProductDetail fetching from `/api/products/:id`
- ProductForm submitting to `/api/products` (POST/PUT)
- Standard error handling patterns
- React Router for navigation