









// ProductList.test.tsx






// Mock the service
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ProductDetail } from '../components/ProductDetail.js';
import { ProductForm } from '../components/ProductForm.js';
import { ProductList } from '../components/ProductList.js';
import { ProductService } from '../services/ProductService.js';
import { Product } from '../types/Product.js';
import React from 'react';

jest.mock('../services/ProductService');

const mockProducts: Product[] = [;
  {
    id: '1',
    name: 'Laptop',
    description: 'High-performance laptop',
    price: 1200,
    category: 'Electronics',
    stock: 10,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: '2',
    name: 'Desk Chair',
    description: 'Ergonomic office chair',
    price: 300,
    category: 'Furniture',
    stock: 5,
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-02')
  }
];

const renderWithRouter = (component: React.ReactNode) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('ProductList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    (ProductService.getAllProducts as jest.Mock).mockResolvedValue([]);
    
    renderWithRouter(<ProductList />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  test('renders product list when data is loaded', async () => {
    (ProductService.getAllProducts as jest.Mock).mockResolvedValue(mockProducts);
    
    renderWithRouter(<ProductList />);
    
    await waitFor(() => {
      expect(screen.getByText('Laptop')).toBeInTheDocument();
      expect(screen.getByText('Desk Chair')).toBeInTheDocument();
      expect(screen.getByText('$1,200.00')).toBeInTheDocument();
      expect(screen.getByText('$300.00')).toBeInTheDocument();
    });
  });

  test('renders error state when API call fails', async () => {
    (ProductService.getAllProducts as jest.Mock).mockRejectedValue(new Error('API Error'));
    
    renderWithRouter(<ProductList />);
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load products/i)).toBeInTheDocument();
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
    });
  });

  test('handles refresh action', async () => {
    (ProductService.getAllProducts as jest.Mock)
      .mockResolvedValueOnce(mockProducts)
      .mockResolvedValueOnce([...mockProducts, {
        id: '3',
        name: 'Monitor',
        description: '4K Monitor',
        price: 400,
        category: 'Electronics',
        stock: 8,
        createdAt: new Date('2023-01-03'),
        updatedAt: new Date('2023-01-03')
      }]);
    
    renderWithRouter(<ProductList />);
    
    await waitFor(() => {
      expect(screen.getByText('Laptop')).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    await waitFor(() => {
      expect(screen.getByText('Monitor')).toBeInTheDocument();
    });
  });

  test('handles delete product action', async () => {
    (ProductService.getAllProducts as jest.Mock).mockResolvedValue(mockProducts);
    (ProductService.deleteProduct as jest.Mock).mockResolvedValue({ success: true });
    
    renderWithRouter(<ProductList />);
    
    await waitFor(() => {
      expect(screen.getByText('Laptop')).toBeInTheDocument();
    });
    
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);
    
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(ProductService.deleteProduct).toHaveBeenCalledWith('1');
      expect(screen.queryByText('Laptop')).not.toBeInTheDocument();
    });
  });

  test('shows empty state when no products exist', async () => {
    (ProductService.getAllProducts as jest.Mock).mockResolvedValue([]);
    
    renderWithRouter(<ProductList />);
    
    await waitFor(() => {
      expect(screen.getByText(/no products found/i)).toBeInTheDocument();
      expect(screen.getByText(/add your first product/i)).toBeInTheDocument();
    });
  });
});
`


// ProductForm.test.tsx






jest.mock('../services/ProductService');

const mockProduct: Product = {
  id: '1',
  name: 'Laptop',
  description: 'High-performance laptop',
  price: 1200,
  category: 'Electronics',
  stock: 10,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01')
};

const renderWithRouter = (component: React.ReactNode) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('ProductForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders create form correctly', () => {
    renderWithRouter(<ProductForm />);
    
    expect(screen.getByText(/create product/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/price/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/stock/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  test('renders edit form with existing product data', () => {
    renderWithRouter(<ProductForm product={mockProduct} />);
    
    expect(screen.getByText(/edit product/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('Laptop')).toBeInTheDocument();
    expect(screen.getByDisplayValue('High-performance laptop')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1200')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Electronics')).toBeInTheDocument();
    expect(screen.getByDisplayValue('10')).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    renderWithRouter(<ProductForm />);
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/price is required/i)).toBeInTheDocument();
      expect(screen.getByText(/category is required/i)).toBeInTheDocument();
      expect(screen.getByText(/stock is required/i)).toBeInTheDocument();
    });
  });

  test('validates price and stock as numbers', async () => {
    renderWithRouter(<ProductForm />);
    
    const priceInput = screen.getByLabelText(/price/i);
    const stockInput = screen.getByLabelText(/stock/i);
    
    fireEvent.change(priceInput, { target: { value: 'invalid' } });
    fireEvent.change(stockInput, { target: { value: 'invalid' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText(/price must be a valid number/i)).toBeInTheDocument();
      expect(screen.getByText(/stock must be a valid number/i)).toBeInTheDocument();
    });
  });

  test('validates positive numbers for price and stock', async () => {
    renderWithRouter(<ProductForm />);
    
    const priceInput = screen.getByLabelText(/price/i);
    const stockInput = screen.getByLabelText(/stock/i);
    
    fireEvent.change(priceInput, { target: { value: '-100' } });
    fireEvent.change(stockInput, { target: { value: '-5' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText(/price must be greater than 0/i)).toBeInTheDocument();
      expect(screen.getByText(/stock must be greater than or equal to 0/i)).toBeInTheDocument();
    });
  });

  test('submits form for creating new product', async () => {
    (ProductService.createProduct as jest.Mock).mockResolvedValue({ ...mockProduct, id: 'new-id' });
    
    renderWithRouter(<ProductForm />);
    
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'New Product' } });
    fireEvent.change(screen.getByLabelText(/description/i), { target: { value: 'New product description' } });
    fireEvent.change(screen.getByLabelText(/price/i), { target: { value: '299.99' } });
    fireEvent.change(screen.getByLabelText(/category/i), { target: { value: 'Electronics' } });
    fireEvent.change(screen.getByLabelText(/stock/i), { target: { value: '15' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(ProductService.createProduct).toHaveBeenCalledWith({
        name: 'New Product',
        description: 'New product description',
        price: 299.99,
        category: 'Electronics',
        stock: 15
      });
      expect(screen.getByText(/product created successfully/i)).toBeInTheDocument();
    });
  });

  test('submits form for updating existing product', async () => {
    (ProductService.updateProduct as jest.Mock).mockResolvedValue(mockProduct);
    
    renderWithRouter(<ProductForm product={mockProduct} />);
    
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'Updated Laptop' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(ProductService.updateProduct).toHaveBeenCalledWith('1', {
        id: '1',
        name: 'Updated Laptop',
        description: 'High-performance laptop',
        price: 1200,
        category: 'Electronics',
        stock: 10,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(screen.getByText(/product updated successfully/i)).toBeInTheDocument();
    });
  });

  test('handles API error during submission', async () => {
    (ProductService.createProduct as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    renderWithRouter(<ProductForm />);
    
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'New Product' } });
    fireEvent.change(screen.getByLabelText(/price/i), { target: { value: '299.99' } });
    fireEvent.change(screen.getByLabelText(/category/i), { target: { value: 'Electronics' } });
    fireEvent.change(screen.getByLabelText(/stock/i), { target: { value: '15' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText(/failed to save product/i)).toBeInTheDocument();
    });
  });

  test('shows loading state during submission', async () => {
    (ProductService.createProduct as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockProduct), 100))
    );
    
    renderWithRouter(<ProductForm />);
    
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'New Product' } });
    fireEvent.change(screen.getByLabelText(/price/i), { target: { value: '299.99' } });
    fireEvent.change(screen.getByLabelText(/category/i), { target: { value: 'Electronics' } });
    fireEvent.change(screen.getByLabelText(/stock/i), { target: { value: '15' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    expect(saveButton).toBeDisabled();
    expect(screen.getByText(/saving/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(saveButton).not.toBeDisabled();
    });
  });

  test('handles cancel action', () => {
    const mockOnCancel = jest.fn();
    renderWithRouter(<ProductForm onCancel={mockOnCancel} />);
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });
});
`


// ProductDetail.test.tsx






jest.mock('../services/ProductService');

const mockProduct: Product = {
  id: '1',
  name: 'Laptop',
  description: 'High-performance laptop with 16GB RAM and 512GB SSD',
  price: 1200,
  category: 'Electronics',
  stock: 10,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-15')
};

const renderWithRouter = (component: React.ReactNode) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('ProductDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    (ProductService.getProductById as jest.Mock).mockResolvedValue(mockProduct);