








import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import React from 'react';
import UserDetail from '../components/UserDetail.js';
import UserForm from '../components/UserForm.js';
import UserList from '../components/UserList.js';

Here's a comprehensive test suite for User components using React Testing Library:'

tsx`
// UserComponents.test.tsx








// Mock axios for API calls
const mockAxios = new MockAdapter(axios);

// Mock user data
const mockUsers = [;
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'user' },
];

const mockUser = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'admin',
  createdAt: '2023-01-01T00:00:00Z',
  lastLogin: '2023-05-01T12:00:00Z',
};

// Wrapper for components that use React Router
const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('User Components', () => {
  beforeEach(() => {
    mockAxios.reset();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('UserList Component', () => {
    it('renders loading state initially', () => {
      mockAxios.onGet('/api/users').reply(() => new Promise(() => {})); // Never resolve
      
      renderWithRouter(<UserList />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders user list after successful API call', async () => {
      mockAxios.onGet('/api/users').reply(200, mockUsers);
      
      renderWithRouter(<UserList />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
      
      expect(screen.getAllByRole('row')).toHaveLength(3); // Header + 2 users
    });

    it('handles API error gracefully', async () => {
      mockAxios.onGet('/api/users').reply(500);
      
      renderWithRouter(<UserList />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load users/i)).toBeInTheDocument();
      });
    });

    it('handles empty user list', async () => {
      mockAxios.onGet('/api/users').reply(200, []);
      
      renderWithRouter(<UserList />);
      
      await waitFor(() => {
        expect(screen.getByText(/no users found/i)).toBeInTheDocument();
      });
    });

    it('navigates to user detail on row click', async () => {
      mockAxios.onGet('/api/users').reply(200, [mockUsers[0]]);
      
      renderWithRouter(<UserList />);
      
      await waitFor(() => {
        const row = screen.getByRole('row', { name: /john doe/i });
        fireEvent.click(row);
      });
      
      // In a real test, you'd check navigation with a router mock'
      expect(screen.getByRole('row', { name: /john doe/i })).toBeInTheDocument();
    });
  });

  describe('UserForm Component', () => {
    const mockOnSubmit = jest.fn();

    beforeEach(() => {
      mockOnSubmit.mockClear();
    });

    it('renders form fields correctly', () => {
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/role is required/i)).toBeInTheDocument();
      });
      
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('validates email format', async () => {
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      fireEvent.input(screen.getByLabelText(/email/i), {
        target: { value: 'invalid-email' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/enter a valid email/i)).toBeInTheDocument();
      });
    });

    it('submits form with valid data', async () => {
      mockAxios.onPost('/api/users').reply(201, mockUser);
      
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      fireEvent.input(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' }
      });
      fireEvent.input(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/role/i), {
        target: { value: 'admin' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'admin'
        });
      });
    });

    it('shows error message on submission failure', async () => {
      mockAxios.onPost('/api/users').reply(500);
      
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      fireEvent.input(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' }
      });
      fireEvent.input(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/role/i), {
        target: { value: 'admin' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/failed to create user/i)).toBeInTheDocument();
      });
    });

    it('shows loading state during submission', async () => {
      mockAxios.onPost('/api/users').reply(() => new Promise(() => {})); // Never resolve
      
      render(<UserForm onSubmit={mockOnSubmit} />);
      
      fireEvent.input(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' }
      });
      fireEvent.input(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/role/i), {
        target: { value: 'admin' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      expect(screen.getByRole('button', { name: /submit/i })).toBeDisabled();
      expect(screen.getByText(/submitting/i)).toBeInTheDocument();
    });
  });

  describe('UserDetail Component', () => {
    it('renders loading state initially', () => {
      mockAxios.onGet('/api/users/1').reply(() => new Promise(() => {})); // Never resolve
      
      renderWithRouter(<UserDetail />);
      
      expect(screen.getByText(/loading user details/i)).toBeInTheDocument();
    });

    it('renders user details after successful API call', async () => {
      mockAxios.onGet('/api/users/1').reply(200, mockUser);
      
      renderWithRouter(<UserDetail />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('admin')).toBeInTheDocument();
        expect(screen.getByText('2023-01-01')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      mockAxios.onGet('/api/users/1').reply(404);
      
      renderWithRouter(<UserDetail />);
      
      await waitFor(() => {
        expect(screen.getByText(/user not found/i)).toBeInTheDocument();
      });
    });

    it('handles network error', async () => {
      mockAxios.onGet('/api/users/1').networkError();
      
      renderWithRouter(<UserDetail />);
      
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
    });

    it('displays formatted dates correctly', async () => {
      mockAxios.onGet('/api/users/1').reply(200, mockUser);
      
      renderWithRouter(<UserDetail />);
      
      await waitFor(() => {
        expect(screen.getByText('2023-01-01')).toBeInTheDocument();
        expect(screen.getByText('2023-05-01')).toBeInTheDocument();
      });
    });

    it('renders action buttons', async () => {
      mockAxios.onGet('/api/users/1').reply(200, mockUser);
      
      renderWithRouter(<UserDetail />);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
      });
    });
  });
});
`

This test suite covers:

1. **Component Rendering**:
   - Loading states
   - Successful data display
   - Empty states
   - Error states

2. **User Interactions**:
   - Form input
   - Button clicks
   - Row selection
   - Dropdown selection

3. **Form Validation**:
   - Required field validation
   - Email format validation
   - Error message display

4. **API Calls**:
   - Successful responses
   - Error responses (4xx, 5xx)
   - Network errors
   - Loading states during requests

5. **Error States**:
   - API errors
   - Network errors
   - Validation errors
   - Empty data states

6. **Loading States**:
   - Initial loading
   - Form submission loading
   - Data fetching loading

Key features of the implementation:

- Uses `axios-mock-adapter` for API mocking
- Implements proper async/await with `waitFor`
- Tests both happy paths and error scenarios
- Covers edge cases like empty lists and network failures
- Uses proper accessibility queries (byRole, byLabelText, etc.)
- Includes router context for components that need it
- Tests form validation and submission flows
- Verifies loading and disabled states

To run these tests, you'll need to install the required dependencies:'

bash`
npm install --save-dev @testing-library/react @testing-library/jest-dom axios-mock-adapter
`

The tests assume your components are located in `../components/` relative to the test file. Adjust the import paths as needed for your project structure.