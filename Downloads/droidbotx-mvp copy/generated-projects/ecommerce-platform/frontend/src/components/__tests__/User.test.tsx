


import * as userApi from '../api/userApi';





import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { User } from '../types.js';
import { UserDetail } from './UserDetail.js';
import { UserForm } from './UserForm.js';
import { UserList } from './UserList.js';
import React from 'react';

Here's a comprehensive test suite for the User components using React Testing Library:'

tsx`
// UserComponents.test.tsx








// Mock API functions
jest.mock('../api/userApi', () => ({
  fetchUsers: jest.fn(),
  fetchUserById: jest.fn(),
  createUser: jest.fn(),
  updateUser: jest.fn(),
  deleteUser: jest.fn(),
}));

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock user data
const mockUsers: User[] = [;
  { id: 1, name: '<PERSON> Doe', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
];

const mockUser: User = {
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'admin',
};

describe('User Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('UserList', () => {
    it('renders loading state initially', () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValue([]);
      render(<UserList />, { wrapper: BrowserRouter });
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders user list after loading', async () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValue(mockUsers);
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      (userApi.fetchUsers as jest.Mock).mockRejectedValue(new Error('API Error'));
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load users/i)).toBeInTheDocument();
      });
    });

    it('navigates to user detail on click', async () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValue(mockUsers);
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('John Doe'));
      });
      
      expect(mockNavigate).toHaveBeenCalledWith('/users/1');
    });
  });

  describe('UserForm', () => {
    const defaultProps = {
      onSubmit: jest.fn(),
      initialUser: undefined,
    };

    it('renders empty form for new user', () => {
      render(<UserForm {...defaultProps} />);
      
      expect(screen.getByLabelText(/name/i)).toHaveValue('');
      expect(screen.getByLabelText(/email/i)).toHaveValue('');
      expect(screen.getByLabelText(/role/i)).toHaveValue('user');
    });

    it('renders form with initial values for editing', () => {
      render(<UserForm {...defaultProps} initialUser={mockUser} />);
      
      expect(screen.getByLabelText(/name/i)).toHaveValue('John Doe');
      expect(screen.getByLabelText(/email/i)).toHaveValue('<EMAIL>');
      expect(screen.getByLabelText(/role/i)).toHaveValue('admin');
    });

    it('shows validation errors for required fields', async () => {
      render(<UserForm {...defaultProps} />);
      
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    it('shows validation error for invalid email', async () => {
      render(<UserForm {...defaultProps} />);
      
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: 'invalid-email' },
      });
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
      });
    });

    it('submits valid form data', async () => {
      const mockSubmit = jest.fn();
      render(<UserForm {...defaultProps} onSubmit={mockSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' },
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(screen.getByLabelText(/role/i), {
        target: { value: 'admin' },
      });
      
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'admin',
        });
      });
    });

    it('shows loading state during submission', async () => {
      const mockSubmit = jest.fn().mockImplementation(() => new Promise(() => {}));
      render(<UserForm {...defaultProps} onSubmit={mockSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' },
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' },
      });
      
      fireEvent.click(screen.getByText(/save/i));
      
      expect(screen.getByText(/saving/i)).toBeInTheDocument();
    });

    it('shows error message on submission failure', async () => {
      const mockSubmit = jest.fn().mockRejectedValue(new Error('Submission failed'));
      render(<UserForm {...defaultProps} onSubmit={mockSubmit} />);
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' },
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' },
      });
      
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(screen.getByText(/failed to save user/i)).toBeInTheDocument();
      });
    });
  });

  describe('UserDetail', () => {
    const mockParams = { userId: '1' };
    
    beforeEach(() => {
      jest.mock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useParams: () => mockParams,
      }));
    });

    it('renders loading state initially', () => {
      (userApi.fetchUserById as jest.Mock).mockResolvedValue(null);
      render(<UserDetail />, { wrapper: BrowserRouter });
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders user details after loading', async () => {
      (userApi.fetchUserById as jest.Mock).mockResolvedValue(mockUser);
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('admin')).toBeInTheDocument();
      });
    });

    it('handles API error gracefully', async () => {
      (userApi.fetchUserById as jest.Mock).mockRejectedValue(new Error('API Error'));
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load user/i)).toBeInTheDocument();
      });
    });

    it('navigates to edit form when edit button is clicked', async () => {
      (userApi.fetchUserById as jest.Mock).mockResolvedValue(mockUser);
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText(/edit/i));
      });
      
      expect(mockNavigate).toHaveBeenCalledWith('/users/1/edit');
    });

    it('deletes user and navigates to user list', async () => {
      (userApi.fetchUserById as jest.Mock).mockResolvedValue(mockUser);
      (userApi.deleteUser as jest.Mock).mockResolvedValue({});
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText(/delete/i));
      });
      
      // Confirm deletion
      fireEvent.click(screen.getByText(/confirm/i));
      
      await waitFor(() => {
        expect(userApi.deleteUser).toHaveBeenCalledWith(1);
        expect(mockNavigate).toHaveBeenCalledWith('/users');
      });
    });

    it('shows error when deletion fails', async () => {
      (userApi.fetchUserById as jest.Mock).mockResolvedValue(mockUser);
      (userApi.deleteUser as jest.Mock).mockRejectedValue(new Error('Delete failed'));
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText(/delete/i));
      });
      
      // Confirm deletion
      fireEvent.click(screen.getByText(/confirm/i));
      
      await waitFor(() => {
        expect(screen.getByText(/failed to delete user/i)).toBeInTheDocument();
      });
    });
  });
});
`

This test suite covers:

1. **Component Rendering**:
   - Initial loading states
   - Successful data display
   - Error states

2. **User Interactions**:
   - Form input changes
   - Button clicks
   - Navigation events

3. **Form Validation**:
   - Required field validation
   - Email format validation
   - Error message display

4. **API Calls**:
   - Successful API responses
   - Error responses
   - Loading states during API calls

5. **Error States**:
   - API error handling
   - Form submission errors
   - Deletion errors

6. **Loading States**:
   - Initial data loading
   - Form submission loading
   - Deletion loading

Key features of the implementation:

- Comprehensive mocking of API calls and router functions
- Realistic user data fixtures
- Proper async/await handling for React Testing Library
- Route parameter mocking for UserDetail component
- Form validation testing with user interactions
- Error boundary testing for API failures
- Navigation testing with mocked useNavigate
- Loading state verification
- Test isolation with beforeEach cleanup

The tests follow React Testing Library best practices by:
- Querying by accessible roles/text rather than implementation details
- Testing user behavior rather than component implementation
- Properly handling async operations with waitFor
- Mocking external dependencies
- Verifying both happy paths and error scenarios