

interface CartItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

interface RelatedProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
}

interface CartItemDetailProps {
  cartItemId: string;
  onEdit: (cartItem: CartItem) => void;
  onDelete: (cartItemId: string) => void;
}

const CartItemDetail: React.FC<CartItemDetailProps> = ({ cartItemId, onEdit, onDelete }) => {
  const [cartItem, setCartItem] = useState<CartItem | null>(null);
  const [relatedProduct, setRelatedProduct] = useState<RelatedProduct | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editForm, setEditForm] = useState<Partial<CartItem>>({});

  useEffect(() => {
    const fetchCartItem = async () => {
      try {
        setLoading(true);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data - replace with actual API call
        const mockCartItem: CartItem = {
          id: cartItemId,
          productId: 'prod-123',
          productName: 'Premium Headphones',
          quantity: 2,
          price: 199.99,
          totalPrice: 399.98,
          status: 'pending',
          createdAt: '2023-05-15T10:30:00Z',
          updatedAt: '2023-05-15T10:30:00Z'
        };

        const mockProduct: RelatedProduct = {
          id: 'prod-123',
          name: 'Premium Headphones',
          description: 'High-quality wireless headphones with noise cancellation',
          price: 199.99,
          imageUrl: '/images/headphones.jpg'
        };

        setCartItem(mockCartItem);
        setEditForm(mockCartItem);
        setRelatedProduct(mockProduct);
      } catch (err) {
        setError('Failed to load cart item details');
      } finally {
        setLoading(false);
      }
    };

    fetchCartItem();
  }, [cartItemId]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (cartItem) {
      setEditForm(cartItem);
    }
  };

  const handleSave = () => {
    if (cartItem) {
      const updatedItem = { ...cartItem, ...editForm };
      setCartItem(updatedItem);
      onEdit(updatedItem);
      setIsEditing(false);
    }
  };

  const handleDelete = () => {
    onDelete(cartItemId);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: name === 'quantity' || name === 'price' ? Number(value) : value
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
        <p className="text-red-700 font-medium">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!cartItem) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
        <p className="text-yellow-700 font-medium">Cart item not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Cart Item Details</h1>
            <p className="text-gray-600 mt-1">ID: {cartItem.id}</p>
          </div>
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  aria-label="Save changes"
                >
                  Save
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  aria-label="Cancel editing"
                >
                  Cancel
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={handleEdit}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="Edit cart item"
                >
                  Edit
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  aria-label="Delete cart item"
                >
                  Delete
                </button>
              </>
            )}
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border rounded-lg p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Item Information</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Product Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="productName"
                    value={editForm.productName || ''}
                    onChange={handleInputChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2 border"
                    aria-label="Product name"
                  />
                ) : (
                  <p className="mt-1 text-gray-900">{cartItem.productName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Quantity</label>
                {isEditing ? (
                  <input
                    type="number"
                    name="quantity"
                    value={editForm.quantity || ''}
                    onChange={handleInputChange}
                    min="1"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2 border"
                    aria-label="Quantity"
                  />
                ) : (
                  <p className="mt-1 text-gray-900">{cartItem.quantity}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Unit Price</label>
                {isEditing ? (
                  <input
                    type="number"
                    name="price"
                    value={editForm.price || ''}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2 border"
                    aria-label="Unit price"
                  />
                ) : (
                  <p className="mt-1 text-gray-900">${cartItem.price.toFixed(2)}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Total Price</label>
                <p className="mt-1 text-gray-900 font-medium">${cartItem.totalPrice.toFixed(2)}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                {isEditing ? (
                  <select
                    name="status"
                    value={editForm.status || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value as any }))}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2 border"
                    aria-label="Status"
                  >
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                ) : (
                  <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    cartItem.status === 'completed' ? 'bg-green-100 text-green-800' :
                    cartItem.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                    cartItem.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {cartItem.status.charAt(0).toUpperCase() + cartItem.status.slice(1).replace('_', ' ')}
                  </span>
                )}
              </div>
            </div>
          </div>

          {relatedProduct && (
            <div className="border rounded-lg p-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Related Product</h2>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{relatedProduct.name}</h3>
                  <p className="text-sm text-gray-500 mt-1 line-clamp-2">{relatedProduct.description}</p>
                  <p className="text-lg font-bold text-gray-900 mt-2">${relatedProduct.price.toFixed(2)}</p>
                </div>
              </div>

              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-900">Product Details</h4>
                <dl className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2">
                  <div>
                    <dt className="text-sm text-gray-500">Created</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(cartItem.createdAt).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Last Updated</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(cartItem.updatedAt).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CartItemDetail;