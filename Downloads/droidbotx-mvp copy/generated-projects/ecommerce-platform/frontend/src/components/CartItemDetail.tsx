

interface CartItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  subtotal: number;
  createdAt: string;
  updatedAt: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
}

interface CartItemDetailProps {
  cartItemId: string;
  onEdit: (cartItem: CartItem) => void;
  onDelete: (cartItemId: string) => void;
}

const CartItemDetail: React.FC<CartItemDetailProps> = ({
  cartItemId,
  onEdit,
  onDelete
}) => {
  const [cartItem, setCartItem] = useState<CartItem | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedQuantity, setEditedQuantity] = useState<number>(1);
  const [editError, setEditError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCartItem = async () => {
      try {
        setLoading(true);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));

        // Mock data - replace with actual API call
        const mockCartItem: CartItem = {
          id: cartItemId,
          productId: 'prod-123',
          productName: 'Premium Headphones',
          productImage: 'https://example.com/headphones.jpg',
          price: 199.99,
          quantity: 2,
          subtotal: 399.98,
          createdAt: '2023-05-15T10:30:00Z',
          updatedAt: '2023-05-15T14:45:00Z'
        };

        const mockProduct: Product = {
          id: 'prod-123',
          name: 'Premium Headphones',
          description: 'High-quality wireless headphones with noise cancellation',
          price: 199.99,
          category: 'Electronics',
          stock: 15
        };

        setCartItem(mockCartItem);
        setEditedQuantity(mockCartItem.quantity);
        setProduct(mockProduct);
      } catch (err) {
        setError('Failed to load cart item details');
        console.error('Error fetching cart item:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCartItem();
  }, [cartItemId]);

  const handleEdit = () => {
    setIsEditing(true);
    setEditError(null);
  };

  const handleSave = () => {
    if (!cartItem) return;

    if (editedQuantity < 1) {
      setEditError('Quantity must be at least 1');
      return;
    }

    if (product && editedQuantity > product.stock) {
      setEditError(`Only ${product.stock} items available in stock`);
      return;
    }

    const updatedCartItem: CartItem = {
      ...cartItem,
      quantity: editedQuantity,
      subtotal: editedQuantity * cartItem.price,
      updatedAt: new Date().toISOString()
    };

    setCartItem(updatedCartItem);
    onEdit(updatedCartItem);
    setIsEditing(false);
  };

  const handleCancel = () => {
    if (cartItem) {
      setEditedQuantity(cartItem.quantity);
    }
    setIsEditing(false);
    setEditError(null);
  };

  const handleDelete = () => {
    if (cartItem) {
      onDelete(cartItem.id);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div
          className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
          aria-label="Loading cart item details"
        ></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-red-50 border-l-4 border-red-500 p-4"
        role="alert"
        aria-live="polite"
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">
              {error}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!cartItem) {
    return (
      <div
        className="bg-yellow-50 border-l-4 border-yellow-400 p-4"
        role="alert"
        aria-live="polite"
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-yellow-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              Cart item not found
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/3 mb-6 md:mb-0">
            <img
              src={cartItem.productImage}
              alt={cartItem.productName}
              className="w-full h-64 object-contain rounded-lg"
            />
          </div>

          <div className="md:w-2/3 md:pl-6">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{cartItem.productName}</h2>
                <p className="text-gray-600 mt-1">Item ID: {cartItem.id}</p>
              </div>

              <div className="flex space-x-2">
                {!isEditing ? (
                  <>
                    <button
                      onClick={handleEdit}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                      aria-label={`Edit ${cartItem.productName}`}
                    >
                      Edit
                    </button>
                    <button
                      onClick={handleDelete}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                      aria-label={`Delete ${cartItem.productName}`}
                    >
                      Delete
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={handleSave}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
                      aria-label="Save changes"
                    >
                      Save
                    </button>
                    <button
                      onClick={handleCancel}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                      aria-label="Cancel editing"
                    >
                      Cancel
                    </button>
                  </>
                )}
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Item Details</h3>
                <dl className="mt-2 space-y-3">
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Price</dt>
                    <dd className="text-gray-900">${cartItem.price.toFixed(2)}</dd>
                  </div>

                  <div className="flex justify-between">
                    <dt className="text-gray-600">Quantity</dt>
                    <dd className="text-gray-900">
                      {isEditing ? (
                        <div className="flex items-center">
                          <input
                            type="number"
                            min="1"
                            value={editedQuantity}
                            onChange={(e) => setEditedQuantity(parseInt(e.target.value) || 1)}
                            className="w-20 px-3 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            aria-label="Quantity"
                          />
                          {product && (
                            <span className="ml-2 text-sm text-gray-500">
                              (Max: {product.stock})
                            </span>
                          )}
                        </div>
                      ) : (
                        cartItem.quantity
                      )}
                    </dd>
                  </div>

                  <div className="flex justify-between">
                    <dt className="text-gray-600">Subtotal</dt>
                    <dd className="text-gray-900 font-medium">
                      ${cartItem.subtotal.toFixed(2)}
                    </dd>
                  </div>

                  <div className="flex justify-between">
                    <dt className="text-gray-600">Last Updated</dt>
                    <dd className="text-gray-900">
                      {new Date(cartItem.updatedAt).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>
              </div>

              {product && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Product Information</h3>
                  <dl className="mt-2 space-y-3">
                    <div>
                      <dt className="text-gray-600">Description</dt>
                      <dd className="text-gray-900 mt-1">{product.description}</dd>
                    </div>

                    <div className="flex justify-between">
                      <dt className="text-gray-600">Category</dt>
                      <dd className="text-gray-900">{product.category}</dd>
                    </div>

                    <div className="flex justify-between">
                      <dt className="text-gray-600">Stock</dt>
                      <dd className="text-gray-900">
                        <span className={product.stock > 5 ? 'text-green-600' : 'text-yellow-600'}>
                          {product.stock} available
                        </span>
                      </dd>
                    </div>
                  </dl>
                </div>
              )}
            </div>

            {editError && (
              <div
                className="mt-4 bg-red-50 border-l-4 border-red-500 p-4"
                role="alert"
                aria-live="polite"
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">
                      {editError}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartItemDetail;