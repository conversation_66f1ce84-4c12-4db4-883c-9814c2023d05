

import { useNavigate, useParams } from 'react-router-dom';

interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
}

interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
}

const OrderItemDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [orderItem, setOrderItem] = useState<OrderItem | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editForm, setEditForm] = useState<Partial<OrderItem>>({});

  const fetchOrderItem = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real application, this would be an API call
      // const response = await fetch(`/api/order-items/${id}`);
      // const data = await response.json();

      // Mock data for demonstration
      const mockOrderItem: OrderItem = {
        id: id || '1',
        orderId: 'order-123',
        productId: 'product-456',
        productName: 'Sample Product',
        quantity: 2,
        unitPrice: 29.99,
        totalPrice: 59.98,
        status: 'pending'
      };

      setOrderItem(mockOrderItem);
      setEditForm(mockOrderItem);

      // Fetch related entities
      // Mock product data
      setProduct({
        id: 'product-456',
        name: 'Sample Product',
        description: 'This is a sample product description',
        price: 29.99
      });

      // Mock order data
      setOrder({
        id: 'order-123',
        orderNumber: 'ORD-2023-001',
        customerId: 'customer-789',
        status: 'pending'
      });
    } catch (err) {
      setError('Failed to load order item details');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchOrderItem();
  }, [fetchOrderItem]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm(orderItem || {});
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      // In a real application, this would be an API call
      // await fetch(`/api/order-items/${id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(editForm)
      // });

      // Update local state with new data
      if (orderItem) {
        setOrderItem({ ...orderItem, ...editForm } as OrderItem);
      }
      setIsEditing(false);
    } catch (err) {
      setError('Failed to update order item');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    const confirmed = window.confirm('Are you sure you want to delete this order item?');
    if (!confirmed) return;

    try {
      setLoading(true);
      // In a real application, this would be an API call
      // await fetch(`/api/order-items/${id}`, { method: 'DELETE' });

      // Redirect to order details after deletion
      navigate(`/orders/${orderItem?.orderId}`);
    } catch (err) {
      setError('Failed to delete order item');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: name === 'quantity' || name === 'unitPrice' ? Number(value) : value
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error: </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  if (!orderItem) {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Not Found: </strong>
        <span className="block sm:inline">Order item not found</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Order Item Details</h1>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                aria-label="Save changes"
                disabled={loading}
              >
                Save
              </button>
              <button
                onClick={handleCancelEdit}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
                aria-label="Cancel editing"
                disabled={loading}
              >
                Cancel
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleEdit}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                aria-label="Edit order item"
                disabled={loading}
              >
                Edit
              </button>
              <button
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                aria-label="Delete order item"
                disabled={loading}
              >
                Delete
              </button>
            </>
          )}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Order Item Information</h2>
        </div>

        <div className="px-6 py-4">
          {isEditing ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                    Product Name
                  </label>
                  <input
                    type="text"
                    id="productName"
                    name="productName"
                    value={editForm.productName || ''}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    aria-required="true"
                  />
                </div>

                <div>
                  <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity
                  </label>
                  <input
                    type="number"
                    id="quantity"
                    name="quantity"
                    value={editForm.quantity || ''}
                    onChange={handleInputChange}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    aria-required="true"
                  />
                </div>

                <div>
                  <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 mb-1">
                    Unit Price ($)
                  </label>
                  <input
                    type="number"
                    id="unitPrice"
                    name="unitPrice"
                    value={editForm.unitPrice || ''}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    aria-required="true"
                  />
                </div>

                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={editForm.status || ''}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    aria-required="true"
                  >
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Product Name</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{orderItem.productName}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Quantity</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{orderItem.quantity}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Unit Price</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">${orderItem.unitPrice.toFixed(2)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Price</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">${orderItem.totalPrice.toFixed(2)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  orderItem.status === 'completed' ? 'bg-green-100 text-green-800' :
                  orderItem.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  orderItem.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {orderItem.status.charAt(0).toUpperCase() + orderItem.status.slice(1).replace('_', ' ')}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {product && (
        <div className="mt-6 bg-white shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800">Product Information</h2>
          </div>

          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Product ID</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{product.id}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Product Name</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{product.name}</p>
              </div>

              <div className="md:col-span-2">
                <h3 className="text-sm font-medium text-gray-500">Description</h3>
                <p className="mt-1 text-lg text-gray-900">{product.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Price</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">${product.price.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {order && (
        <div className="mt-6 bg-white shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800">Order Information</h2>
          </div>

          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Order ID</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{order.id}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Order Number</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{order.orderNumber}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Customer ID</h3>
                <p className="mt-1 text-lg font-medium text-gray-900">{order.customerId}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  order.status === 'completed' ? 'bg-green-100 text-green-800' :
                  order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  order.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('_', ' ')}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderItemDetail;