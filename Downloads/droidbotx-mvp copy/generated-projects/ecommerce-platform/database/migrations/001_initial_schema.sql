CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Product table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL CHECK (price >= 0),
    sku VARCHAR(100) UNIQUE NOT NULL,
    inventory_count INTEGER NOT NULL DEFAULT 0 CHECK (inventory_count >= 0),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- ProductCategory table
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Junction table for Product-Category relationship
CREATE TABLE product_category_assignments (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID REFERENCES product_categories(id) ON DELETE CASCADE,
    PRIMARY KEY (product_id, category_id)
);

-- User table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- ShoppingCart table
CREATE TABLE shopping_carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- CartItem table
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cart_id UUID REFERENCES shopping_carts(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(cart_id, product_id)
);

-- Order table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    total_amount DECIMAL(10, 2) NOT NULL CHECK (total_amount >= 0),
    shipping_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- OrderItem table
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price_per_unit DECIMAL(10, 2) NOT NULL CHECK (price_per_unit >= 0),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_product_categories_name ON product_categories(name);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_shopping_carts_user_id ON shopping_carts(user_id);
CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX idx_cart_items_product_id ON cart_items(product_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- RLS Policies (if needed)
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Sample data for testing
INSERT INTO product_categories (name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Clothing', 'Apparel and fashion items'),
('Books', 'Physical and digital books');

INSERT INTO products (name, description, price, sku, inventory_count) VALUES
('Smartphone', 'Latest model smartphone', 699.99, 'SMRT-001', 50),
('Laptop', 'High-performance laptop', 1299.99, 'LPTP-001', 25),
('T-Shirt', 'Cotton t-shirt', 19.99, 'TSHIRT-001', 100);

INSERT INTO product_category_assignments (product_id, category_id)
SELECT p.id, pc.id
FROM products p, product_categories pc
WHERE p.sku = 'SMRT-001' AND pc.name = 'Electronics';

INSERT INTO product_category_assignments (product_id, category_id)
SELECT p.id, pc.id
FROM products p, product_categories pc
WHERE p.sku = 'LPTP-001' AND pc.name = 'Electronics';

INSERT INTO product_category_assignments (product_id, category_id)
SELECT p.id, pc.id
FROM products p, product_categories pc
WHERE p.sku = 'TSHIRT-001' AND pc.name = 'Clothing';

INSERT INTO users (email, password_hash, first_name, last_name, is_admin) VALUES
('<EMAIL>', crypt('admin123', gen_salt('bf')), 'Admin', 'User', true),
('<EMAIL>', crypt('customer123', gen_salt('bf')), 'John', 'Doe', false);

INSERT INTO shopping_carts (user_id)
SELECT id FROM users WHERE email = '<EMAIL>';

INSERT INTO cart_items (cart_id, product_id, quantity)
SELECT sc.id, p.id, 2
FROM shopping_carts sc
JOIN users u ON sc.user_id = u.id
JOIN products p ON p.sku = 'SMRT-001'
WHERE u.email = '<EMAIL>';

INSERT INTO orders (user_id, status, total_amount, shipping_address)
SELECT id, 'processing', 1439.97, '123 Main St, City, State 12345'
FROM users WHERE email = '<EMAIL>';

INSERT INTO order_items (order_id, product_id, quantity, price_per_unit)
SELECT o.id, p.id, 1, p.price
FROM orders o
JOIN users u ON o.user_id = u.id
JOIN products p ON p.sku = 'LPTP-001'
WHERE u.email = '<EMAIL>';

INSERT INTO order_items (order_id, product_id, quantity, price_per_unit)
SELECT o.id, p.id, 1, p.price
FROM orders o
JOIN users u ON o.user_id = u.id
JOIN products p ON p.sku = 'SMRT-001'
WHERE u.email = '<EMAIL>';