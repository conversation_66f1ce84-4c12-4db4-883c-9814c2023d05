{"name": "taskflow-pro-backend", "version": "1.0.0", "description": "Backend for TaskFlow Pro - A task management and workflow application", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typeorm": "typeorm-ts-node-commonjs"}, "keywords": ["task-management", "workflow", "productivity", "backend", "express", "typescript"], "author": "TaskFlow Pro Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "pg": "^8.11.3", "typeorm": "^0.3.17", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "express-rate-limit": "^6.10.0", "winston": "^3.10.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "uuid": "^9.0.0", "moment": "^2.29.4", "socket.io": "^4.7.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.9", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.14", "@types/uuid": "^9.0.3", "@types/compression": "^1.7.2", "typescript": "^5.2.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.6.4", "supertest": "^6.3.3", "@types/jest": "^29.5.4", "@types/supertest": "^2.0.12", "eslint": "^8.48.0", "eslint-config-standard-with-typescript": "^39.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.0.2", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}