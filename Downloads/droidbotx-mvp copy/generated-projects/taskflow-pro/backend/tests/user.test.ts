









import { UserController } from '../src/controllers/UserController.js';
import { NotFoundError } from '../src/errors/NotFoundError.js';
import { UnauthorizedError } from '../src/errors/UnauthorizedError.js';
import { ValidationError } from '../src/errors/ValidationError.js';
import { AuthMiddleware } from '../src/middleware/auth.js';
import { User, UserRole } from '../src/models/User.js';
import { UserService } from '../src/services/UserService.js';
import express from 'express';
import request from 'supertest';

I'll create comprehensive Jest tests for a User entity. Since the application type is undefined, I'll create a generic user management system with common operations and business rules.


// __tests__/user.test.ts









// Mock dependencies
jest.mock('../src/services/UserService');
jest.mock('../src/middleware/auth');

const mockUserService = UserService as jest.Mocked<typeof UserService>;
const mockAuthMiddleware = AuthMiddleware as jest.Mocked<typeof AuthMiddleware>;

describe('User Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup express app for route testing
    app = express();
    app.use(express.json());
    
    // Mock auth middleware
    (mockAuthMiddleware.authenticate as jest.Mock) = jest.fn((req, res, next) => next());
    (mockAuthMiddleware.authorize as jest.Mock) = jest.fn((roles) => (req, res, next) => next());
    
    // Setup routes
    const userController = new UserController();
    app.get('/api/users', mockAuthMiddleware.authenticate, userController.getAllUsers);
    app.get('/api/users/:id', mockAuthMiddleware.authenticate, userController.getUserById);
    app.post('/api/users', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([UserRole.ADMIN]), userController.createUser);
    app.put('/api/users/:id', mockAuthMiddleware.authenticate, userController.updateUser);
    app.delete('/api/users/:id', mockAuthMiddleware.authenticate, mockAuthMiddleware.authorize([UserRole.ADMIN]), userController.deleteUser);
  });

  describe('API Endpoints', () => {
    describe('GET /api/users', () => {
      it('should return all users when authenticated', async () => {
        const mockUsers = [;
          { id: 1, email: '<EMAIL>', name: 'User 1', role: UserRole.USER },
          { id: 2, email: '<EMAIL>', name: 'User 2', role: UserRole.USER }
        ];
        
        mockUserService.getAllUsers.mockResolvedValue(mockUsers);

        const response = await request(app);
          .get('/api/users')
          .set('Authorization', 'Bearer valid-token')
          .expect(200);

        expect(response.body).toEqual(mockUsers);
        expect(mockUserService.getAllUsers).toHaveBeenCalled();
      });

      it('should return 401 when not authenticated', async () => {
        (mockAuthMiddleware.authenticate as jest.Mock).mockImplementationOnce((req, res, next) => {
          return res.status(401).json({ message: 'Unauthorized' });
        });

        await request(app)
          .get('/api/users')
          .expect(401);
      });
    });

    describe('GET /api/users/:id', () => {
      it('should return user by id when authenticated', async () => {
        const mockUser = { id: 1, email: '<EMAIL>', name: 'Test User', role: UserRole.USER };
        mockUserService.getUserById.mockResolvedValue(mockUser);

        const response = await request(app);
          .get('/api/users/1')
          .set('Authorization', 'Bearer valid-token')
          .expect(200);

        expect(response.body).toEqual(mockUser);
        expect(mockUserService.getUserById).toHaveBeenCalledWith(1);
      });

      it('should return 404 when user not found', async () => {
        mockUserService.getUserById.mockRejectedValue(new NotFoundError('User not found'));

        await request(app)
          .get('/api/users/999')
          .set('Authorization', 'Bearer valid-token')
          .expect(404);
      });
    });

    describe('POST /api/users', () => {
      it('should create a new user when authorized as admin', async () => {
        const newUser = { email: '<EMAIL>', name: 'New User', password: 'password123', role: UserRole.USER };
        const createdUser = { id: 1, ...newUser, password: undefined };
        
        mockUserService.createUser.mockResolvedValue(createdUser);

        const response = await request(app);
          .post('/api/users')
          .set('Authorization', 'Bearer admin-token')
          .send(newUser)
          .expect(201);

        expect(response.body).toEqual(createdUser);
        expect(mockUserService.createUser).toHaveBeenCalledWith(newUser);
      });

      it('should return 400 when validation fails', async () => {
        const invalidUser = { email: 'invalid-email', name: '', password: '123' };
        mockUserService.createUser.mockRejectedValue(new ValidationError('Validation failed'));

        await request(app)
          .post('/api/users')
          .set('Authorization', 'Bearer admin-token')
          .send(invalidUser)
          .expect(400);
      });

      it('should return 403 when not authorized as admin', async () => {
        (mockAuthMiddleware.authorize as jest.Mock).mockImplementationOnce(() => (req, res, next) => {
          return res.status(403).json({ message: 'Forbidden' });
        });

        await request(app)
          .post('/api/users')
          .set('Authorization', 'Bearer user-token')
          .send({ email: '<EMAIL>', name: 'User', password: 'password' })
          .expect(403);
      });
    });

    describe('PUT /api/users/:id', () => {
      it('should update user when authenticated', async () => {
        const updatedUser = { id: 1, email: '<EMAIL>', name: 'Updated User', role: UserRole.USER };
        mockUserService.updateUser.mockResolvedValue(updatedUser);

        const response = await request(app);
          .put('/api/users/1')
          .set('Authorization', 'Bearer valid-token')
          .send({ name: 'Updated User' })
          .expect(200);

        expect(response.body).toEqual(updatedUser);
        expect(mockUserService.updateUser).toHaveBeenCalledWith(1, { name: 'Updated User' });
      });

      it('should return 400 when trying to update with invalid data', async () => {
        mockUserService.updateUser.mockRejectedValue(new ValidationError('Invalid email'));

        await request(app)
          .put('/api/users/1')
          .set('Authorization', 'Bearer valid-token')
          .send({ email: 'invalid-email' })
          .expect(400);
      });
    });

    describe('DELETE /api/users/:id', () => {
      it('should delete user when authorized as admin', async () => {
        mockUserService.deleteUser.mockResolvedValue();

        await request(app)
          .delete('/api/users/1')
          .set('Authorization', 'Bearer admin-token')
          .expect(204);

        expect(mockUserService.deleteUser).toHaveBeenCalledWith(1);
      });

      it('should return 403 when not authorized as admin', async () => {
        (mockAuthMiddleware.authorize as jest.Mock).mockImplementationOnce(() => (req, res, next) => {
          return res.status(403).json({ message: 'Forbidden' });
        });

        await request(app)
          .delete('/api/users/1')
          .set('Authorization', 'Bearer user-token')
          .expect(403);
      });

      it('should return 404 when user not found', async () => {
        mockUserService.deleteUser.mockRejectedValue(new NotFoundError('User not found'));

        await request(app)
          .delete('/api/users/999')
          .set('Authorization', 'Bearer admin-token')
          .expect(404);
      });
    });
  });

  describe('Service Layer', () => {
    let userService: UserService;

    beforeEach(() => {
      userService = new UserService();
    });

    describe('getAllUsers', () => {
      it('should return all users from repository', async () => {
        const mockUsers = [;
          new User(1, '<EMAIL>', 'User 1', UserRole.USER),
          new User(2, '<EMAIL>', 'User 2', UserRole.ADMIN)
        ];
        
        // Mock User.findAll method
        (User.findAll as jest.Mock) = jest.fn().mockResolvedValue(mockUsers);

        const result = await userService.getAllUsers();
        expect(result).toEqual(mockUsers);
      });
    });

    describe('getUserById', () => {
      it('should return user when found', async () => {
        const mockUser = new User(1, '<EMAIL>', 'Test User', UserRole.USER);
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(mockUser);

        const result = await userService.getUserById(1);
        expect(result).toEqual(mockUser);
      });

      it('should throw NotFoundError when user not found', async () => {
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(null);

        await expect(userService.getUserById(999)).rejects.toThrow(NotFoundError);
      });
    });

    describe('createUser', () => {
      it('should create and return new user', async () => {
        const userData = {
          email: '<EMAIL>',
          name: 'New User',
          password: 'password123',
          role: UserRole.USER
        };
        
        const createdUser = new User(1, userData.email, userData.name, userData.role);
        (User.create as jest.Mock) = jest.fn().mockResolvedValue(createdUser);

        const result = await userService.createUser(userData);
        expect(result).toEqual(createdUser);
        expect(User.create).toHaveBeenCalledWith(userData);
      });

      it('should throw ValidationError for invalid email', async () => {
        const invalidData = {
          email: 'invalid-email',
          name: 'New User',
          password: 'password123'
        };

        await expect(userService.createUser(invalidData)).rejects.toThrow(ValidationError);
      });

      it('should throw ValidationError for short password', async () => {
        const invalidData = {
          email: '<EMAIL>',
          name: 'New User',
          password: '123'
        };

        await expect(userService.createUser(invalidData)).rejects.toThrow(ValidationError);
      });
    });

    describe('updateUser', () => {
      it('should update and return user', async () => {
        const updateData = { name: 'Updated Name' };
        const existingUser = new User(1, '<EMAIL>', 'Old Name', UserRole.USER);
        const updatedUser = new User(1, '<EMAIL>', 'Updated Name', UserRole.USER);
        
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(existingUser);
        (User.update as jest.Mock) = jest.fn().mockResolvedValue(updatedUser);

        const result = await userService.updateUser(1, updateData);
        expect(result).toEqual(updatedUser);
      });

      it('should throw NotFoundError when user not found', async () => {
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(null);

        await expect(userService.updateUser(999, { name: 'New Name' })).rejects.toThrow(NotFoundError);
      });
    });

    describe('deleteUser', () => {
      it('should delete user successfully', async () => {
        const existingUser = new User(1, '<EMAIL>', 'Test User', UserRole.USER);
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(existingUser);
        (User.delete as jest.Mock) = jest.fn().mockResolvedValue();

        await expect(userService.deleteUser(1)).resolves.toBeUndefined();
        expect(User.delete).toHaveBeenCalledWith(1);
      });

      it('should throw NotFoundError when user not found', async () => {
        (User.findById as jest.Mock) = jest.fn().mockResolvedValue(null);

        await expect(userService.deleteUser(999)).rejects.toThrow(NotFoundError);
      });
    });
  });

  describe('Data Model Validation', () => {
    describe('User Model', () => {
      it('should create user with valid data', () => {
        const user = new User(1, '<EMAIL>', 'Test User', UserRole.USER);
        expect(user.id).toBe(1);
        expect(user.email).toBe('<EMAIL>');
        expect(user.name).toBe('Test User');
        expect(user.role).toBe(UserRole.USER);
      });

      it('should throw error for invalid email', () => {
        expect(() => {
          new User(1, 'invalid-email', 'Test User', UserRole.USER);
        }).toThrow(ValidationError);
      });

      it('should throw error for empty name', () => {
        expect(() => {
          new User(1, '<EMAIL>', '', UserRole.USER);
        }).toThrow(ValidationError);
      });

      it('should throw error for invalid role', () => {
        expect(() => {
          // @ts-ignore - Testing invalid role
          new User(1, '<EMAIL>', 'Test User', 'INVALID_ROLE');
        }).toThrow(ValidationError);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const userService = new UserService();
      (User.findAll as jest.Mock) = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(userService.getAllUsers()).rejects.toThrow('Database error');
    });

    it('should handle unexpected errors in service layer', async () => {
      const userService = new UserService();
      (User.findById as jest.Mock) = jest.fn().mockRejectedValue(new Error('Unexpected error'));

      await expect(userService.getUserById(1)).rejects.toThrow('Unexpected error');
    });
  });

  describe('Authentication and Authorization', () => {
    it('should deny access to protected routes without;'