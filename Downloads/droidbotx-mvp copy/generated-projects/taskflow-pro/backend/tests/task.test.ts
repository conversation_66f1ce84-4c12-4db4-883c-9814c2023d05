






import { db } from '../src/config/database.js';
import { TaskModel } from '../src/models/taskModel.js';
import { AuthService } from '../src/services/AuthService';
import { TaskService } from '../src/services/TaskService';
import request from 'supertest';
import app from '../src/app.js';

Here's a comprehensive Jest test suite for the Task entity in a task management application:'


// __tests__/task.test.ts






// Mock dependencies
jest.mock('../src/services/taskService');
jest.mock('../src/services/authService');
jest.mock('../src/models/taskModel');
jest.mock('../src/config/database');

describe('Task Management Tests', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    role: 'user',
    email: '<EMAIL>'
  };

  const mockAdminUser = {
    id: 2,
    username: 'admin',
    role: 'admin',
    email: '<EMAIL>'
  };

  const mockTask = {
    id: 1,
    title: 'Test Task',
    description: 'Test Description',
    status: 'pending',
    priority: 'medium',
    dueDate: new Date('2023-12-31'),
    userId: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Endpoints', () => {
    describe('GET /api/tasks', () => {
      it('should return all tasks for admin users', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockAdminUser);
        (TaskService.getAllTasks as jest.Mock).mockResolvedValue([mockTask]);

        const response = await request(app);
          .get('/api/tasks')
          .set('Authorization', 'Bearer valid-token');

        expect(response.status).toBe(200);
        expect(response.body).toHaveLength(1);
        expect(TaskService.getAllTasks).toHaveBeenCalled();
      });

      it('should return user-specific tasks for regular users', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.getUserTasks as jest.Mock).mockResolvedValue([mockTask]);

        const response = await request(app);
          .get('/api/tasks')
          .set('Authorization', 'Bearer valid-token');

        expect(response.status).toBe(200);
        expect(response.body).toHaveLength(1);
        expect(TaskService.getUserTasks).toHaveBeenCalledWith(1);
      });

      it('should handle authentication errors', async () => {
        (AuthService.authenticate as jest.Mock).mockRejectedValue(new Error('Invalid token'));

        const response = await request(app);
          .get('/api/tasks')
          .set('Authorization', 'Bearer invalid-token');

        expect(response.status).toBe(401);
      });
    });

    describe('POST /api/tasks', () => {
      const newTaskData = {
        title: 'New Task',
        description: 'New Task Description',
        priority: 'high',
        dueDate: '2023-12-31'
      };

      it('should create a new task successfully', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.createTask as jest.Mock).mockResolvedValue({
          ...newTaskData,
          id: 2,
          userId: 1,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        });

        const response = await request(app);
          .post('/api/tasks')
          .set('Authorization', 'Bearer valid-token')
          .send(newTaskData);

        expect(response.status).toBe(201);
        expect(response.body.title).toBe(newTaskData.title);
        expect(TaskService.createTask).toHaveBeenCalledWith({
          ...newTaskData,
          userId: 1
        });
      });

      it('should handle validation errors', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);

        const response = await request(app);
          .post('/api/tasks')
          .set('Authorization', 'Bearer valid-token')
          .send({ title: '' }); // Invalid data

        expect(response.status).toBe(400);
      });
    });

    describe('PUT /api/tasks/:id', () => {
      it('should update a task successfully', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.updateTask as jest.Mock).mockResolvedValue({
          ...mockTask,
          title: 'Updated Task'
        });

        const response = await request(app);
          .put('/api/tasks/1')
          .set('Authorization', 'Bearer valid-token')
          .send({ title: 'Updated Task' });

        expect(response.status).toBe(200);
        expect(response.body.title).toBe('Updated Task');
        expect(TaskService.updateTask).toHaveBeenCalledWith(1, { title: 'Updated Task' }, 1);
      });

      it('should prevent users from updating others tasks', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.updateTask as jest.Mock).mockRejectedValue(new Error('Unauthorized'));

        const response = await request(app);
          .put('/api/tasks/999')
          .set('Authorization', 'Bearer valid-token')
          .send({ title: 'Updated Task' });

        expect(response.status).toBe(403);
      });
    });

    describe('DELETE /api/tasks/:id', () => {
      it('should delete a task successfully', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.deleteTask as jest.Mock).mockResolvedValue(true);

        const response = await request(app);
          .delete('/api/tasks/1')
          .set('Authorization', 'Bearer valid-token');

        expect(response.status).toBe(204);
        expect(TaskService.deleteTask).toHaveBeenCalledWith(1, 1);
      });

      it('should prevent deletion of non-existent tasks', async () => {
        (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);
        (TaskService.deleteTask as jest.Mock).mockResolvedValue(false);

        const response = await request(app);
          .delete('/api/tasks/999')
          .set('Authorization', 'Bearer valid-token');

        expect(response.status).toBe(404);
      });
    });
  });

  describe('Service Layer', () => {
    describe('createTask', () => {
      it('should create a task with valid data', async () => {
        const taskData = {
          title: 'Service Task',
          description: 'Service Description',
          priority: 'medium',
          userId: 1
        };

        (TaskModel.create as jest.Mock).mockResolvedValue({
          ...taskData,
          id: 1,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        });

        const result = await TaskService.createTask(taskData);

        expect(result).toHaveProperty('id');
        expect(result.title).toBe(taskData.title);
        expect(TaskModel.create).toHaveBeenCalledWith({
          ...taskData,
          status: 'pending'
        });
      });

      it('should reject tasks with missing title', async () => {
        await expect(TaskService.createTask({
          description: 'No title',
          userId: 1
        } as any)).rejects.toThrow('Title is required');
      });
    });

    describe('updateTask', () => {
      it('should update task status correctly', async () => {
        const existingTask = { ...mockTask };
        (TaskModel.findByPk as jest.Mock).mockResolvedValue(existingTask);
        (TaskModel.update as jest.Mock).mockResolvedValue([1]);

        const result = await TaskService.updateTask(1, { status: 'completed' }, 1);

        expect(result.status).toBe('completed');
        expect(TaskModel.update).toHaveBeenCalledWith(;
          { status: 'completed' },
          { where: { id: 1, userId: 1 } }
        );
      });

      it('should prevent updating completed tasks', async () => {
        (TaskModel.findByPk as jest.Mock).mockResolvedValue({
          ...mockTask,
          status: 'completed'
        });

        await expect(TaskService.updateTask(1, { title: 'Updated' }, 1))
          .rejects.toThrow('Cannot modify completed tasks');
      });
    });

    describe('getUserTasks', () => {
      it('should return tasks for a specific user', async () => {
        (TaskModel.findAll as jest.Mock).mockResolvedValue([mockTask]);

        const result = await TaskService.getUserTasks(1);

        expect(result).toHaveLength(1);
        expect(result[0].userId).toBe(1);
        expect(TaskModel.findAll).toHaveBeenCalledWith({
          where: { userId: 1 }
        });
      });
    });
  });

  describe('Data Model Validation', () => {
    it('should validate required fields', async () => {
      await expect(TaskModel.create({
        description: 'Missing title'
      } as any)).rejects.toThrow();
    });

    it('should validate status values', async () => {
      await expect(TaskModel.create({
        title: 'Test Task',
        status: 'invalid-status'
      } as any)).rejects.toThrow();
    });

    it('should validate priority values', async () => {
      await expect(TaskModel.create({
        title: 'Test Task',
        priority: 'invalid-priority'
      } as any)).rejects.toThrow();
    });

    it('should validate due date is in future', async () => {
      await expect(TaskModel.create({
        title: 'Test Task',
        dueDate: new Date('2020-01-01')
      } as any)).rejects.toThrow('Due date must be in the future');
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      (db.authenticate as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await expect(TaskService.getAllTasks()).rejects.toThrow('Database connection error');
    });

    it('should handle task not found errors', async () => {
      (TaskModel.findByPk as jest.Mock).mockResolvedValue(null);

      await expect(TaskService.updateTask(999, { title: 'Update' }, 1))
        .rejects.toThrow('Task not found');
    });

    it('should handle unauthorized access', async () => {
      (TaskModel.findByPk as jest.Mock).mockResolvedValue({
        ...mockTask,
        userId: 2 // Different user
      });

      await expect(TaskService.updateTask(1, { title: 'Update' }, 1))
        .rejects.toThrow('Unauthorized');
    });
  });

  describe('Authentication and Authorization', () => {
    it('should allow admin to access all tasks', async () => {
      (AuthService.authenticate as jest.Mock).mockResolvedValue(mockAdminUser);
      (TaskService.getAllTasks as jest.Mock).mockResolvedValue([mockTask]);

      const response = await request(app);
        .get('/api/tasks')
        .set('Authorization', 'Bearer admin-token');

      expect(response.status).toBe(200);
      expect(TaskService.getAllTasks).toHaveBeenCalled();
    });

    it('should prevent regular users from accessing all tasks', async () => {
      (AuthService.authenticate as jest.Mock).mockResolvedValue(mockUser);

      const response = await request(app);
        .get('/api/tasks?all=true')
        .set('Authorization', 'Bearer user-token');

      expect(response.status).toBe(403);
    });

    it('should reject requests without authentication', async () => {
      const response = await request(app).get('/api/tasks');
      expect(response.status).toBe(401);
    });

    it('should reject expired tokens', async () => {
      (AuthService.authenticate as jest.Mock).mockRejectedValue(new Error('Token expired'));

      const response = await request(app);
        .get('/api/tasks')
        .set('Authorization', 'Bearer expired-token');

      expect(response.status).toBe(401);
    });
  });
});
`

This test suite covers:

1. **API Endpoints Testing**:
   - GET /api/tasks (different behavior for admin vs regular users)
   - POST /api/tasks (creation with validation)
   - PUT /api/tasks/:id (updates with authorization)
   - DELETE /api/tasks/:id (deletion with ownership check)

2. **Service Layer Testing**:
   - Task creation with validation rules
   - Task updates with business logic constraints
   - User-specific task retrieval
   - Error scenarios handling

3. **Data Model Validation**:
   - Required field validation
   - Status/priority enum validation
   - Business rule validation (due dates)

4. **Error Handling**:
   - Database connection failures
   - Task not found scenarios
   - Unauthorized access attempts

5. **Authentication and Authorization**:
   - Token validation
   - Role-based access control
   - Ownership verification
   - Admin vs user permissions

The tests use proper mocking for dependencies and cover both success and error scenarios. Each test focuses on specific business rules and edge cases relevant to a task management system.