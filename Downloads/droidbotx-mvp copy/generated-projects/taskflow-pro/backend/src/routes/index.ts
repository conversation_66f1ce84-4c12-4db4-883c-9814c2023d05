```typescript
import express, { Request, Response, NextFunction } from 'express';
import taskRoutes from './tasks.routes';
import userRoutes from './users.routes';

const router = express.Router();

// Middleware
router.use(express.json());
router.use(express.urlencoded({ extended: true }));

// Logging middleware
router.use((req: Request, res: Response, next: NextFunction) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Authentication middleware
router.use((req: Request, res: Response, next: NextFunction) => {
  // TODO: Implement proper authentication logic
  // Example: Check for valid JWT token
  // const token = req.headers.authorization?.split(' ')[1];
  // if (!token) return res.status(401).json({ error: 'Unauthorized' });
  next();
});

// Route mounting
router.use('/api/tasks', taskRoutes);
router.use('/api/users', userRoutes);

// Health check endpoint
router.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
router.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
router.use('*', (req: Request, res: Response) => {
  res.status(404).json({ error: 'Route not found' });
});

export default router;
```