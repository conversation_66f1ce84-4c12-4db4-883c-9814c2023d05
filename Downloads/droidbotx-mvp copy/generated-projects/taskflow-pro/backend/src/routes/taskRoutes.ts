// Auto-generated standardized service
// Pattern: Express Route Service








import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';
import { taskRoutes } from '../services/TaskRoutes';
import { logger } from '../core/Logger.js';
import { body, validationResult } from 'express-validator';
import { Pool } from 'pg';
import jwt from 'jsonwebtoken';

const router = Router();
const taskRoutes = new taskRoutes();

router.use(authenticateToken);
router.use(validateRequest);






interface Task {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  due_date: Date;
  created_at: Date;
  updated_at: Date;
}

interface AuthRequest extends Request {
  user?: {
    id: number;
    role: string;
  };
}

const taskRoutes = Router();
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Authentication middleware
const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user as { id: number; role: string };
    next();
  });
};

// Authorization middleware
const authorizeRole = (roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Validation rules
const validateTask = [
  body('title').isLength({ min: 1, max: 255 }).withMessage('Title is required and must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('status').optional().isIn(['pending', 'in-progress', 'completed']).withMessage('Invalid status'),
  body('priority').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid priority'),
  body('due_date').optional().isISO8601().withMessage('Invalid date format'),
];

// GET /tasks - Get all tasks
taskRoutes.get('/', authenticateToken, async (req: AuthRequest, res: Response) => {
  try {
    const client = await pool.connect();
    try {
      const query = `
        SELECT * FROM tasks
        WHERE user_id = $1
        ORDER BY created_at DESC
      `;
      const result = await client.query(query, [req.user?.id]);
      res.json(result.rows);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /tasks/:id - Get a specific task
taskRoutes.get('/:id', authenticateToken, async (req: AuthRequest, res: Response) => {
  const taskId = parseInt(req.params.id, 10);

  if (isNaN(taskId)) {
    return res.status(400).json({ error: 'Invalid task ID' });
  }

  try {
    const client = await pool.connect();
    try {
      const query = `
        SELECT * FROM tasks
        WHERE id = $1 AND user_id = $2
      `;
      const result = await client.query(query, [taskId, req.user?.id]);

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Task not found' });
      }

      res.json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /tasks - Create a new task
taskRoutes.post('/', authenticateToken, validateTask, async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { title, description, status, priority, due_date } = req.body;

  try {
    const client = await pool.connect();
    try {
      const query = `
        INSERT INTO tasks (title, description, status, priority, due_date, user_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        RETURNING *
      `;
      const values = [
        title,
        description || '',
        status || 'pending',
        priority || 'medium',
        due_date ? new Date(due_date) : null,
        req.user?.id
      ];

      const result = await client.query(query, values);
      res.status(201).json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /tasks/:id - Update a task
taskRoutes.put('/:id', authenticateToken, validateTask, async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const taskId = parseInt(req.params.id, 10);
  if (isNaN(taskId)) {
    return res.status(400).json({ error: 'Invalid task ID' });
  }

  const { title, description, status, priority, due_date } = req.body;

  try {
    const client = await pool.connect();
    try {
      // Check if task exists and belongs to user
      const checkQuery = `
        SELECT id FROM tasks
        WHERE id = $1 AND user_id = $2
      `;
      const checkResult = await client.query(checkQuery, [taskId, req.user?.id]);

      if (checkResult.rows.length === 0) {
        return res.status(404).json({ error: 'Task not found' });
      }

      const query = `
        UPDATE tasks
        SET title = $1, description = $2, status = $3, priority = $4, due_date = $5, updated_at = NOW()
        WHERE id = $6 AND user_id = $7
        RETURNING *
      `;
      const values = [
        title,
        description || '',
        status || 'pending',
        priority || 'medium',
        due_date ? new Date(due_date) : null,
        taskId,
        req.user?.id
      ];

      const result = await client.query(query, values);
      res.json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /tasks/:id - Delete a task
taskRoutes.delete('/:id', authenticateToken, async (req: AuthRequest, res: Response) => {
  const taskId = parseInt(req.params.id, 10);

  if (isNaN(taskId)) {
    return res.status(400).json({ error: 'Invalid task ID' });
  }

  try {
    const client = await pool.connect();
    try {
      // Check if task exists and belongs to user
      const checkQuery = `
        SELECT id FROM tasks
        WHERE id = $1 AND user_id = $2
      `;
      const checkResult = await client.query(checkQuery, [taskId, req.user?.id]);

      if (checkResult.rows.length === 0) {
        return res.status(404).json({ error: 'Task not found' });
      }

      const query = 'DELETE FROM tasks WHERE id = $1 AND user_id = $2';
      await client.query(query, [taskId, req.user?.id]);

      res.status(204).send();
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default taskRoutes;


const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};

// Logging utilities

logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});

export default taskRoutes;