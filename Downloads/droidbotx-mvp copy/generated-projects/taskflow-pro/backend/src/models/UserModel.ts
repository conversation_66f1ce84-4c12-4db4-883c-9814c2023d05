




import { Pool, QueryResult } from 'pg';
import { Logger, createLogger } from 'winston';
import { BusinessError, ValidationError } from '../errors/CustomErrors.js';
import { validateEmail, validatePassword } from '../utils/validation.js';

export interface User {
  id?: number;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UserCreationAttributes {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface UserAuthenticationAttributes {
  email: string;
  password: string;
}

export class UserModel {
  private logger: Logger;

  constructor(
    private db: Pool
  ) {
    this.logger = createLogger({
      defaultMeta: { service: 'UserModel' }
    });
  }

  async create(userData: UserCreationAttributes): Promise<User> {
    this.logger.info('Creating new user', { email: userData.email });

    try {
      this.validateUserData(userData);

      const existingUser = await this.findByEmail(userData.email);
      if (existingUser) {
        throw new ValidationError('Email already exists');
      }

      const query = `
        INSERT INTO users (email, password, first_name, last_name, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id, email, first_name, last_name, created_at, updated_at
      `;

      const values = [
        userData.email,
        userData.password,
        userData.firstName,
        userData.lastName
      ];

      const result: QueryResult<User> = await this.db.query(query, values);

      if (result.rowCount === 0) {
        throw new BusinessError('Failed to create user');
      }

      this.logger.info('User created successfully', { userId: result.rows[0].id });
      return result.rows[0];
    } catch (error) {
      this.logger.error('Error creating user', { error });
      if (error instanceof ValidationError || error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to create user');
    }
  }

  async findById(id: number): Promise<User | null> {
    this.logger.info('Finding user by ID', { id });

    try {
      const query = `
        SELECT id, email, first_name, last_name, created_at, updated_at
        FROM users
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const result: QueryResult<User> = await this.db.query(query, [id]);

      if (result.rowCount === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error('Error finding user by ID', { error, id });
      throw new BusinessError('Failed to retrieve user');
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    this.logger.info('Finding user by email', { email });

    try {
      const query = `
        SELECT id, email, password, first_name, last_name, created_at, updated_at
        FROM users
        WHERE email = $1 AND deleted_at IS NULL
      `;

      const result: QueryResult<User> = await this.db.query(query, [email]);

      if (result.rowCount === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error('Error finding user by email', { error, email });
      throw new BusinessError('Failed to retrieve user');
    }
  }

  async update(id: number, userData: Partial<User>): Promise<User> {
    this.logger.info('Updating user', { id });

    try {
      const existingUser = await this.findById(id);
      if (!existingUser) {
        throw new ValidationError('User not found');
      }

      const fields = [];
      const values = [];
      let paramCount = 1;

      if (userData.email) {
        this.validateEmail(userData.email);
        fields.push(`email = $${paramCount}`);
        values.push(userData.email);
        paramCount++;
      }

      if (userData.firstName) {
        fields.push(`first_name = $${paramCount}`);
        values.push(userData.firstName);
        paramCount++;
      }

      if (userData.lastName) {
        fields.push(`last_name = $${paramCount}`);
        values.push(userData.lastName);
        paramCount++;
      }

      if (fields.length === 0) {
        throw new ValidationError('No valid fields to update');
      }

      values.push(id); // Add ID for WHERE clause

      const query = `
        UPDATE users
        SET ${fields.join(', ')}, updated_at = NOW()
        WHERE id = $${paramCount} AND deleted_at IS NULL
        RETURNING id, email, first_name, last_name, created_at, updated_at
      `;

      const result: QueryResult<User> = await this.db.query(query, values);

      if (result.rowCount === 0) {
        throw new BusinessError('Failed to update user');
      }

      this.logger.info('User updated successfully', { userId: result.rows[0].id });
      return result.rows[0];
    } catch (error) {
      this.logger.error('Error updating user', { error, id });
      if (error instanceof ValidationError || error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('Failed to update user');
    }
  }

  async delete(id: number): Promise<boolean> {
    this.logger.info('Deleting user', { id });

    try {
      const query = `
        UPDATE users
        SET deleted_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rowCount === 0) {
        throw new ValidationError('User not found or already deleted');
      }

      this.logger.info('User deleted successfully', { id });
      return true;
    } catch (error) {
      this.logger.error('Error deleting user', { error, id });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Failed to delete user');
    }
  }

  async authenticate(credentials: UserAuthenticationAttributes): Promise<User> {
    this.logger.info('Authenticating user', { email: credentials.email });

    try {
      const user = await this.findByEmail(credentials.email);

      if (!user) {
        throw new ValidationError('Invalid credentials');
      }

      // In a real implementation, we would hash and compare passwords
      if (user.password !== credentials.password) {
        throw new ValidationError('Invalid credentials');
      }

      this.logger.info('User authenticated successfully', { userId: user.id });
      return user;
    } catch (error) {
      this.logger.error('Error authenticating user', { error });
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Authentication failed');
    }
  }

  private validateUserData(userData: UserCreationAttributes): void {
    this.validateEmail(userData.email);
    this.validatePassword(userData.password);

    if (!userData.firstName || userData.firstName.trim().length === 0) {
      throw new ValidationError('First name is required');
    }

    if (!userData.lastName || userData.lastName.trim().length === 0) {
      throw new ValidationError('Last name is required');
    }
  }

  private validateEmail(email: string): void {
    if (!validateEmail(email)) {
      throw new ValidationError('Invalid email format');
    }
  }

  private validatePassword(password: string): void {
    if (!validatePassword(password)) {
      throw new ValidationError('Password does not meet security requirements');
    }
  }
}