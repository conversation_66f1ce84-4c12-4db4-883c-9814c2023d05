```typescript
/**
 * User entity representing an authenticated user in the project management system
 * @interface User
 */
export interface User {
  /**
   * Unique identifier for the user
   * @type {string}
   * @required
   * @format UUID
   */
  id: string;

  /**
   * User's email address used for authentication
   * @type {string}
   * @required
   * @format email
   * @maxLength 255
   */
  email: string;

  /**
   * User's password for authentication
   * @type {string}
   * @required
   * @minLength 8
   * @maxLength 128
   * @pattern At least one uppercase, one lowercase, one number, and one special character
   */
  password: string;

  /**
   * User's full name
   * @type {string}
   * @required
   * @minLength 1
   * @maxLength 100
   */
  name: string;

  /**
   * Timestamp when the user record was created
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   */
  createdAt: string;

  /**
   * Timestamp when the user record was last updated
   * @type {string}
   * @required
   * @format ISO 8601 date-time
   */
  updatedAt: string;
}
```