





import { Pool, QueryResult } from 'pg';
import { DatabaseError } from '../errors/DatabaseError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { Task } from '../types/Task.js';
import { Logger } from '../utils/Logger.js';

export interface TaskRepository {
  create(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task>;
  findById(id: number): Promise<Task | null>;
  update(id: number, task: Partial<Task>): Promise<Task>;
  delete(id: number): Promise<boolean>;
  markAsComplete(id: number): Promise<Task>;
}

export class TaskModel implements TaskRepository {
  private readonly logger: Logger;

  constructor(
    private readonly db: Pool
  ) {
    this.logger = new Logger('TaskModel');
  }

  async create(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    this.logger.info('Creating new task', { title: taskData.title });

    // Validate required fields
    if (!taskData.title || taskData.title.trim().length === 0) {
      throw new ValidationError('Task title is required');
    }

    if (taskData.title.length > 255) {
      throw new ValidationError('Task title must be less than 255 characters');
    }

    if (taskData.description && taskData.description.length > 1000) {
      throw new ValidationError('Task description must be less than 1000 characters');
    }

    try {
      const query = `
        INSERT INTO tasks (title, description, status, due_date, project_id, assignee_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, title, description, status, due_date, project_id, assignee_id, created_at, updated_at
      `;

      const values = [
        taskData.title,
        taskData.description || null,
        taskData.status || 'pending',
        taskData.dueDate || null,
        taskData.projectId || null,
        taskData.assigneeId || null
      ];

      const result: QueryResult = await this.db.query(query, values);
      const task: Task = result.rows[0];

      this.logger.info('Task created successfully', { taskId: task.id });
      return task;
    } catch (error) {
      this.logger.error('Failed to create task', { error });
      throw new DatabaseError('Failed to create task');
    }
  }

  async findById(id: number): Promise<Task | null> {
    this.logger.info('Fetching task by ID', { taskId: id });

    if (!Number.isInteger(id) || id <= 0) {
      throw new ValidationError('Invalid task ID');
    }

    try {
      const query = `
        SELECT id, title, description, status, due_date, project_id, assignee_id, created_at, updated_at
        FROM tasks
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Task not found', { taskId: id });
        return null;
      }

      const task: Task = result.rows[0];
      this.logger.info('Task found', { taskId: task.id });
      return task;
    } catch (error) {
      this.logger.error('Failed to fetch task', { taskId: id, error });
      throw new DatabaseError('Failed to fetch task');
    }
  }

  async update(id: number, taskData: Partial<Task>): Promise<Task> {
    this.logger.info('Updating task', { taskId: id });

    if (!Number.isInteger(id) || id <= 0) {
      throw new ValidationError('Invalid task ID');
    }

    // Validate provided fields
    if (taskData.title !== undefined && taskData.title.trim().length === 0) {
      throw new ValidationError('Task title cannot be empty');
    }

    if (taskData.title && taskData.title.length > 255) {
      throw new ValidationError('Task title must be less than 255 characters');
    }

    if (taskData.description && taskData.description.length > 1000) {
      throw new ValidationError('Task description must be less than 1000 characters');
    }

    try {
      const fields: string[] = [];
      const values: any[] = [];
      let paramCount = 1;

      // Build dynamic update query
      Object.entries(taskData).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
          fields.push(`${key} = $${paramCount}`);
          values.push(value);
          paramCount++;
        }
      });

      if (fields.length === 0) {
        throw new ValidationError('No valid fields provided for update');
      }

      // Add updated_at timestamp
      fields.push(`updated_at = NOW()`);

      // Add ID to values for WHERE clause
      values.push(id);

      const query = `
        UPDATE tasks
        SET ${fields.join(', ')}
        WHERE id = $${paramCount} AND deleted_at IS NULL
        RETURNING id, title, description, status, due_date, project_id, assignee_id, created_at, updated_at
      `;

      const result: QueryResult = await this.db.query(query, values);

      if (result.rows.length === 0) {
        throw new ValidationError('Task not found or already deleted');
      }

      const task: Task = result.rows[0];
      this.logger.info('Task updated successfully', { taskId: task.id });
      return task;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to update task', { taskId: id, error });
      throw new DatabaseError('Failed to update task');
    }
  }

  async delete(id: number): Promise<boolean> {
    this.logger.info('Deleting task', { taskId: id });

    if (!Number.isInteger(id) || id <= 0) {
      throw new ValidationError('Invalid task ID');
    }

    try {
      // Soft delete by setting deleted_at timestamp
      const query = `
        UPDATE tasks
        SET deleted_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
        RETURNING id
      `;

      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rows.length === 0) {
        this.logger.info('Task not found or already deleted', { taskId: id });
        return false;
      }

      this.logger.info('Task deleted successfully', { taskId: id });
      return true;
    } catch (error) {
      this.logger.error('Failed to delete task', { taskId: id, error });
      throw new DatabaseError('Failed to delete task');
    }
  }

  async markAsComplete(id: number): Promise<Task> {
    this.logger.info('Marking task as complete', { taskId: id });

    if (!Number.isInteger(id) || id <= 0) {
      throw new ValidationError('Invalid task ID');
    }

    try {
      const query = `
        UPDATE tasks
        SET status = 'completed', updated_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
        RETURNING id, title, description, status, due_date, project_id, assignee_id, created_at, updated_at
      `;

      const result: QueryResult = await this.db.query(query, [id]);

      if (result.rows.length === 0) {
        throw new ValidationError('Task not found or already deleted');
      }

      const task: Task = result.rows[0];
      this.logger.info('Task marked as complete', { taskId: task.id });
      return task;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      this.logger.error('Failed to mark task as complete', { taskId: id, error });
      throw new DatabaseError('Failed to mark task as complete');
    }
  }
}