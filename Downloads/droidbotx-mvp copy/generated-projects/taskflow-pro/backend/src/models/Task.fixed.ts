/**
 * Task entity interface for project management application
 * Represents a unit of work that needs to be completed within a plan or project
 */
export interface Task {
  /**
   * Unique identifier for the task
   * @format uuid
   * @required
   */
  id: string;

  /**
   * Title of the task
   * @required
   * @minLength 1
   * @maxLength 255
   */
  title: string;

  /**
   * Detailed description of the task
   * @required
   * @minLength 1
   * @maxLength 1000
   */
  description: string;

  /**
   * Current status of the task
   * @required
   * @enum ['todo', 'in-progress', 'review', 'completed', 'blocked']
   */
  status: 'todo' | 'in-progress' | 'review' | 'completed' | 'blocked';

  /**
   * Date and time when the task was created
   * @format ISO 8601 date-time
   * @required
   */
  createdAt: string;

  /**
   * Date and time when the task was last updated
   * @format ISO 8601 date-time
   * @required
   */
  updatedAt: string;
}