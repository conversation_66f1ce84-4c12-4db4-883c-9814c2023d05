// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { Task } from '../models/Task.js';
import { TaskRepository } from '../repositories/TaskRepository.js';
import { Logger } from '../utils/Logger.js';

export class TaskService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }







export class TaskService {
  private taskRepository: TaskRepository;
  private logger: Logger;

  constructor(pool: Pool) {
    this.taskRepository = new TaskRepository(pool);
    this.logger = new Logger('TaskService');
  }

  async getAllTasks(): Promise<Task[]> {
    try {
      this.logger.info('Fetching all tasks');
      const tasks = await this.taskRepository.findAll();
      this.logger.info(`Retrieved ${tasks.length} tasks`);
      return tasks;
    } catch (error) {
      this.logger.error('Error fetching all tasks', error);
      throw new BusinessError('Failed to retrieve tasks');
    }
  }

  async getTaskById(id: number): Promise<Task | null> {
    try {
      this.logger.info(`Fetching task with ID: ${id}`);

      if (!id || id <= 0) {
        throw new BusinessError('Invalid task ID provided');
      }

      const task = await this.taskRepository.findById(id);

      if (!task) {
        this.logger.warn(`Task with ID ${id} not found`);
        return null;
      }

      this.logger.info(`Task with ID ${id} retrieved successfully`);
      return task;
    } catch (error) {
      this.logger.error(`Error fetching task with ID ${id}`, error);
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to retrieve task');
    }
  }

  async createTask(taskData: Partial<Task>): Promise<Task> {
    try {
      this.logger.info('Creating new task');

      // Validate required fields
      if (!taskData.title || taskData.title.trim() === '') {
        throw new BusinessError('Task title is required');
      }

      // Validate business rules
      this.validateTaskData(taskData);

      const task = await this.taskRepository.create(taskData);
      this.logger.info(`Task created successfully with ID: ${task.id}`);
      return task;
    } catch (error) {
      this.logger.error('Error creating task', error);
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to create task');
    }
  }

  async updateTask(id: number, taskData: Partial<Task>): Promise<Task> {
    try {
      this.logger.info(`Updating task with ID: ${id}`);

      if (!id || id <= 0) {
        throw new BusinessError('Invalid task ID provided');
      }

      // Validate business rules
      this.validateTaskData(taskData);

      const existingTask = await this.taskRepository.findById(id);
      if (!existingTask) {
        throw new BusinessError('Task not found');
      }

      const task = await this.taskRepository.update(id, taskData);
      this.logger.info(`Task with ID ${id} updated successfully`);
      return task;
    } catch (error) {
      this.logger.error(`Error updating task with ID ${id}`, error);
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to update task');
    }
  }

  async deleteTask(id: number): Promise<boolean> {
    try {
      this.logger.info(`Deleting task with ID: ${id}`);

      if (!id || id <= 0) {
        throw new BusinessError('Invalid task ID provided');
      }

      const existingTask = await this.taskRepository.findById(id);
      if (!existingTask) {
        throw new BusinessError('Task not found');
      }

      const result = await this.taskRepository.delete(id);
      if (result) {
        this.logger.info(`Task with ID ${id} deleted successfully`);
      } else {
        this.logger.warn(`Task with ID ${id} was not deleted`);
      }
      return result;
    } catch (error) {
      this.logger.error(`Error deleting task with ID ${id}`, error);
      if (error instanceof BusinessError) throw error;
      throw new BusinessError('Failed to delete task');
    }
  }

  private validateTaskData(taskData: Partial<Task>): void {
    // Validate title length
    if (taskData.title && taskData.title.length > 255) {
      throw new BusinessError('Task title cannot exceed 255 characters');
    }

    // Validate priority values
    if (taskData.priority !== undefined &&;
        taskData.priority !== null &&
        ![1, 2, 3, 4, 5].includes(taskData.priority)) {
      throw new BusinessError('Task priority must be between 1 and 5');
    }

    // Validate status values
    if (taskData.status &&;
        !['pending', 'in_progress', 'completed', 'cancelled'].includes(taskData.status)) {
      throw new BusinessError('Invalid task status');
    }

    // Validate due date is in the future
    if (taskData.dueDate && new Date(taskData.dueDate) < new Date()) {
      throw new BusinessError('Due date must be in the future');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'TaskService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'TaskService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default TaskService;
}