// Auto-generated standardized service
// Pattern: Database Service








import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { ValidationError } from '../errors/ValidationError.js';
import { User } from '../models/User.js';
import { UserRepository } from '../repositories/UserRepository.js';
import { Logger } from '../utils/Logger.js';

export class UserService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }








export interface UserService {
  getUserById(id: number): Promise<User>;
  getAllUsers(): Promise<User[]>;
  createUser(userData: Partial<User>): Promise<User>;
  updateUser(id: number, userData: Partial<User>): Promise<User>;
  deleteUser(id: number): Promise<void>;
}

export class UserServiceImpl implements UserService {
  private userRepository: UserRepository;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.userRepository = new UserRepository(pool);
    this.logger = logger;
  }

  async getUserById(id: number): Promise<User> {
    this.logger.info(`Fetching user with ID: ${id}`);

    if (!id || id <= 0) {
      this.logger.warn(`Invalid user ID provided: ${id}`);
      throw new ValidationError('Invalid user ID provided');
    }

    try {
      const user = await this.userRepository.findById(id);

      if (!user) {
        this.logger.warn(`User not found with ID: ${id}`);
        throw new BusinessError('User not found');
      }

      this.logger.info(`Successfully retrieved user with ID: ${id}`);
      return user;
    } catch (error) {
      this.logger.error(`Error fetching user with ID ${id}:`, error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Failed to retrieve user');
    }
  }

  async getAllUsers(): Promise<User[]> {
    this.logger.info('Fetching all users');

    try {
      const users = await this.userRepository.findAll();
      this.logger.info(`Successfully retrieved ${users.length} users`);
      return users;
    } catch (error) {
      this.logger.error('Error fetching all users:', error);
      throw new BusinessError('Failed to retrieve users');
    }
  }

  async createUser(userData: Partial<User>): Promise<User> {
    this.logger.info('Creating new user');

    // Validate required fields
    if (!userData.email) {
      this.logger.warn('Email is required for user creation');
      throw new ValidationError('Email is required');
    }

    if (!userData.name) {
      this.logger.warn('Name is required for user creation');
      throw new ValidationError('Name is required');
    }

    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        this.logger.warn(`User already exists with email: ${userData.email}`);
        throw new BusinessError('User with this email already exists');
      }

      const user = await this.userRepository.create(userData);
      this.logger.info(`Successfully created user with ID: ${user.id}`);
      return user;
    } catch (error) {
      this.logger.error('Error creating user:', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Failed to create user');
    }
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User> {
    this.logger.info(`Updating user with ID: ${id}`);

    if (!id || id <= 0) {
      this.logger.warn(`Invalid user ID provided for update: ${id}`);
      throw new ValidationError('Invalid user ID provided');
    }

    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        this.logger.warn(`User not found for update with ID: ${id}`);
        throw new BusinessError('User not found');
      }

      // If email is being updated, check for duplicates
      if (userData.email && userData.email !== existingUser.email) {
        const userWithSameEmail = await this.userRepository.findByEmail(userData.email);
        if (userWithSameEmail) {
          this.logger.warn(`Email ${userData.email} is already in use`);
          throw new BusinessError('Email is already in use by another user');
        }
      }

      const updatedUser = await this.userRepository.update(id, userData);
      this.logger.info(`Successfully updated user with ID: ${id}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Error updating user with ID ${id}:`, error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Failed to update user');
    }
  }

  async deleteUser(id: number): Promise<void> {
    this.logger.info(`Deleting user with ID: ${id}`);

    if (!id || id <= 0) {
      this.logger.warn(`Invalid user ID provided for deletion: ${id}`);
      throw new ValidationError('Invalid user ID provided');
    }

    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        this.logger.warn(`User not found for deletion with ID: ${id}`);
        throw new BusinessError('User not found');
      }

      await this.userRepository.delete(id);
      this.logger.info(`Successfully deleted user with ID: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting user with ID ${id}:`, error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('Failed to delete user');
    }
  }
}


private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: 'UserService',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}

// Logging utilities

logger.info('Database operation', {
  service: 'UserService',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});

export default UserService;
}