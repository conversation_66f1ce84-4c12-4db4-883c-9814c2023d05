// Auto-generated standardized service
// Pattern: Database Service

import { DatabaseConnection } from '../database/connection.js';
import { TransactionManager } from '../database/transaction.js';
import { QueryBuilder } from '../database/queryBuilder.js';
import { validate } from '../utils/validation.js';
import { logger } from '../core/Logger.js';
import { Pool } from 'pg';
import { BusinessError } from '../errors/BusinessError.js';
import { Task } from '../models/Task.js';
import { TaskRepository } from '../repositories/TaskRepository.js';
import { Logger } from '../utils/Logger.js';

export class TaskService {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }

