




import { createServer } from './server.js';
import cors from 'cors';
import express from 'express';
import helmet from 'helmet';

const app = createServer();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors());

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});

export default app;