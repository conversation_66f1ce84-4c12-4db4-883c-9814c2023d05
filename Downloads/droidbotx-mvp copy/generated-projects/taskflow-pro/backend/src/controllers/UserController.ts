





import { NextFunction, Request, Response } from 'express';
import { ValidationError } from '../errors/ValidationError.js';
import { User } from '../models/User.js';
import { UserService } from '../services/UserService.js';
import { Logger } from '../utils/Logger.js';

export class UserController {
  private userService: UserService;
  private logger: Logger;

  constructor(userService: UserService, logger: Logger) {
    this.userService = userService;
    this.logger = logger;
  }

  public getAllUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Fetching all users');
      const users = await this.userService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users,
        message: 'Users retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Error fetching users', { error });
      next(error);
    }
  };

  public getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Fetching user with ID: ${userId}`);
      const user = await this.userService.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user,
        message: 'User retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Error fetching user by ID', { error });
      next(error);
    }
  };

  public createUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Creating new user', { userData: req.body });

      const userData: Partial<User> = req.body;
      const user = await this.userService.createUser(userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
      });
    } catch (error) {
      this.logger.error('Error creating user', { error });
      next(error);
    }
  };

  public updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Updating user with ID: ${userId}`, { updateData: req.body });

      const userData: Partial<User> = req.body;
      const user = await this.userService.updateUser(userId, userData);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user,
        message: 'User updated successfully'
      });
    } catch (error) {
      this.logger.error('Error updating user', { error });
      next(error);
    }
  };

  public deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = parseInt(req.params.id, 10);

      if (isNaN(userId)) {
        throw new ValidationError('Invalid user ID provided');
      }

      this.logger.info(`Deleting user with ID: ${userId}`);

      const result = await this.userService.deleteUser(userId);

      if (!result) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      this.logger.error('Error deleting user', { error });
      next(error);
    }
  };
}