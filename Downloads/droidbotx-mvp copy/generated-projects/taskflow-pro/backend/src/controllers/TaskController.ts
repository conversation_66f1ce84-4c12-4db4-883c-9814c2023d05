





import { NextFunction, Request, Response } from 'express';
import { ValidationError } from '../errors/ValidationError.js';
import { Task } from '../models/Task.js';
import { TaskService } from '../services/TaskService.js';
import { Logger } from '../utils/Logger.js';

export class TaskController {
  private taskService: TaskService;
  private logger: Logger;

  constructor(taskService: TaskService, logger: Logger) {
    this.taskService = taskService;
    this.logger = logger;
  }

  public getAllTasks = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      this.logger.info('Fetching all tasks');
      const tasks = await this.taskService.getAllTasks();
      res.status(200).json({
        success: true,
        data: tasks,
        message: 'Tasks retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Error fetching tasks', error);
      next(error);
    }
  };

  public getTaskById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const taskId = parseInt(req.params.id, 10);

      if (isNaN(taskId)) {
        throw new ValidationError('Invalid task ID provided');
      }

      this.logger.info(`Fetching task with ID: ${taskId}`);
      const task = await this.taskService.getTaskById(taskId);

      if (!task) {
        res.status(404).json({
          success: false,
          message: 'Task not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: task,
        message: 'Task retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Error fetching task by ID', error);
      next(error);
    }
  };

  public createTask = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const taskData: Partial<Task> = req.body;

      this.logger.info('Creating new task', taskData);
      const createdTask = await this.taskService.createTask(taskData);

      res.status(201).json({
        success: true,
        data: createdTask,
        message: 'Task created successfully'
      });
    } catch (error) {
      this.logger.error('Error creating task', error);
      next(error);
    }
  };

  public updateTask = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const taskId = parseInt(req.params.id, 10);
      const taskData: Partial<Task> = req.body;

      if (isNaN(taskId)) {
        throw new ValidationError('Invalid task ID provided');
      }

      this.logger.info(`Updating task with ID: ${taskId}`, taskData);
      const updatedTask = await this.taskService.updateTask(taskId, taskData);

      if (!updatedTask) {
        res.status(404).json({
          success: false,
          message: 'Task not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: updatedTask,
        message: 'Task updated successfully'
      });
    } catch (error) {
      this.logger.error('Error updating task', error);
      next(error);
    }
  };

  public deleteTask = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const taskId = parseInt(req.params.id, 10);

      if (isNaN(taskId)) {
        throw new ValidationError('Invalid task ID provided');
      }

      this.logger.info(`Deleting task with ID: ${taskId}`);
      const deleted = await this.taskService.deleteTask(taskId);

      if (!deleted) {
        res.status(404).json({
          success: false,
          message: 'Task not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Task deleted successfully'
      });
    } catch (error) {
      this.logger.error('Error deleting task', error);
      next(error);
    }
  };
}