```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=taskflow_pro
DB_USER=taskflow_user
DB_PASSWORD=secure_password
DB_SSL=false

# JWT Configuration
JWT_ACCESS_SECRET=your_access_token_secret_here
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Server Configuration
PORT=3000
NODE_ENV=development

# API Keys
API_KEY_SALT=your_api_key_salt_here

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
EMAIL_FROM=<EMAIL>

# Frontend URL
FRONTEND_URL=http://localhost:3001

# Logging
LOG_LEVEL=info

# TaskFlow Pro Specific
TASK_ASSIGNMENT_TIMEOUT=24h
MAX_TASK_ATTACHMENTS=5
MAX_ATTACHMENT_SIZE=10MB
DEFAULT_TASK_PRIORITY=medium
```