```yaml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - NODE_ENV=development
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - taskflow-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=************************************************/taskflow_db
      - JWT_SECRET=taskflow_jwt_secret_key
      - NODE_ENV=development
      - PORT=5000
    depends_on:
      - db
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - taskflow-network

  db:
    image: postgres:13-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=taskflow_user
      - POSTGRES_PASSWORD=taskflow_pass
      - POSTGRES_DB=taskflow_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - taskflow-network

volumes:
  postgres_data:

networks:
  taskflow-network:
    driver: bridge
```