


import * as taskApi from '../api/taskApi';




import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { TaskDetail } from './TaskDetail.js';
import { TaskForm } from './TaskForm.js';
import { TaskList } from './TaskList.js';
import userEvent from '@testing-library/user-event';
import React from 'react';

Here's a comprehensive test suite for Task components using React Testing Library:'

tsx`
// Task.test.tsx







// Mock API functions
jest.mock('../api/taskApi');

// Mock task data
const mockTasks = [;
  { id: 1, title: 'Task 1', description: 'Description 1', status: 'todo', priority: 'high' },
  { id: 2, title: 'Task 2', description: 'Description 2', status: 'in-progress', priority: 'medium' },
  { id: 3, title: 'Task 3', description: 'Description 3', status: 'done', priority: 'low' },
];

const mockTask = {
  id: 1,
  title: 'Test Task',
  description: 'Test Description',
  status: 'todo',
  priority: 'high',
  dueDate: '2023-12-31',
  assignee: '<PERSON>'
};

describe('Task Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('TaskList', () => {
    it('renders loading state', () => {
      (taskApi.fetchTasks as jest.Mock).mockResolvedValue([]);
      render(<TaskList />);
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders tasks after loading', async () => {
      (taskApi.fetchTasks as jest.Mock).mockResolvedValue(mockTasks);
      render(<TaskList />);
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument();
        expect(screen.getByText('Task 2')).toBeInTheDocument();
        expect(screen.getByText('Task 3')).toBeInTheDocument();
      });
    });

    it('handles API error', async () => {
      (taskApi.fetchTasks as jest.Mock).mockRejectedValue(new Error('API Error'));
      render(<TaskList />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });
    });

    it('filters tasks by status', async () => {
      (taskApi.fetchTasks as jest.Mock).mockResolvedValue(mockTasks);
      render(<TaskList />);
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText(/in progress/i));
      
      expect(screen.queryByText('Task 1')).not.toBeInTheDocument();
      expect(screen.getByText('Task 2')).toBeInTheDocument();
    });
  });

  describe('TaskForm', () => {
    const mockOnSubmit = jest.fn();
    
    beforeEach(() => {
      mockOnSubmit.mockClear();
    });

    it('renders form fields', () => {
      render(<TaskForm onSubmit={mockOnSubmit} />);
      
      expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/priority/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/due date/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
    });

    it('shows validation errors for required fields', async () => {
      render(<TaskForm onSubmit={mockOnSubmit} />);
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/title is required/i)).toBeInTheDocument();
        expect(screen.getByText(/description is required/i)).toBeInTheDocument();
      });
      
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('submits form with valid data', async () => {
      (taskApi.createTask as jest.Mock).mockResolvedValue(mockTask);
      render(<TaskForm onSubmit={mockOnSubmit} />);
      
      await userEvent.type(screen.getByLabelText(/title/i), 'New Task');
      await userEvent.type(screen.getByLabelText(/description/i), 'Task Description');
      fireEvent.change(screen.getByLabelText(/priority/i), { target: { value: 'high' } });
      fireEvent.change(screen.getByLabelText(/due date/i), { target: { value: '2023-12-31' } });
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(taskApi.createTask).toHaveBeenCalledWith({
          title: 'New Task',
          description: 'Task Description',
          priority: 'high',
          dueDate: '2023-12-31'
        });
        expect(mockOnSubmit).toHaveBeenCalledWith(mockTask);
      });
    });

    it('shows API error message', async () => {
      (taskApi.createTask as jest.Mock).mockRejectedValue(new Error('Creation failed'));
      render(<TaskForm onSubmit={mockOnSubmit} />);
      
      await userEvent.type(screen.getByLabelText(/title/i), 'New Task');
      await userEvent.type(screen.getByLabelText(/description/i), 'Task Description');
      
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/failed to create task/i)).toBeInTheDocument();
      });
    });
  });

  describe('TaskDetail', () => {
    it('renders task details', async () => {
      (taskApi.fetchTask as jest.Mock).mockResolvedValue(mockTask);
      render(<TaskDetail taskId={1} />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Task')).toBeInTheDocument();
        expect(screen.getByText('Test Description')).toBeInTheDocument();
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('2023-12-31')).toBeInTheDocument();
      });
    });

    it('shows loading state', () => {
      (taskApi.fetchTask as jest.Mock).mockResolvedValue(null);
      render(<TaskDetail taskId={1} />);
      
      expect(screen.getByText(/loading task details/i)).toBeInTheDocument();
    });

    it('handles API error', async () => {
      (taskApi.fetchTask as jest.Mock).mockRejectedValue(new Error('Not found'));
      render(<TaskDetail taskId={999} />);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load task/i)).toBeInTheDocument();
      });
    });

    it('updates task status', async () => {
      const updatedTask = { ...mockTask, status: 'in-progress' };
      (taskApi.fetchTask as jest.Mock).mockResolvedValue(mockTask);
      (taskApi.updateTask as jest.Mock).mockResolvedValue(updatedTask);
      
      render(<TaskDetail taskId={1} />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Task')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByRole('button', { name: /start task/i }));
      
      await waitFor(() => {
        expect(taskApi.updateTask).toHaveBeenCalledWith(1, { status: 'in-progress' });
        expect(screen.getByText(/in progress/i)).toBeInTheDocument();
      });
    });
  });
});
`

This test suite covers:

1. **Component Rendering**:
   - Loading states for all components
   - Proper rendering of task data
   - Form field presence

2. **User Interactions**:
   - Form submission
   - Task filtering
   - Status updates
   - Input typing

3. **Form Validation**:
   - Required field validation
   - Error message display
   - Submission prevention

4. **API Calls**:
   - Successful data fetching
   - Create/update operations
   - Error handling

5. **Error States**:
   - API failure handling
   - Error message display
   - Recovery scenarios

6. **Loading States**:
   - Initial loading indicators
   - Async operation handling
   - State transitions

Key features of the implementation:

- **Mocking**: API functions are properly mocked using Jest
- **Async Handling**: Uses `waitFor` for async operations
- **User Events**: Simulates real user interactions
- **Assertions**: Comprehensive checks for UI states
- **Edge Cases**: Covers error scenarios and validation
- **Type Safety**: Written in TypeScript with proper typing

To run these tests, you'll need the following dependencies:'
bash`
npm install --save-dev @testing-library/react @testing-library/user-event
`

The tests assume:
1. Task components accept props for data and callbacks
2. API functions return promises with task data;
3. Components handle loading and error states
4. Form components validate inputs before submission

You may need to adjust selectors and component interfaces based on your actual implementation.