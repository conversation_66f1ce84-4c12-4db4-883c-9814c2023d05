


import * as userApi from '../api/userApi';





import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { User } from '../types.js';
import { UserDetail } from './UserDetail.js';
import { UserForm } from './UserForm.js';
import { UserList } from './UserList.js';
import React from 'react';

Here's a comprehensive test suite for User components using React Testing Library:'

tsx`
// User.test.tsx








// Mock API functions
jest.mock('../api/userApi');

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock data
const mockUsers: User[] = [;
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'user' },
];

const mockUser: User = {
  id: 1,
  name: '<PERSON>e',
  email: '<EMAIL>',
  role: 'admin',
};

describe('User Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('UserList', () => {
    it('renders loading state', () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValueOnce([]);
      render(<UserList />, { wrapper: BrowserRouter });
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders user list', async () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValueOnce(mockUsers);
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('handles API error', async () => {
      (userApi.fetchUsers as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load users/i)).toBeInTheDocument();
      });
    });

    it('navigates to user detail on click', async () => {
      (userApi.fetchUsers as jest.Mock).mockResolvedValueOnce(mockUsers);
      
      render(<UserList />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('John Doe'));
      });
      
      expect(mockNavigate).toHaveBeenCalledWith('/users/1');
    });
  });

  describe('UserForm', () => {
    const defaultProps = {
      onSubmit: jest.fn(),
      onCancel: jest.fn(),
    };

    it('renders empty form', () => {
      render(<UserForm {...defaultProps} />, { wrapper: BrowserRouter });
      
      expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
    });

    it('renders with initial values', () => {
      render(<UserForm {...defaultProps} initialUser={mockUser} />, { wrapper: BrowserRouter });
      
      expect(screen.getByLabelText(/name/i)).toHaveValue('John Doe');
      expect(screen.getByLabelText(/email/i)).toHaveValue('<EMAIL>');
    });

    it('shows validation errors', async () => {
      render(<UserForm {...defaultProps} />, { wrapper: BrowserRouter });
      
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    it('submits valid form', async () => {
      const mockSubmit = jest.fn();
      render(<UserForm {...defaultProps} onSubmit={mockSubmit} />, { wrapper: BrowserRouter });
      
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' },
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(screen.getByLabelText(/role/i), {
        target: { value: 'admin' },
      });
      
      fireEvent.click(screen.getByText(/save/i));
      
      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'admin',
        });
      });
    });

    it('calls onCancel when cancel button is clicked', () => {
      const mockCancel = jest.fn();
      render(<UserForm {...defaultProps} onCancel={mockCancel} />, { wrapper: BrowserRouter });
      
      fireEvent.click(screen.getByText(/cancel/i));
      expect(mockCancel).toHaveBeenCalled();
    });
  });

  describe('UserDetail', () => {
    it('renders loading state', () => {
      (userApi.fetchUser as jest.Mock).mockResolvedValueOnce(null);
      render(<UserDetail />, { wrapper: BrowserRouter });
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('renders user details', async () => {
      (userApi.fetchUser as jest.Mock).mockResolvedValueOnce(mockUser);
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('admin')).toBeInTheDocument();
      });
    });

    it('handles API error', async () => {
      (userApi.fetchUser as jest.Mock).mockRejectedValueOnce(new Error('API Error'));
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load user/i)).toBeInTheDocument();
      });
    });

    it('navigates to edit form', async () => {
      (userApi.fetchUser as jest.Mock).mockResolvedValueOnce(mockUser);
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText(/edit/i));
      });
      
      expect(mockNavigate).toHaveBeenCalledWith('/users/1/edit');
    });

    it('deletes user', async () => {
      (userApi.fetchUser as jest.Mock).mockResolvedValueOnce(mockUser);
      (userApi.deleteUser as jest.Mock).mockResolvedValueOnce(undefined);
      
      render(<UserDetail />, { wrapper: BrowserRouter });
      
      await waitFor(() => {
        fireEvent.click(screen.getByText(/delete/i));
      });
      
      // Confirm deletion
      fireEvent.click(screen.getByText(/confirm/i));
      
      await waitFor(() => {
        expect(userApi.deleteUser).toHaveBeenCalledWith(1);
        expect(mockNavigate).toHaveBeenCalledWith('/users');
      });
    });
  });
});
`

Key features of this test suite:

1. **Component Rendering Tests**:
   - Verifies loading states for async components
   - Checks proper rendering of user data
   - Tests error handling scenarios

2. **User Interaction Tests**:
   - Form submission with valid/invalid data
   - Navigation between components
   - Delete confirmation flows

3. **Form Validation Tests**:
   - Empty form submission triggers validation errors
   - Required field validation messages
   - Proper validation error display

4. **API Mocking**:
   - Mocks all API calls using jest.mock
   - Tests both success and error scenarios
   - Verifies correct parameters passed to API functions

5. **Error State Tests**:
   - API failure handling
   - Error message display
   - Recovery mechanisms

6. **Loading State Tests**:
   - Initial loading indicators
   - Loading during async operations
   - Transition from loading to content

7. **Routing Tests**:
   - Navigation between user views
   - Edit form navigation
   - Redirect after delete operations

The tests use:
- React Testing Library best practices (findBy/getBy queries)
- Proper async/await with waitFor
- Comprehensive mocking of dependencies
- Realistic user interaction scenarios
- TypeScript type safety throughout

To run these tests, ensure you have:
1. React Testing Library installed
2. Jest configured for TypeScript
3. Mock implementations for your API functions
4. Proper component implementations matching the test expectations