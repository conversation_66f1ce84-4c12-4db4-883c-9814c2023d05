




import { AccessTime as RecentIcon, Add as AddIcon, Assignment as TaskIcon, CalendarToday as CalendarIcon, Person as UserIcon, TrendingUp as TrendingIcon } from '@mui/icons-material';
import { Avatar, Box, Button, Card, CardContent, Chip, Grid, IconButton, List, ListItem, ListItemText, Tooltip, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import React from 'react';

tsx`



  Box, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Button, 
  List, 
  ListItem, 
  ListItemText,
  Chip,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';

  Assignment as TaskIcon,
  Person as UserIcon,
  Add as AddIcon,
  TrendingUp as TrendingIcon,
  AccessTime as RecentIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';

interface Task {
  id: string;
  title: string;
  status: 'todo' | 'in-progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  assignee?: {
    id: string;
    name: string;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  lastActive?: string;
}

interface DashboardStats {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  activeUsers: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
    activeUsers: 0
  });
  const [recentTasks, setRecentTasks] = useState<Task[]>([]);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);

  // Mock data fetching
  useEffect(() => {
    // In a real app, this would be API calls
    setStats({
      totalTasks: 24,
      completedTasks: 12,
      pendingTasks: 8,
      activeUsers: 5
    });

    setRecentTasks([;
      {
        id: '1',
        title: 'Design dashboard UI',
        status: 'completed',
        priority: 'high',
        dueDate: '2023-06-15',
        assignee: { id: '1', name: 'Alex Johnson' }
      },
      {
        id: '2',
        title: 'Implement authentication',
        status: 'in-progress',
        priority: 'high',
        dueDate: '2023-06-20',
        assignee: { id: '2', name: 'Sam Wilson' }
      },
      {
        id: '3',
        title: 'Write documentation',
        status: 'todo',
        priority: 'medium',
        dueDate: '2023-06-25'
      }
    ]);

    setRecentUsers([;
      {
        id: '1',
        name: 'Alex Johnson',
        email: '<EMAIL>',
        role: 'Developer',
        lastActive: '2023-06-10T14:30:00Z'
      },
      {
        id: '2',
        name: 'Sam Wilson',
        email: '<EMAIL>',
        role: 'Designer',
        lastActive: '2023-06-10T12:15:00Z'
      },
      {
        id: '3',
        name: 'Taylor Reed',
        email: '<EMAIL>',
        role: 'Project Manager',
        lastActive: '2023-06-09T16:45:00Z'
      }
    ]);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in-progress': return 'warning';
      case 'todo': return 'default';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (;
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={() => navigate('/tasks/new')}
        >
          New Task
        </Button>
      </Box>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <TaskIcon />
                </Avatar>
                <Typography color="textSecondary" gutterBottom>
                  Total Tasks
                </Typography>
              </Box>
              <Typography variant="h4">{stats.totalTasks}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="body2" color="success.main">
                  +12% from last week
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <TaskIcon />
                </Avatar>
                <Typography color="textSecondary" gutterBottom>
                  Completed
                </Typography>
              </Box>
              <Typography variant="h4">{stats.completedTasks}</Typography>
              <Typography variant="body2" color="textSecondary">
                {Math.round((stats.completedTasks / stats.totalTasks) * 100)}% of total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <TaskIcon />
                </Avatar>
                <Typography color="textSecondary" gutterBottom>
                  In Progress
                </Typography>
              </Box>
              <Typography variant="h4">{stats.pendingTasks}</Typography>
              <Typography variant="body2" color="textSecondary">
                Requires attention
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <UserIcon />
                </Avatar>
                <Typography color="textSecondary" gutterBottom>
                  Active Users
                </Typography>
              </Box>
              <Typography variant="h4">{stats.activeUsers}</Typography>
              <Typography variant="body2" color="textSecondary">
                Online now
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Access */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <AssignmentIcon sx={{ mr: 1 }} />
                Quick Access
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<TaskIcon />}
                    onClick={() => navigate('/tasks')}
                    sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                  >
                    All Tasks
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<UserIcon />}
                    onClick={() => navigate('/users')}
                    sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                  >
                    All Users
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => navigate('/tasks/new')}
                    sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                  >
                    New Task
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => navigate('/users/new')}
                    sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                  >
                    New User
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Recent Users */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <UserIcon sx={{ mr: 1 }} />
                Recent Users
              </Typography>
              <List>
                {recentUsers.map((user) => (
                  <ListItem 
                    key={user.id} 
                    sx={{ py: 1 }}
                    secondaryAction={
                      <Chip label={user.role} size="small" variant="outlined" />
                    }
                  >
                    <ListItemText 
                      primary={user.name} 
                      secondary={user.email}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Tasks */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <RecentIcon sx={{ mr: 1 }} />
                Recent Tasks
              </Typography>
              <List>
                {recentTasks.map((task) => (
                  <ListItem 
                    key={task.id} 
                    sx={{ 
                      py: 2, 
                      borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                      '&:last-child': { borderBottom: 'none' }
                    }}
                    secondaryAction={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {task.dueDate && (
                          <Tooltip title={`Due: ${new Date(task.dueDate).toLocaleDateString()}`}>
                            <IconButton size="small" sx={{ mr: 1 }}>
                              <CalendarIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Chip 
                          label={task.status.replace('-', ' ')} 
                          size="small" 
                          color={getStatusColor(task.status) as any}
                          sx={{ mr: 1 }}
                        />
                        <Chip 
                          label={task.priority} 
                          size="small" 
                          color={getPriorityColor(task.priority) as any}
                        />
                      </Box>
                    }
                  >
                    <ListItemText 
                      primary={
                        <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                          {task.title}
                        </Typography>
                      }
                      secondary={
                        task.assignee 
                          ? `Assigned to ${task.assignee.name}` 
                          : 'Unassigned'
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
`