




import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, CircularProgress, Container, CssB<PERSON><PERSON>, Tool<PERSON>, Typography } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter as Router, Link, Navigate, Route, Routes } from 'react-router-dom';
import React from 'react';

AppBar, 
  Toolbar, 
  Typography, 
  Button, 
  Container, 
  CssBaseline,
  Box,
  CircularProgress,
  Alert
} from '@mui/material';

// Types and Interfaces
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'completed';
  assigneeId: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface AppState {
  tasks: Task[];
  users: User[];
  loading: boolean;
  error: string | null;
}

type AuthAction = 
  | { type: 'LOGIN'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; payload: boolean };

type AppAction = 
  | { type: 'SET_TASKS'; payload: Task[] }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// Contexts
const AuthContext = createContext<{
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
} | undefined>(undefined);

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | undefined>(undefined);

// Reducers
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    default:
      return state;
  }
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_TASKS':
      return {
        ...state,
        tasks: action.payload,
        loading: false
      };
    case 'SET_USERS':
      return {
        ...state,
        users: action.payload,
        loading: false
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false
      };
    default:
      return state;
  }
};

// Custom Hooks
const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#e57373',
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

// Components
const Navbar: React.FC = () => {
  const { state, dispatch } = useAuth();
  
  const handleLogout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  return (;
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          <Link to="/" style={{ color: 'white', textDecoration: 'none' }}>
            taskflow-pro
          </Link>
        </Typography>
        {state.isAuthenticated ? (
          <>
            <Button color="inherit" component={Link} to="/tasks">
              Tasks
            </Button>
            <Button color="inherit" component={Link} to="/users">
              Users
            </Button>
            <Button color="inherit" onClick={handleLogout}>
              Logout
            </Button>
          </>
        ) : (
          <Button color="inherit" component={Link} to="/login">
            Login
          </Button>
        )}
      </Toolbar>
    </AppBar>
  );
};

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth();
  
  if (state.loading) {
    return (;
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }
  
  return state.isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const Home: React.FC = () => {
  return (;
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Welcome to taskflow-pro
      </Typography>
      <Typography variant="h5" component="h2" gutterBottom>
        Your comprehensive project management solution
      </Typography>
      <Typography variant="body1" paragraph>
        taskflow-pro helps you efficiently manage your work through intuitive task lifecycle management.
        Create, track, and complete tasks within structured plans and projects.
      </Typography>
    </Container>
  );
};

const TasksPage: React.FC = () => {
  const { state } = useApp();
  
  return (;
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Tasks
      </Typography>
      {state.loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : state.error ? (
        <Alert severity="error">{state.error}</Alert>
      ) : (
        <Box>
          {state.tasks.length === 0 ? (
            <Typography>No tasks found</Typography>
          ) : (
            <Box>
              {state.tasks.map((task) => (
                <Box key={task.id} sx={{ mb: 2, p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
                  <Typography variant="h6">{task.title}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Status: {task.status}
                  </Typography>
                  <Typography variant="body2">
                    {task.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      )}
    </Container>
  );
};

const UsersPage: React.FC = () => {
  const { state } = useApp();
  
  return (;
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Users
      </Typography>
      {state.loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : state.error ? (
        <Alert severity="error">{state.error}</Alert>
      ) : (
        <Box>
          {state.users.length === 0 ? (
            <Typography>No users found</Typography>
          ) : (
            <Box>
              {state.users.map((user) => (
                <Box key={user.id} sx={{ mb: 2, p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
                  <Typography variant="h6">{user.name}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {user.email}
                  </Typography>
                  <Typography variant="body2">
                    Role: {user.role}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      )}
    </Container>
  );
};

const LoginPage: React.FC = () => {
  const { dispatch } = useAuth();
  
  const handleLogin = () => {
    // Simulate login
    const mockUser: User = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin'
    };
    
    dispatch({ type: 'LOGIN', payload: mockUser });
  };
  
  return (;
    <Container maxWidth="sm" sx={{ mt: 8 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography component="h1" variant="h5" gutterBottom>
          Sign in to taskflow-pro
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Button
            fullWidth
            variant="contained"
            onClick={handleLogin}
            sx={{ mb: 2 }}
          >
            Login as Demo User
          </Button>
          <Typography variant="body2" align="center">
            Use the demo login to explore taskflow-pro features
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

const NotFound: React.FC = () => {
  return (;
    <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Page Not Found
      </Typography>
      <Typography variant="body1" paragraph>
        The page you are looking for doesn't exist.'
      </Typography>
      <Button variant="contained" component={Link} to="/">
        Go Home
      </Button>
    </Container>
  );
};

const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = () => setHasError(true);
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return (;
      <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
        <Typography variant="h5" component="h1" gutterBottom>
          Something went wrong
        </Typography>
        <Typography variant="body1" paragraph>
          An unexpected error occurred. Please try again later.
        </Typography>
        <Button 
          variant="contained" 
          onClick={() => window.location.reload()}
        >
          Reload Page
        </Button>
      </Container>
    );
  }

  return <>{children}</>;
};

// Main App Component
const App: React.FC = () => {
  const [authState, authDispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    loading: true
  });
  
  const [appState, appDispatch] = useReducer(appReducer, {
    tasks: [],
    users: [],
    loading: true,
    error: null
  });
  
  // Simulate initial data loading
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Simulate API calls
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockTasks: Task[] = [;
          {
            id: '1',
            title: 'Create project plan',
            description: 'Develop detailed project plan with milestones',
            status: 'completed',
            assigneeId: '1',
            projectId: '1',
            createdAt: '2023-01-15',
            updatedAt: '2023-01-20'
          },
          {
            id: '2',
            title: 'Design user interface',
            description: 'Create wireframes and mockups for all screens',
            status: 'in-progress',
            assigneeId: '2',
            projectId: '1',
            createdAt: '2023-01-18',
            updatedAt: '2023-01-22'
          }
        ];
        
        const mockUsers: User[] = [;
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'admin'
          },
          {
            id: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: 'user'
          }
        ];
        
        appDispatch({ type: 'SET_TASKS', payload: mockTasks });
        appDispatch({ type: 'SET_USERS', payload: mockUsers });
      } catch (error) {
        appDispatch({ type: 'SET_ERROR', payload: 'Failed to load data' });
      } finally {
        authDispatch({ type: 'SET_LOADING', payload: false });
        appDispatch({ type: 'SET_LOADING', payload: false });
      }
    };
    
    initializeApp();
  }, []);
  
  return (;
    <ErrorBoundary>
      <AuthContext.Provider value={{ state: authState, dispatch: authDispatch }}>
        <AppContext.Provider value={{ state: appState, dispatch: appDispatch }}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <Router>
              <Navbar />
              <Routes>
                <Route path="/" element={<Home />} />
                <Route 
                  path="/tasks" 
                  element={
                    <ProtectedRoute>
                      <TasksPage />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/users" 
                  element={
                    <ProtectedRoute>
                      <UsersPage />
                    </ProtectedRoute>
                  } 
                />
                <Route path="/login" element={<LoginPage />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Router>
          </ThemeProvider>
        </AppContext.Provider>
      </