CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority INTEGER DEFAULT 1,
    assigned_to UUID REFERENCES users(id),
    created_by UUID REFERENCES users(id) NOT NULL,
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_status CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    CONSTRAINT chk_priority CHECK (priority BETWEEN 1 AND 5)
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_created_by ON tasks(created_by);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);

ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

CREATE POLICY task_access_policy ON tasks
    FOR ALL TO PUBLIC
    USING (
        created_by = current_user_id() OR
        assigned_to = current_user_id()
    );

CREATE OR REPLACE FUNCTION current_user_id()
RETURNS UUID AS $$
BEGIN
    RETURN NULLIF(current_setting('app.current_user_id', true), '')::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

INSERT INTO users (id, email, password_hash, first_name, last_name, is_active) VALUES
    ('a1b2c3d4-e5f6-7890-abcd-ef1234567890', '<EMAIL>', crypt('password123', gen_salt('bf')), 'John', 'Doe', true),
    ('b2c3d4e5-f678-90ab-cdef-1234567890ab', '<EMAIL>', crypt('password456', gen_salt('bf')), 'Jane', 'Smith', true);

INSERT INTO tasks (title, description, status, priority, assigned_to, created_by, due_date) VALUES
    ('Setup database schema', 'Create initial database schema for the application', 'completed', 3, 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', CURRENT_TIMESTAMP + INTERVAL '7 days'),
    ('Implement authentication', 'Add user authentication functionality', 'in_progress', 5, 'b2c3d4e5-f678-90ab-cdef-1234567890ab', 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', CURRENT_TIMESTAMP + INTERVAL '14 days'),
    ('Design UI mockups', 'Create initial UI designs for the dashboard', 'pending', 2, NULL, 'b2c3d4e5-f678-90ab-cdef-1234567890ab', CURRENT_TIMESTAMP + INTERVAL '21 days');