# TaskFlow Pro Documentation

## 1. Project Overview and Purpose

### What is TaskFlow Pro?

TaskFlow Pro is a comprehensive task management and workflow automation platform designed to help teams and individuals efficiently organize, track, and execute their work. The application provides a centralized system for creating, assigning, and monitoring tasks while enabling collaborative workflows and productivity insights.

### Key Features

- **Task Management**: Create, assign, and track tasks with detailed descriptions, due dates, and priorities
- **User Collaboration**: Enable team members to collaborate on tasks with comments and status updates
- **Workflow Automation**: Define custom workflows and automate repetitive task processes
- **Progress Tracking**: Visualize task completion rates and team productivity metrics
- **Notification System**: Real-time alerts for task assignments, updates, and deadlines
- **Role-based Access**: Secure access control with different permission levels

### Target Audience

- Project managers overseeing multiple team initiatives
- Development teams managing sprint backlogs and feature implementation
- Marketing teams coordinating campaign activities and deliverables
- Operations teams handling routine processes and procedures
- Freelancers and individuals managing personal productivity

## 2. Architecture Description

### High-Level Architecture

TaskFlow Pro follows a modern, scalable architecture pattern consisting of:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (React)       │◄──►│   (Node.js)     │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
       │                       │                       │
       ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Authentication│    │   Business      │    │   Data          │
│   (JWT)         │    │   Logic         │    │   Persistence   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

#### Frontend
- **React 18** with TypeScript for component-based UI development
- **Redux Toolkit** for state management
- **React Router** for client-side routing
- **Material-UI** for responsive design components
- **Axios** for HTTP client communication

#### Backend
- **Node.js** with Express.js framework
- **TypeScript** for type-safe server-side development
- **PostgreSQL** for relational data storage
- **JWT** for authentication and authorization
- **Socket.IO** for real-time notifications
- **Redis** for caching and session management

#### Infrastructure
- **Docker** for containerization
- **Nginx** as reverse proxy and load balancer
- **PM2** for process management
- **Jest** and **Cypress** for testing

### Data Flow

1. **User Authentication**: Users authenticate via JWT tokens
2. **API Requests**: Frontend communicates with backend via RESTful APIs
3. **Business Logic**: Server processes requests and applies business rules
4. **Data Persistence**: Information stored in PostgreSQL database
5. **Real-time Updates**: WebSocket connections for live notifications
6. **Caching**: Redis cache for frequently accessed data

## 3. API Documentation

### Authentication Endpoints

#### POST `/api/auth/register`
Register a new user account

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "createdAt": "timestamp"
  },
  "token": "jwt_token"
}
```

#### POST `/api/auth/login`
Authenticate user and generate access token

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  },
  "token": "jwt_token"
}
```

### Task Endpoints

#### GET `/api/tasks`
Retrieve all tasks for the authenticated user

**Query Parameters:**
- `status` (optional): Filter by task status (pending, in_progress, completed)
- `priority` (optional): Filter by priority level (low, medium, high)
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 10)

**Response:**
```json
{
  "tasks": [
    {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "status": "pending|in_progress|completed",
      "priority": "low|medium|high",
      "dueDate": "timestamp",
      "assigneeId": "uuid",
      "creatorId": "uuid",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25
  }
}
```

#### POST `/api/tasks`
Create a new task

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "status": "pending",
  "priority": "medium",
  "dueDate": "timestamp",
  "assigneeId": "uuid"
}
```

**Response:**
```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "status": "pending",
  "priority": "medium",
  "dueDate": "timestamp",
  "assigneeId": "uuid",
  "creatorId": "uuid",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

#### GET `/api/tasks/:id`
Retrieve a specific task by ID

**Response:**
```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "status": "pending|in_progress|completed",
  "priority": "low|medium|high",
  "dueDate": "timestamp",
  "assignee": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  },
  "creator": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  },
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

#### PUT `/api/tasks/:id`
Update an existing task

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "status": "in_progress",
  "priority": "high",
  "dueDate": "timestamp",
  "assigneeId": "uuid"
}
```

**Response:**
```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "status": "in_progress",
  "priority": "high",
  "dueDate": "timestamp",
  "assigneeId": "uuid",
  "creatorId": "uuid",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

#### DELETE `/api/tasks/:id`
Delete a task

**Response:**
```json
{
  "message": "Task deleted successfully"
}
```

### User Endpoints

#### GET `/api/users`
Retrieve all users (admin only)

**Response:**
```json
[
  {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "user|admin",
    "createdAt": "timestamp"
  }
]
```

#### GET `/api/users/:id`
Retrieve a specific user by ID

**Response:**
```json
{
  "id": "uuid",
  "username": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "role": "user|admin",
  "createdAt": "timestamp"
}
```

#### PUT `/api/users/:id`
Update user profile (self or admin)

**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string"
}
```

**Response:**
```json
{
  "id": "uuid",
  "username": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "role": "user|admin",
  "createdAt": "timestamp"
}
```

## 4. Setup and Installation Instructions

### Prerequisites

- Node.js v16+ installed
- PostgreSQL v13+ installed
- Docker and Docker Compose (optional but recommended)
- Git for version control

### Quick Start with Docker (Recommended)

1. Clone the repository:
```bash
git clone https://github.com/your-organization/taskflow-pro.git
cd taskflow-pro
```

2. Create environment files:
```bash
cp .env.example .env
cp .env.db.example .env.db
```

3. Configure environment variables in `.env`:
```env
# Server configuration
PORT=3000
NODE_ENV=development

# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=taskflow_pro
DB_USER=taskflow_user
DB_PASSWORD=your_secure_password

# JWT configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Frontend URL
FRONTEND_URL=http://localhost:3000
```

4. Start the application:
```bash
docker-compose up -d
```

5. Run database migrations:
```bash
docker-compose exec backend npm run migrate
```

6. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Database: localhost:5432

### Manual Installation

#### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up PostgreSQL database:
```sql
CREATE DATABASE taskflow_pro;
CREATE USER taskflow_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE taskflow_pro TO taskflow_user;
```

4. Run database migrations:
```bash
npm run migrate
```

5. Start the development server:
```bash
npm run dev
```

#### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

### Environment Configuration

#### Backend Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port number | Yes |
| `NODE_ENV` | Environment (development/production) | Yes |
| `DB_HOST` | Database host | Yes |
| `DB_PORT` | Database port | Yes |
| `DB_NAME` | Database name | Yes |
| `DB_USER` | Database user | Yes |
| `DB_PASSWORD` | Database password | Yes |
| `JWT_SECRET` | Secret key for JWT tokens | Yes |
| `JWT_EXPIRES_IN` | Token expiration time | Yes |
| `REDIS_HOST` | Redis host | No |
| `REDIS_PORT` | Redis port | No |
| `FRONTEND_URL` | Frontend application URL | Yes |

#### Database Schema

The application uses the following database schema:

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  role VARCHAR(20) DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tasks table
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  status VARCHAR(20) DEFAULT 'pending',
  priority VARCHAR(10) DEFAULT 'medium',
  due_date TIMESTAMP,
  assignee_id UUID REFERENCES users(id),
  creator_id UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
```

## 5. Usage Examples

### Creating and Managing Tasks

#### Creating a New Task
```javascript
// Using the API client
const newTask = {
  title: "Implement user authentication",
  description: "Create login and registration functionality with JWT",
  priority: "high",
  dueDate: "2023-12-31T23:59:59Z",
  assigneeId: "user-uuid-here"
};

const response = await apiClient.post('/tasks', newTask);
console.log('Task created:', response.data);
```

#### Updating Task Status
```javascript
// Update task to in progress
const updatedTask = {
  status: "in_progress",
  description: "Implement user authentication - currently working on JWT setup"
};

const response = await apiClient.put(`/tasks/${taskId}`, updatedTask);
console.log('Task updated:', response.data);
```

#### Filtering Tasks
```javascript
// Get high priority tasks
const response = await apiClient.get('/tasks', {
  params: {
    priority: 'high',
    status: 'pending'
  }
});

console.log('High priority tasks:', response.data.tasks);
```

### User Management

#### User Registration
```javascript
const newUser = {
  username: "johndoe",
  email: "<EMAIL>",
  password: "securePassword123",
  firstName: "John",
  lastName: "Doe"
};

const response = await apiClient.post('/auth/register', newUser);
console.log('User registered:', response.data);
```

#### Profile Update
```javascript
const updatedProfile = {
  firstName: "Jonathan",
  lastName: "Doe-Smith"
};

const response = await apiClient.put(`/users/${userId}`, updatedProfile);
console.log('Profile updated:', response.data);
```

### Frontend Component Example

```tsx
// TaskList.tsx
import React, { useEffect, useState } from 'react';
import { Task } from '../types';
import { apiClient } from '../utils/api';

interface TaskListProps {
  status?: 'pending' | 'in_progress' | 'completed';
}

const TaskList: React.FC<TaskListProps> = ({ status }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        const response = await apiClient.get('/tasks', {
          params: { status }
        });
        setTasks(response.data.tasks);
      } catch (error) {
        console.error('Error fetching tasks:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, [status]);

  if (loading) return <div>Loading tasks...</div>;

  return (
    <div className="task-list">
      <h2>{status ? `${status.replace('_', ' ')} Tasks` : 'All Tasks'}</h2>
      {tasks.map(task => (
        <div key={task.id} className="task-item">
          <h3>{task.title}</h3>
          <p>{task.description}</p>
          <div className="task-meta">
            <span className={`priority-${task.priority}`}>{task.priority}</span>
            <span>Due: {new Date(task.dueDate).toLocaleDateString()}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TaskList;
```

## 6. Development Guidelines

### Code Structure

#### Backend Directory Structure
```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── models/          # Database models
│   ├── routes/          # API route definitions
│   ├── middleware/      # Custom middleware
│   ├── utils/           # Utility functions
│   ├── config/          # Configuration files
│   └── index.ts         # Application entry point
├── tests/               # Test files
├── migrations/          # Database migrations
└── package.json
```

#### Frontend Directory Structure
```
frontend/
├── src/
│   ├── components/      # Reusable UI components
│   ├── pages/           # Page components
│   ├── hooks/           # Custom React hooks
│   ├── services/        # API service clients
│   ├── store/           # Redux store configuration
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   └── App.tsx          # Main application component
├── public/              # Static assets
└── package.json
```

### Coding Standards

#### TypeScript Guidelines
- Use strict typing for all functions and variables
- Define interfaces for complex data structures
- Use enums for fixed sets of values
- Enable strict TypeScript compiler options

```typescript
// Good example
interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: Date;
  assigneeId?: string;
  creatorId: string;
  createdAt: Date;
  updatedAt: Date;
}

enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}

enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = '