"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedQualityAnalyzer = void 0;
const Logger_1 = require("../core/Logger");
/**
 * Advanced quality analyzer with comprehensive code quality metrics
 */
class AdvancedQualityAnalyzer {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Analyze code quality with comprehensive metrics
     */
    async analyzeCodeQuality(analysis) {
        this.logger.info('Starting advanced quality analysis', {
            projectName: analysis.projectName,
            fileCount: Object.keys(analysis.files).length
        });
        const report = {
            overallScore: 0,
            compilationScore: 0,
            syntaxScore: 0,
            completenessScore: 0,
            templateScore: 0,
            securityScore: 0,
            performanceScore: 0,
            maintainabilityScore: 0,
            issues: [],
            recommendations: [],
            passed: false
        };
        try {
            // 1. Compilation Analysis
            const compilationResult = await this.analyzeCompilation(analysis);
            report.compilationScore = compilationResult.score;
            report.issues.push(...compilationResult.issues);
            // 2. Syntax Analysis
            const syntaxResult = await this.analyzeSyntax(analysis);
            report.syntaxScore = syntaxResult.score;
            report.issues.push(...syntaxResult.issues);
            // 3. Completeness Analysis
            const completenessResult = await this.analyzeCompleteness(analysis);
            report.completenessScore = completenessResult.score;
            report.issues.push(...completenessResult.issues);
            // 4. Template Substitution Analysis
            const templateResult = await this.analyzeTemplateSubstitution(analysis);
            report.templateScore = templateResult.score;
            report.issues.push(...templateResult.issues);
            // 5. Security Analysis
            const securityResult = await this.analyzeSecurity(analysis);
            report.securityScore = securityResult.score;
            report.issues.push(...securityResult.issues);
            // 6. Performance Analysis
            const performanceResult = await this.analyzePerformance(analysis);
            report.performanceScore = performanceResult.score;
            report.issues.push(...performanceResult.issues);
            // 7. Maintainability Analysis
            const maintainabilityResult = await this.analyzeMaintainability(analysis);
            report.maintainabilityScore = maintainabilityResult.score;
            report.issues.push(...maintainabilityResult.issues);
            // Calculate overall score
            report.overallScore = this.calculateOverallScore(report);
            // Generate recommendations
            report.recommendations = this.generateRecommendations(report);
            // Determine if quality gates pass
            report.passed = this.determineQualityGateStatus(report);
            this.logger.info('Advanced quality analysis completed', {
                projectName: analysis.projectName,
                overallScore: report.overallScore,
                passed: report.passed,
                issueCount: report.issues.length
            });
        }
        catch (error) {
            this.logger.error('Quality analysis failed', { error });
            report.issues.push({
                type: 'error',
                category: 'compilation',
                message: `Quality analysis failed: ${error instanceof Error ? error.message : String(error)}`,
                severity: 'critical',
                autoFixable: false
            });
        }
        return report;
    }
    /**
     * Analyze compilation quality with real TypeScript compilation
     */
    async analyzeCompilation(analysis) {
        const issues = [];
        let compilationErrors = 0;
        try {
            const ts = require('typescript');
            for (const [filePath, content] of Object.entries(analysis.files)) {
                if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                    const result = ts.transpileModule(content, {
                        compilerOptions: {
                            target: ts.ScriptTarget.ES2020,
                            module: ts.ModuleKind.CommonJS,
                            strict: true,
                            skipLibCheck: true
                        },
                        reportDiagnostics: true
                    });
                    if (result.diagnostics && result.diagnostics.length > 0) {
                        for (const diagnostic of result.diagnostics) {
                            const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
                            compilationErrors++;
                            issues.push({
                                type: 'error',
                                category: 'compilation',
                                message: `TypeScript compilation error: ${message}`,
                                file: filePath,
                                severity: 'critical',
                                autoFixable: false
                            });
                        }
                    }
                }
            }
        }
        catch (error) {
            issues.push({
                type: 'warning',
                category: 'compilation',
                message: 'TypeScript compiler not available for validation',
                severity: 'medium',
                autoFixable: false
            });
        }
        // Score based on compilation success
        const score = compilationErrors === 0 ? 100 : Math.max(0, 100 - (compilationErrors * 10));
        return { score, issues };
    }
    /**
     * Analyze syntax quality
     */
    async analyzeSyntax(analysis) {
        const issues = [];
        let syntaxErrors = 0;
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for markdown artifacts
            if (content.includes('```')) {
                syntaxErrors++;
                issues.push({
                    type: 'error',
                    category: 'syntax',
                    message: 'Markdown artifacts found in generated code',
                    file: filePath,
                    severity: 'high',
                    autoFixable: true
                });
            }
            // Check for unmatched braces
            const openBraces = (content.match(/\{/g) || []).length;
            const closeBraces = (content.match(/\}/g) || []).length;
            if (openBraces !== closeBraces) {
                syntaxErrors++;
                issues.push({
                    type: 'error',
                    category: 'syntax',
                    message: `Unmatched braces: ${openBraces} open, ${closeBraces} close`,
                    file: filePath,
                    severity: 'critical',
                    autoFixable: false
                });
            }
            // Check for incomplete function calls
            if (/\(\s*;/.test(content)) {
                syntaxErrors++;
                issues.push({
                    type: 'error',
                    category: 'syntax',
                    message: 'Incomplete function calls detected',
                    file: filePath,
                    severity: 'high',
                    autoFixable: true
                });
            }
        }
        const score = Math.max(0, 100 - (syntaxErrors * 15));
        return { score, issues };
    }
    /**
     * Analyze code completeness
     */
    async analyzeCompleteness(analysis) {
        const issues = [];
        let completenessScore = 100;
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for TODO comments or placeholder code
            if (/TODO|FIXME|PLACEHOLDER/i.test(content)) {
                completenessScore -= 10;
                issues.push({
                    type: 'warning',
                    category: 'syntax',
                    message: 'TODO or placeholder code found',
                    file: filePath,
                    severity: 'medium',
                    autoFixable: false
                });
            }
            // Check for empty functions
            if (/function\s+\w+\s*\([^)]*\)\s*\{\s*\}/.test(content)) {
                completenessScore -= 15;
                issues.push({
                    type: 'warning',
                    category: 'syntax',
                    message: 'Empty function implementations found',
                    file: filePath,
                    severity: 'medium',
                    autoFixable: false
                });
            }
        }
        return { score: Math.max(0, completenessScore), issues };
    }
    /**
     * Analyze template substitution quality
     */
    async analyzeTemplateSubstitution(analysis) {
        const issues = [];
        let templateErrors = 0;
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for unsubstituted template variables
            const templatePatterns = [
                /undefined Application/g,
                /undefined management/g,
                /\{[^}]+\}/g
            ];
            for (const pattern of templatePatterns) {
                const matches = content.match(pattern);
                if (matches) {
                    templateErrors += matches.length;
                    issues.push({
                        type: 'error',
                        category: 'template',
                        message: `Unsubstituted template variables: ${matches.join(', ')}`,
                        file: filePath,
                        severity: 'high',
                        autoFixable: true
                    });
                }
            }
        }
        const score = Math.max(0, 100 - (templateErrors * 20));
        return { score, issues };
    }
    /**
     * Analyze security aspects
     */
    async analyzeSecurity(analysis) {
        const issues = [];
        let securityScore = 100;
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for hardcoded secrets
            if (/password\s*=\s*['"][^'"]+['"]|api_key\s*=\s*['"][^'"]+['"]/i.test(content)) {
                securityScore -= 30;
                issues.push({
                    type: 'error',
                    category: 'security',
                    message: 'Hardcoded credentials detected',
                    file: filePath,
                    severity: 'critical',
                    autoFixable: false
                });
            }
            // Check for SQL injection vulnerabilities
            if (/\$\{.*\}/.test(content) && content.includes('SELECT')) {
                securityScore -= 20;
                issues.push({
                    type: 'warning',
                    category: 'security',
                    message: 'Potential SQL injection vulnerability',
                    file: filePath,
                    severity: 'high',
                    autoFixable: false
                });
            }
        }
        return { score: Math.max(0, securityScore), issues };
    }
    /**
     * Analyze performance aspects
     */
    async analyzePerformance(analysis) {
        const issues = [];
        let performanceScore = 100;
        // Basic performance checks
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for inefficient patterns
            if (/for\s*\(.*in.*\)/.test(content)) {
                performanceScore -= 5;
                issues.push({
                    type: 'info',
                    category: 'performance',
                    message: 'Consider using for...of instead of for...in for arrays',
                    file: filePath,
                    severity: 'low',
                    autoFixable: true
                });
            }
        }
        return { score: Math.max(0, performanceScore), issues };
    }
    /**
     * Analyze maintainability aspects
     */
    async analyzeMaintainability(analysis) {
        const issues = [];
        let maintainabilityScore = 100;
        for (const [filePath, content] of Object.entries(analysis.files)) {
            // Check for proper TypeScript typing
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                if (content.includes(': any')) {
                    maintainabilityScore -= 5;
                    issues.push({
                        type: 'warning',
                        category: 'maintainability',
                        message: 'Use of "any" type reduces type safety',
                        file: filePath,
                        severity: 'medium',
                        autoFixable: false
                    });
                }
            }
        }
        return { score: Math.max(0, maintainabilityScore), issues };
    }
    /**
     * Calculate overall quality score
     */
    calculateOverallScore(report) {
        const weights = {
            compilation: 0.3,
            syntax: 0.2,
            completeness: 0.15,
            template: 0.15,
            security: 0.1,
            performance: 0.05,
            maintainability: 0.05
        };
        return Math.round(report.compilationScore * weights.compilation +
            report.syntaxScore * weights.syntax +
            report.completenessScore * weights.completeness +
            report.templateScore * weights.template +
            report.securityScore * weights.security +
            report.performanceScore * weights.performance +
            report.maintainabilityScore * weights.maintainability);
    }
    /**
     * Generate recommendations based on analysis
     */
    generateRecommendations(report) {
        const recommendations = [];
        if (report.compilationScore < 80) {
            recommendations.push('Fix TypeScript compilation errors to ensure code functionality');
        }
        if (report.syntaxScore < 80) {
            recommendations.push('Address syntax issues including markdown artifacts and unmatched braces');
        }
        if (report.templateScore < 80) {
            recommendations.push('Complete template variable substitution for proper project customization');
        }
        if (report.securityScore < 80) {
            recommendations.push('Address security vulnerabilities including hardcoded credentials');
        }
        return recommendations;
    }
    /**
     * Determine if quality gates pass
     */
    determineQualityGateStatus(report) {
        const criticalIssues = report.issues.filter(issue => issue.severity === 'critical').length;
        const highIssues = report.issues.filter(issue => issue.severity === 'high').length;
        return criticalIssues === 0 && highIssues <= 2 && report.overallScore >= 80;
    }
}
exports.AdvancedQualityAnalyzer = AdvancedQualityAnalyzer;
exports.default = AdvancedQualityAnalyzer;
//# sourceMappingURL=AdvancedQualityAnalyzer.js.map