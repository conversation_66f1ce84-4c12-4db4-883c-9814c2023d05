export interface QualityReport {
    overallScore: number;
    compilationScore: number;
    syntaxScore: number;
    completenessScore: number;
    templateScore: number;
    securityScore: number;
    performanceScore: number;
    maintainabilityScore: number;
    issues: QualityIssue[];
    recommendations: string[];
    passed: boolean;
}
export interface QualityIssue {
    type: 'error' | 'warning' | 'info';
    category: 'compilation' | 'syntax' | 'template' | 'security' | 'performance' | 'maintainability';
    message: string;
    file?: string;
    line?: number;
    severity: 'critical' | 'high' | 'medium' | 'low';
    autoFixable: boolean;
}
export interface GeneratedCodeAnalysis {
    files: {
        [filePath: string]: string;
    };
    projectPath: string;
    projectName: string;
    domain: string;
}
/**
 * Advanced quality analyzer with comprehensive code quality metrics
 */
export declare class AdvancedQualityAnalyzer {
    private logger;
    constructor();
    /**
     * Analyze code quality with comprehensive metrics
     */
    analyzeCodeQuality(analysis: GeneratedCodeAnalysis): Promise<QualityReport>;
    /**
     * Analyze compilation quality with real TypeScript compilation
     */
    private analyzeCompilation;
    /**
     * Analyze syntax quality
     */
    private analyzeSyntax;
    /**
     * Analyze code completeness
     */
    private analyzeCompleteness;
    /**
     * Analyze template substitution quality
     */
    private analyzeTemplateSubstitution;
    /**
     * Analyze security aspects
     */
    private analyzeSecurity;
    /**
     * Analyze performance aspects
     */
    private analyzePerformance;
    /**
     * Analyze maintainability aspects
     */
    private analyzeMaintainability;
    /**
     * Calculate overall quality score
     */
    private calculateOverallScore;
    /**
     * Generate recommendations based on analysis
     */
    private generateRecommendations;
    /**
     * Determine if quality gates pass
     */
    private determineQualityGateStatus;
}
export default AdvancedQualityAnalyzer;
//# sourceMappingURL=AdvancedQualityAnalyzer.d.ts.map