"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodingAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const AICodeGenerator_1 = require("../core/AICodeGenerator");
const ImportExportManager_1 = require("../utils/ImportExportManager");
const AdvancedImportManager_1 = require("../core/AdvancedImportManager");
const DatabaseSchemaIntegration_1 = require("../core/DatabaseSchemaIntegration");
const ServiceStandardization_1 = require("../core/ServiceStandardization");
const PerformanceOptimization_1 = require("../core/PerformanceOptimization");
const TemplateEngine_1 = require("../core/TemplateEngine");
const PostProcessingPipeline_1 = require("../core/PostProcessingPipeline");
const AdvancedQualityAnalyzer_1 = require("../quality/AdvancedQualityAnalyzer");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const typescript = __importStar(require("typescript"));
class CodingAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('CodingAgent', 'Generates complete React frontend, Express backend, PostgreSQL setup, JWT auth, and tests', `You are a senior full-stack developer responsible for generating production-ready, domain-specific code based on semantic business analysis.

Your role is to:
1. Generate domain-appropriate React frontend components with TypeScript
2. Create business-specific Express.js backend with TypeScript
3. Set up PostgreSQL database schema matching business entities
4. Implement JWT authentication with role-based access control
5. Create comprehensive tests for business logic
6. Generate all configuration files and deployment scripts

You use AI-driven code generation to create applications that accurately reflect the business domain:
- E-learning platforms with courses, lessons, and assignments
- E-commerce systems with products, orders, and inventory
- Healthcare systems with patients, appointments, and records
- Financial systems with accounts, transactions, and portfolios

Always generate code that solves the actual business problem, not generic CRUD operations.`);
        this.aiCodeGenerator = new AICodeGenerator_1.AICodeGenerator();
        this.importExportManager = new ImportExportManager_1.ImportExportManager();
        this.advancedImportManager = new AdvancedImportManager_1.AdvancedImportManager();
        this.databaseIntegration = new DatabaseSchemaIntegration_1.DatabaseSchemaIntegration();
        this.serviceStandardization = new ServiceStandardization_1.ServiceStandardization();
        this.performanceOptimization = new PerformanceOptimization_1.PerformanceOptimization();
        this.templateEngine = TemplateEngine_1.TemplateEngine.getInstance();
        this.postProcessingPipeline = new PostProcessingPipeline_1.PostProcessingPipeline();
        this.qualityAnalyzer = new AdvancedQualityAnalyzer_1.AdvancedQualityAnalyzer();
    }
    canHandle(task) {
        return task.type === 'generate';
    }
    /**
     * Enhanced code generation with comprehensive quality assurance
     */
    async generateCodeWithEnhancedQuality(semanticAnalysis, technicalSpec) {
        this.logInfo('Starting enhanced code generation with quality assurance');
        // 1. Create project context for template substitution
        const projectContext = this.templateEngine.createProjectContext(technicalSpec, semanticAnalysis);
        this.aiCodeGenerator.setProjectContext(projectContext);
        this.logInfo('Project context created', {
            projectName: projectContext.projectName,
            domain: projectContext.domain,
            entitiesCount: projectContext.entities?.length || 0
        });
        // 2. Generate code using existing logic but with enhanced processing
        // Note: This would integrate with the existing code generation methods
        const generatedCode = {
            files: {},
            projectPath: './generated-projects/' + technicalSpec.projectName.toLowerCase().replace(/\s+/g, '-'),
            packageJsons: {},
            scripts: {},
            documentation: '',
            metadata: {}
        };
        // 3. Apply comprehensive quality analysis
        const qualityReport = await this.qualityAnalyzer.analyzeCodeQuality({
            files: generatedCode.files,
            projectPath: generatedCode.projectPath,
            projectName: technicalSpec.projectName,
            domain: semanticAnalysis.domain
        });
        this.logInfo('Quality analysis completed', {
            overallScore: qualityReport.overallScore,
            passed: qualityReport.passed,
            issuesCount: qualityReport.issues.length
        });
        // 4. Log quality report details
        if (qualityReport.issues.length > 0) {
            console.log('🔍 Quality Analysis Results:');
            console.log(`📊 Overall Score: ${qualityReport.overallScore}/100`);
            console.log(`✅ Quality Gates: ${qualityReport.passed ? 'PASSED' : 'FAILED'}`);
            console.log(`🐛 Issues Found: ${qualityReport.issues.length}`);
            const criticalIssues = qualityReport.issues.filter(i => i.severity === 'critical');
            const highIssues = qualityReport.issues.filter(i => i.severity === 'high');
            if (criticalIssues.length > 0) {
                console.log('❌ Critical Issues:');
                criticalIssues.forEach(issue => console.log(`   - ${issue.message} (${issue.file})`));
            }
            if (highIssues.length > 0) {
                console.log('⚠️  High Priority Issues:');
                highIssues.forEach(issue => console.log(`   - ${issue.message} (${issue.file})`));
            }
            if (qualityReport.recommendations.length > 0) {
                console.log('💡 Recommendations:');
                qualityReport.recommendations.forEach(rec => console.log(`   - ${rec}`));
            }
        }
        else {
            console.log('✅ Quality Analysis: All checks passed!');
        }
        // 5. Return enhanced generated code with quality metrics
        return {
            ...generatedCode,
            metadata: {
                ...(generatedCode.metadata || {}),
                qualityReport
            }
        };
    }
    /**
     * Apply Phase 3 optimizations to generated code
     */
    async applyPhase3Optimizations(generatedCode, semanticAnalysis) {
        this.logInfo('Applying Phase 3 optimizations to generated code');
        const optimizedCode = { ...generatedCode };
        // 1. Apply Advanced Import Management
        for (const [filePath, content] of Object.entries(optimizedCode.files)) {
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                // Register file for import analysis
                this.advancedImportManager.registerFile(filePath, content);
            }
        }
        // Analyze and resolve import conflicts
        const conflicts = this.advancedImportManager.analyzeConflicts();
        this.logInfo('Import conflicts detected', { conflictsCount: conflicts.length });
        // Optimize imports for each TypeScript file
        for (const [filePath, content] of Object.entries(optimizedCode.files)) {
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                const optimizedContent = this.advancedImportManager.optimizeFileImports(filePath, content);
                optimizedCode.files[filePath] = optimizedContent;
            }
        }
        // 2. Apply Service Standardization
        const serviceFiles = Object.keys(optimizedCode.files).filter(path => path.includes('Service.ts') || path.includes('Routes.ts') || path.includes('Component.tsx'));
        for (const filePath of serviceFiles) {
            const content = optimizedCode.files[filePath];
            let serviceType;
            if (filePath.includes('Routes.ts')) {
                serviceType = 'express_route';
            }
            else if (filePath.includes('Component.tsx')) {
                serviceType = 'react_component';
            }
            else if (filePath.includes('Service.ts')) {
                serviceType = 'database_service';
            }
            else {
                serviceType = 'utility_service';
            }
            const serviceName = path.basename(filePath, path.extname(filePath));
            const standardizedService = this.serviceStandardization.standardizeService(serviceName, serviceType, content, []);
            optimizedCode.files[filePath] = standardizedService.content;
        }
        // 3. Apply Database Schema Integration
        if (semanticAnalysis.entities && semanticAnalysis.entities.length > 0) {
            try {
                const databaseTables = this.databaseIntegration.generateSchemaFromEntities(semanticAnalysis.entities, semanticAnalysis);
                // Ensure databaseTables is an array
                if (Array.isArray(databaseTables) && databaseTables.length > 0) {
                    // Generate optimized database schema file
                    const schemaSQL = databaseTables.map(table => this.generateCreateTableSQL(table)).join('\n\n');
                    optimizedCode.files['database/schema.sql'] = schemaSQL;
                    // Add migration files
                    const migration = this.databaseIntegration.generateMigration([], databaseTables, 'initial_schema');
                    if (migration && migration.up && Array.isArray(migration.up)) {
                        optimizedCode.files[`database/migrations/${migration.version}_${migration.name}.sql`] = migration.up.join('\n');
                    }
                    else {
                        this.logWarn('Migration generation returned invalid result', { migration });
                    }
                }
                else {
                    this.logWarn('Database schema generation returned empty or invalid result', {
                        databaseTables: databaseTables,
                        entitiesCount: semanticAnalysis.entities.length
                    });
                }
            }
            catch (error) {
                this.logError('Database schema integration failed', {
                    error: error instanceof Error ? error.message : String(error),
                    entitiesCount: semanticAnalysis.entities.length
                });
                // Continue without database schema
            }
        }
        // 4. Apply Performance Optimizations
        const optimizationReport = this.performanceOptimization.generateOptimizationReport();
        this.logInfo('Performance optimizations applied', {
            tokensSaved: optimizationReport.metrics.tokensSaved,
            cacheHitRate: optimizationReport.metrics.cacheHitRate
        });
        return optimizedCode;
    }
    /**
     * Generate CREATE TABLE SQL for database table
     */
    generateCreateTableSQL(table) {
        try {
            // Ensure table has required properties
            if (!table || !table.name || !Array.isArray(table.columns)) {
                this.logWarn('Invalid table structure for SQL generation', { table });
                return `-- Invalid table structure for ${table?.name || 'unknown'}`;
            }
            const columns = table.columns.map((col) => {
                if (!col || !col.name || !col.type) {
                    return `  -- Invalid column structure`;
                }
                let sql = `  ${col.name} ${col.type}`;
                if (!col.nullable)
                    sql += ' NOT NULL';
                if (col.defaultValue)
                    sql += ` DEFAULT ${col.defaultValue}`;
                if (col.unique)
                    sql += ' UNIQUE';
                return sql;
            }).join(',\n');
            let sql = `CREATE TABLE ${table.name} (\n${columns}`;
            // Add primary key if exists
            if (Array.isArray(table.primaryKey) && table.primaryKey.length > 0) {
                const primaryKey = `  PRIMARY KEY (${table.primaryKey.join(', ')})`;
                sql += `,\n${primaryKey}`;
            }
            // Add foreign keys if exist
            if (Array.isArray(table.foreignKeys) && table.foreignKeys.length > 0) {
                const foreignKeys = table.foreignKeys.map((fk) => {
                    if (!fk || !fk.name || !Array.isArray(fk.columns)) {
                        return `  -- Invalid foreign key structure`;
                    }
                    return `  CONSTRAINT ${fk.name} FOREIGN KEY (${fk.columns.join(', ')}) REFERENCES ${fk.referencedTable}(${fk.referencedColumns.join(', ')}) ON DELETE ${fk.onDelete || 'CASCADE'} ON UPDATE ${fk.onUpdate || 'CASCADE'}`;
                }).join(',\n');
                sql += `,\n${foreignKeys}`;
            }
            sql += '\n);';
            // Add indexes if exist
            if (Array.isArray(table.indexes)) {
                table.indexes.forEach((index) => {
                    if (index && index.name && Array.isArray(index.columns)) {
                        const uniqueKeyword = index.unique ? 'UNIQUE ' : '';
                        sql += `\nCREATE ${uniqueKeyword}INDEX ${index.name} ON ${table.name} (${index.columns.join(', ')});`;
                    }
                });
            }
            return sql;
        }
        catch (error) {
            this.logError('Error generating CREATE TABLE SQL', {
                error: error instanceof Error ? error.message : String(error),
                tableName: table?.name || 'unknown'
            });
            return `-- Error generating table ${table?.name || 'unknown'}: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    async execute(task) {
        try {
            this.logInfo('Starting AI-driven code generation phase', { taskId: task.id });
            // Get technical spec and semantic analysis from previous result - handle multiple parameter formats
            const previousResult = task.parameters.previousResult;
            const technicalSpec = previousResult?.technicalSpec || task.parameters.technicalSpec;
            const businessLogic = previousResult?.businessLogic || task.parameters.businessLogic;
            // Validate and resolve file structure conflicts with BusinessLogicAgent
            await this.validateAndResolveFileStructureConflicts(task.parameters.projectPath, businessLogic);
            // Extract semantic analysis from multiple possible sources
            const semanticAnalysis = previousResult?.semanticAnalysis ||
                previousResult?.metadata?.semanticAnalysis ||
                task.parameters.semanticAnalysis ||
                task.parameters.domainAnalysis;
            const requirements = task.parameters.requirements || [];
            if (!technicalSpec) {
                throw new Error('Technical specification not provided from previous phase');
            }
            if (!semanticAnalysis) {
                throw new Error('Semantic analysis not provided - required for AI-driven generation');
            }
            if (!technicalSpec.projectName) {
                throw new Error('Project name not specified in technical specification');
            }
            // Validate semantic analysis structure and add defensive checks
            if (!semanticAnalysis.entities || !Array.isArray(semanticAnalysis.entities)) {
                this.logWarn('Semantic analysis missing entities array, initializing empty array', {
                    semanticAnalysis: JSON.stringify(semanticAnalysis, null, 2)
                });
                semanticAnalysis.entities = [];
            }
            // Validate each entity has required properties
            semanticAnalysis.entities.forEach((entity, index) => {
                if (!entity.validations || !Array.isArray(entity.validations)) {
                    this.logWarn(`Entity ${entity.name || index} missing validations array, initializing empty array`);
                    entity.validations = [];
                }
                if (!entity.operations || !Array.isArray(entity.operations)) {
                    this.logWarn(`Entity ${entity.name || index} missing operations array, initializing empty array`);
                    entity.operations = [];
                }
            });
            // Create project directory in a more accessible location
            const projectPath = path.join(process.cwd(), 'generated-projects', technicalSpec.projectName);
            await this.createProjectStructure(projectPath, technicalSpec);
            this.logInfo('Project directory created', { projectPath });
            // Generate all code files using AI-driven generation
            const generatedCode = await this.generateAllCodeWithAI(projectPath, technicalSpec, semanticAnalysis, businessLogic, requirements);
            // Apply Phase 3 optimizations
            const optimizedCode = await this.applyPhase3Optimizations(generatedCode, semanticAnalysis);
            // Write optimized files to disk
            await this.writeFilesToDisk(optimizedCode);
            this.logInfo('AI-driven code generation completed successfully', {
                taskId: task.id,
                projectPath: generatedCode.projectPath,
                fileCount: Object.keys(generatedCode.files).length,
                domain: semanticAnalysis.domain,
                entitiesGenerated: semanticAnalysis.entities.length,
            });
            return {
                success: true,
                data: generatedCode,
                metadata: {
                    phase: 'coding',
                    timestamp: new Date().toISOString(),
                    projectPath: generatedCode.projectPath,
                    generationMethod: 'ai-driven',
                    businessDomain: semanticAnalysis.domain,
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown coding error';
            this.logError('AI-driven code generation failed', { taskId: task.id, error: errorMessage });
            return {
                success: false,
                error: errorMessage,
            };
        }
    }
    async generateAllCodeWithAI(projectPath, technicalSpec, semanticAnalysis, businessLogic, requirements) {
        const files = {};
        const packageJsons = {};
        const scripts = {};
        this.logInfo('Generating code using AI for business domain', {
            domain: semanticAnalysis.domain,
            entitiesCount: semanticAnalysis.entities.length
        });
        // Generate database schema based on business entities
        await this.generateDatabaseSchemaWithAI(files, semanticAnalysis, technicalSpec);
        // Generate backend code for each business entity
        await this.generateBackendCodeWithAI(files, semanticAnalysis, technicalSpec, businessLogic);
        // Generate frontend components for each business entity
        await this.generateFrontendCodeWithAI(files, semanticAnalysis, technicalSpec, businessLogic);
        // Generate configuration files
        await this.generateConfigurationFiles(files, packageJsons, scripts, technicalSpec, semanticAnalysis);
        // PRIORITY FIX: Generate essential missing files
        await this.generateEssentialFiles(files, technicalSpec, semanticAnalysis);
        // Generate tests
        await this.generateTestsWithAI(files, semanticAnalysis, technicalSpec);
        // Generate documentation
        const documentation = await this.generateDocumentationWithAI(semanticAnalysis, technicalSpec);
        return {
            projectPath,
            files,
            packageJsons,
            scripts,
            documentation,
        };
    }
    async generateDatabaseSchemaWithAI(files, semanticAnalysis, technicalSpec) {
        // Defensive check for entities
        if (!semanticAnalysis.entities || !Array.isArray(semanticAnalysis.entities)) {
            this.logWarn('No entities found in semantic analysis, skipping database schema generation');
            return;
        }
        this.logInfo('Generating database schema with AI', { entitiesCount: semanticAnalysis.entities.length });
        const context = {
            projectName: technicalSpec.projectName,
            businessDomain: semanticAnalysis,
            targetFramework: 'PostgreSQL',
            architecturalPatterns: ['Database First', 'Foreign Key Constraints'],
            existingEntities: (semanticAnalysis.entities || []).map(e => e.name || 'entity'),
            relatedEntities: semanticAnalysis.entities || [],
        };
        const schemaCode = await this.aiCodeGenerator.generateDatabaseSchema(semanticAnalysis.entities, context);
        files['database/init.sql'] = schemaCode.content;
        files['database/migrations/001_initial_schema.sql'] = schemaCode.content;
        // Generate database connection and models
        for (const entity of semanticAnalysis.entities) {
            // Ensure entity has required arrays
            if (!entity.operations || !Array.isArray(entity.operations)) {
                entity.operations = [];
            }
            if (!entity.validations || !Array.isArray(entity.validations)) {
                entity.validations = [];
            }
            const modelRequest = {
                type: 'model-class',
                entity,
                context,
                specifications: {
                    fileName: `${entity.name}Model.ts`,
                    purpose: `Data model for ${entity.name} entity`,
                    requirements: [`Represent ${entity.name} business entity`, 'Include validation', 'Support database operations'],
                    dependencies: ['pg', 'joi'],
                    businessRules: (entity.operations || []).flatMap(op => {
                        if (typeof op === 'string')
                            return [];
                        if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                            return op.businessRules;
                        return [];
                    }),
                    validationRules: (entity.validations && Array.isArray(entity.validations)) ? entity.validations.map(v => (typeof v === 'string' ? v : (v && v.rule) || 'validation')) : [],
                    securityRequirements: ['Input validation', 'SQL injection prevention'],
                },
            };
            const modelCode = await this.aiCodeGenerator.generateServiceClass(modelRequest);
            files[`backend/src/models/${entity.name}Model.ts`] = modelCode.content;
        }
    }
    async generateBackendCodeWithAI(files, semanticAnalysis, technicalSpec, businessLogic) {
        // Defensive check for parameters
        if (!files || typeof files !== 'object') {
            this.logError('Files object is undefined or invalid in generateBackendCodeWithAI');
            return;
        }
        // Defensive check for entities
        if (!semanticAnalysis || !semanticAnalysis.entities || !Array.isArray(semanticAnalysis.entities)) {
            this.logWarn('No entities found in semantic analysis, skipping backend code generation');
            return;
        }
        this.logInfo('Generating backend code with AI', { entitiesCount: semanticAnalysis.entities.length });
        const context = {
            projectName: technicalSpec.projectName,
            businessDomain: semanticAnalysis,
            targetFramework: 'Express.js with TypeScript',
            architecturalPatterns: ['MVC', 'Repository Pattern', 'Service Layer'],
            existingEntities: (semanticAnalysis.entities || []).map(e => e.name || 'entity'),
            relatedEntities: semanticAnalysis.entities || [],
        };
        // Generate routes for each entity
        for (const entity of semanticAnalysis.entities) {
            try {
                // Defensive check for entity
                if (!entity || !entity.name) {
                    this.logWarn('Invalid entity found, skipping', { entity });
                    continue;
                }
                // Ensure entity has required arrays with deep validation
                if (!entity.operations || !Array.isArray(entity.operations)) {
                    this.logWarn(`Entity ${entity.name} missing operations array, initializing`, { entity });
                    entity.operations = [];
                }
                if (!entity.validations || !Array.isArray(entity.validations)) {
                    this.logWarn(`Entity ${entity.name} missing validations array, initializing`, { entity });
                    entity.validations = [];
                }
                // Validate operations array structure - always process the array
                entity.operations = (entity.operations || []).map(op => {
                    if (!op || typeof op !== 'object') {
                        this.logWarn(`Invalid operation found in entity ${entity.name}, replacing with default`, { op });
                        return {
                            name: 'defaultOperation',
                            type: 'read',
                            description: 'Default operation',
                            parameters: [],
                            businessRules: []
                        };
                    }
                    if (!op.businessRules || !Array.isArray(op.businessRules)) {
                        op.businessRules = [];
                    }
                    return op;
                });
                const routeRequest = {
                    type: 'backend-route',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name.toLowerCase()}Routes.ts`,
                        purpose: `REST API routes for ${entity.name} entity`,
                        requirements: [
                            `CRUD operations for ${entity.name}`,
                            'Authentication and authorization',
                            'Input validation',
                            'Error handling',
                            'Business rule enforcement',
                        ],
                        dependencies: ['express', 'joi', 'jsonwebtoken'],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: (entity.validations && Array.isArray(entity.validations)) ? entity.validations.map(v => (typeof v === 'string' ? v : (v && v.rule) || 'validation')) : [],
                        securityRequirements: ['JWT authentication', 'Role-based access control', 'Input sanitization'],
                    },
                };
                const routeCode = await this.aiCodeGenerator.generateExpressRoute(routeRequest);
                if (routeCode && routeCode.content) {
                    files[`backend/src/routes/${entity.name.toLowerCase()}Routes.ts`] = routeCode.content;
                }
                else {
                    this.logWarn(`Failed to generate route code for entity ${entity.name}`, { routeCode });
                }
                // Generate service class for business logic
                const serviceRequest = {
                    type: 'service-class',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name}Service.ts`,
                        purpose: `Business logic service for ${entity.name} entity`,
                        requirements: [
                            `Business operations for ${entity.name}`,
                            'Data validation',
                            'Business rule enforcement',
                            'Error handling',
                        ],
                        dependencies: ['pg'],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: (entity.validations && Array.isArray(entity.validations)) ? entity.validations.map(v => (typeof v === 'string' ? v : (v && v.rule) || 'validation')) : [],
                        securityRequirements: ['Data validation', 'Business rule enforcement'],
                    },
                };
                const serviceCode = await this.aiCodeGenerator.generateServiceClass(serviceRequest);
                if (serviceCode && serviceCode.content) {
                    files[`backend/src/services/${entity.name}Service.ts`] = serviceCode.content;
                }
                else {
                    this.logWarn(`Failed to generate service code for entity ${entity.name}`, { serviceCode });
                }
                // Generate controller class for handling HTTP requests
                const controllerRequest = {
                    type: 'service-class',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name}Controller.ts`,
                        purpose: `HTTP request controller for ${entity.name} entity`,
                        requirements: [
                            `HTTP request handling for ${entity.name}`,
                            'Request validation',
                            'Response formatting',
                            'Error handling',
                            'Service layer integration',
                        ],
                        dependencies: ['express', `../services/${entity.name}Service`],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: (entity.validations && Array.isArray(entity.validations)) ? entity.validations.map(v => (typeof v === 'string' ? v : (v && v.rule) || 'validation')) : [],
                        securityRequirements: ['Request validation', 'Authentication check'],
                    },
                };
                const controllerCode = await this.aiCodeGenerator.generateServiceClass(controllerRequest);
                if (controllerCode && controllerCode.content) {
                    files[`backend/src/controllers/${entity.name}Controller.ts`] = controllerCode.content;
                }
                else {
                    this.logWarn(`Failed to generate controller code for entity ${entity.name}`, { controllerCode });
                }
            }
            catch (error) {
                this.logError(`Error generating backend code for entity ${entity.name}`, {
                    error: error instanceof Error ? error.message : String(error),
                    entityName: entity?.name || 'unknown'
                });
            }
        }
        // Generate common middleware
        try {
            // Authentication middleware
            const authMiddleware = this.generateAuthMiddleware();
            files['backend/src/middleware/auth.ts'] = authMiddleware;
            // Error handling middleware
            const errorMiddleware = this.generateErrorHandler();
            files['backend/src/middleware/errorHandler.ts'] = errorMiddleware;
            // Logging utility
            const loggingUtility = this.generateLogger();
            files['backend/src/utils/logger.ts'] = loggingUtility;
        }
        catch (error) {
            this.logError('Error generating middleware', { error: error instanceof Error ? error.message : String(error) });
        }
        // Generate main server file
        try {
            const serverCode = await this.generateServerFileWithAI(semanticAnalysis, technicalSpec);
            if (serverCode && typeof serverCode === 'string') {
                files['backend/src/server.ts'] = serverCode;
            }
            else {
                this.logWarn('Failed to generate server file', { serverCode });
            }
        }
        catch (error) {
            this.logError('Error generating server file', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    async generateFrontendCodeWithAI(files, semanticAnalysis, technicalSpec, businessLogic) {
        // Defensive check for parameters
        if (!files || typeof files !== 'object') {
            this.logError('Files object is undefined or invalid in generateFrontendCodeWithAI');
            return;
        }
        // Defensive check for entities
        if (!semanticAnalysis || !semanticAnalysis.entities || !Array.isArray(semanticAnalysis.entities)) {
            this.logWarn('No entities found in semantic analysis, skipping frontend code generation');
            return;
        }
        this.logInfo('Generating frontend code with AI', { entitiesCount: semanticAnalysis.entities.length });
        const context = {
            projectName: technicalSpec.projectName,
            businessDomain: semanticAnalysis,
            targetFramework: 'React with TypeScript',
            architecturalPatterns: ['Component-based', 'Hooks', 'Context API'],
            existingEntities: (semanticAnalysis.entities || []).map(e => e.name || 'entity'),
            relatedEntities: semanticAnalysis.entities || [],
        };
        // Generate components for each entity
        for (const entity of semanticAnalysis.entities) {
            try {
                // Defensive check for entity
                if (!entity || !entity.name) {
                    this.logWarn('Invalid entity found, skipping', { entity });
                    continue;
                }
                // Ensure entity has required arrays with deep validation
                if (!entity.operations || !Array.isArray(entity.operations)) {
                    this.logWarn(`Entity ${entity.name} missing operations array, initializing`, { entity });
                    entity.operations = [];
                }
                if (!entity.validations || !Array.isArray(entity.validations)) {
                    this.logWarn(`Entity ${entity.name} missing validations array, initializing`, { entity });
                    entity.validations = [];
                }
                // Validate operations array structure - always process the array
                entity.operations = (entity.operations || []).map(op => {
                    if (!op || typeof op !== 'object') {
                        this.logWarn(`Invalid operation found in entity ${entity.name}, replacing with default`, { op });
                        return {
                            name: 'defaultOperation',
                            type: 'read',
                            description: 'Default operation',
                            parameters: [],
                            businessRules: []
                        };
                    }
                    if (!op.businessRules || !Array.isArray(op.businessRules)) {
                        op.businessRules = [];
                    }
                    return op;
                });
                // List component
                const listRequest = {
                    type: 'frontend-component',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name}List.tsx`,
                        purpose: `List view component for ${entity.name} entities`,
                        requirements: [
                            `Display list of ${entity.name} entities`,
                            'Search and filter functionality',
                            'Pagination support',
                            'Loading and error states',
                            'Action buttons for CRUD operations',
                        ],
                        dependencies: ['react', 'axios'],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: [],
                        securityRequirements: ['Authentication check', 'Authorization validation'],
                    },
                };
                const listCode = await this.aiCodeGenerator.generateReactComponent(listRequest);
                if (listCode && listCode.content) {
                    files[`frontend/src/components/${entity.name}List.tsx`] = listCode.content;
                }
                else {
                    this.logWarn(`Failed to generate list component for entity ${entity.name}`, { listCode });
                }
                // Form component
                const formRequest = {
                    type: 'frontend-component',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name}Form.tsx`,
                        purpose: `Form component for creating/editing ${entity.name} entities`,
                        requirements: [
                            `Form for ${entity.name} creation and editing`,
                            'Field validation',
                            'Error handling',
                            'Loading states',
                            'Success feedback',
                        ],
                        dependencies: ['react', 'axios'],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: (entity.validations && Array.isArray(entity.validations)) ? entity.validations.map(v => (typeof v === 'string' ? v : (v && v.rule) || 'validation')) : [],
                        securityRequirements: ['Input validation', 'CSRF protection'],
                    },
                };
                const formCode = await this.aiCodeGenerator.generateReactComponent(formRequest);
                if (formCode && formCode.content) {
                    files[`frontend/src/components/${entity.name}Form.tsx`] = formCode.content;
                }
                else {
                    this.logWarn(`Failed to generate form component for entity ${entity.name}`, { formCode });
                }
                // Detail component
                const detailRequest = {
                    type: 'frontend-component',
                    entity,
                    context,
                    specifications: {
                        fileName: `${entity.name}Detail.tsx`,
                        purpose: `Detail view component for ${entity.name} entity`,
                        requirements: [
                            `Display detailed view of ${entity.name}`,
                            'Edit and delete actions',
                            'Related entity information',
                            'Loading and error states',
                        ],
                        dependencies: ['react', 'axios', 'react-router-dom'],
                        businessRules: (entity.operations || []).flatMap(op => {
                            if (typeof op === 'string')
                                return [];
                            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                                return op.businessRules;
                            return [];
                        }),
                        validationRules: [],
                        securityRequirements: ['Authentication check', 'Authorization validation'],
                    },
                };
                const detailCode = await this.aiCodeGenerator.generateReactComponent(detailRequest);
                if (detailCode && detailCode.content) {
                    files[`frontend/src/components/${entity.name}Detail.tsx`] = detailCode.content;
                }
                else {
                    this.logWarn(`Failed to generate detail component for entity ${entity.name}`, { detailCode });
                }
            }
            catch (error) {
                this.logError(`Error generating frontend components for entity ${entity.name}`, {
                    error: error instanceof Error ? error.message : String(error),
                    entityName: entity?.name || 'unknown'
                });
            }
        }
        // Generate main App component
        try {
            const appCode = await this.generateAppComponentWithAI(semanticAnalysis, technicalSpec);
            if (appCode && typeof appCode === 'string') {
                files['frontend/src/App.tsx'] = appCode;
            }
            else {
                this.logWarn('Failed to generate App component', { appCode });
            }
        }
        catch (error) {
            this.logError('Error generating App component', { error: error instanceof Error ? error.message : String(error) });
        }
        // Generate dashboard component
        try {
            const dashboardCode = await this.generateDashboardComponentWithAI(semanticAnalysis, technicalSpec);
            if (dashboardCode && typeof dashboardCode === 'string') {
                files['frontend/src/components/Dashboard.tsx'] = dashboardCode;
            }
            else {
                this.logWarn('Failed to generate Dashboard component', { dashboardCode });
            }
        }
        catch (error) {
            this.logError('Error generating Dashboard component', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    async generateTestsWithAI(files, semanticAnalysis, technicalSpec) {
        // Defensive check for entities
        if (!semanticAnalysis.entities || !Array.isArray(semanticAnalysis.entities)) {
            this.logWarn('No entities found in semantic analysis, skipping test generation');
            return;
        }
        this.logInfo('Generating tests with AI', { entitiesCount: semanticAnalysis.entities.length });
        // Generate backend tests for each entity
        for (const entity of semanticAnalysis.entities) {
            // Ensure entity has required arrays
            if (!entity.operations || !Array.isArray(entity.operations)) {
                entity.operations = [];
            }
            const testPrompt = `Generate comprehensive Jest tests for ${entity.name} in a ${semanticAnalysis.domain} application.

Entity: ${entity.name}
Operations: ${(entity.operations || []).map(op => {
                if (typeof op === 'string')
                    return op;
                if (op && typeof op === 'object' && op.name)
                    return op.name;
                return 'operation';
            }).join(', ')}
Business Rules: ${(entity.operations || []).flatMap(op => {
                if (typeof op === 'string')
                    return [];
                if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                    return op.businessRules;
                return [];
            }).join(', ')}

Generate tests for:
1. API endpoints (routes)
2. Service layer (business logic)
3. Data model validation
4. Error handling
5. Authentication and authorization

Return complete TypeScript test file with proper mocking and assertions.`;
            const testCode = await this.generateSingleLLMResponse(testPrompt, {
                temperature: 0.2,
                maxTokens: 3000
            });
            files[`backend/tests/${entity.name.toLowerCase()}.test.ts`] = testCode;
        }
        // Generate frontend tests
        for (const entity of semanticAnalysis.entities) {
            const frontendTestPrompt = `Generate React Testing Library tests for ${entity.name} components in a ${semanticAnalysis.domain} application.

Components to test:
- ${entity.name}List
- ${entity.name}Form
- ${entity.name}Detail

Generate tests for:
1. Component rendering
2. User interactions
3. Form validation
4. API calls
5. Error states
6. Loading states

Return complete TypeScript test file with proper mocking and assertions.`;
            const frontendTestCode = await this.generateSingleLLMResponse(frontendTestPrompt, {
                temperature: 0.2,
                maxTokens: 3000
            });
            files[`frontend/src/components/__tests__/${entity.name}.test.tsx`] = frontendTestCode;
        }
    }
    async generateConfigurationFiles(files, packageJsons, scripts, technicalSpec, semanticAnalysis) {
        this.logInfo('Generating configuration files with AI');
        // Generate package.json files
        const frontendPackageJson = await this.generatePackageJsonWithAI('frontend', semanticAnalysis, technicalSpec);
        const backendPackageJson = await this.generatePackageJsonWithAI('backend', semanticAnalysis, technicalSpec);
        // Clean and parse JSON responses (remove markdown code blocks if present)
        const cleanFrontendJson = this.cleanJsonResponse(frontendPackageJson);
        const cleanBackendJson = this.cleanJsonResponse(backendPackageJson);
        packageJsons['frontend/package.json'] = JSON.parse(cleanFrontendJson);
        packageJsons['backend/package.json'] = JSON.parse(cleanBackendJson);
        // Generate Docker configuration
        const dockerComposeCode = await this.generateDockerComposeWithAI(semanticAnalysis, technicalSpec);
        files['docker-compose.yml'] = dockerComposeCode;
        // Generate environment files
        const envExampleCode = await this.generateEnvExampleWithAI(semanticAnalysis, technicalSpec);
        files['.env.example'] = envExampleCode;
        // Generate README
        const readmeCode = await this.generateReadmeWithAI(semanticAnalysis, technicalSpec);
        files['README.md'] = readmeCode;
    }
    async generateDocumentationWithAI(semanticAnalysis, technicalSpec) {
        const docPrompt = `Generate comprehensive documentation for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate documentation that includes:
1. Project overview and purpose
2. Architecture description
3. API documentation
4. Setup and installation instructions
5. Usage examples
6. Development guidelines

Return comprehensive markdown documentation.`;
        return await this.generateSingleLLMResponse(docPrompt, {
            temperature: 0.3,
            maxTokens: 4000
        });
    }
    async generateServerFileWithAI(semanticAnalysis, technicalSpec) {
        const serverPrompt = `Generate a main Express.js server file for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate a complete server.ts file that:
1. Sets up Express application
2. Configures middleware (CORS, helmet, rate limiting)
3. Sets up database connection
4. Imports and uses all route modules
5. Implements error handling
6. Starts the server

Include proper TypeScript types and error handling.

Return ONLY the TypeScript code, no additional text.`;
        return await this.generateSingleLLMResponse(serverPrompt, {
            temperature: 0.2,
            maxTokens: 3000
        });
    }
    async generateAppComponentWithAI(semanticAnalysis, technicalSpec) {
        const appPrompt = `Generate a complete App.tsx file for the "${technicalSpec.projectName}" application.

CRITICAL FORMATTING REQUIREMENTS:
- Return ONLY raw TypeScript React code
- DO NOT wrap in markdown blocks (no \`\`\`typescript, \`\`\`tsx, or \`\`\`)
- DO NOT include any \`\`\` markers anywhere in the response
- DO NOT add explanatory text before or after the code
- Ensure all template literals are properly closed with backticks
- Replace ALL placeholder text with actual content
- Use "${technicalSpec.projectName}" as the application name throughout

PROJECT CONTEXT:
- Name: ${technicalSpec.projectName}
- Description: ${technicalSpec.description || `${semanticAnalysis.domain} management application`}
- Domain: ${semanticAnalysis.domain}
- Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

REQUIREMENTS:
1. Set up React Router with proper routing
2. Include authentication context and providers
3. Define routes for all entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}
4. Implement navigation with proper links
5. Include error boundaries for error handling
6. Use Material-UI components for consistent styling
7. Include proper TypeScript types and interfaces

IMPORTANT: The generated code must compile without errors. Ensure:
- All imports are correct and complete
- All components are properly typed
- All functions have complete parameter lists
- All template literals are properly closed
- No undefined variables or missing dependencies

Return the complete, compilable TypeScript React code without any markdown formatting.`;
        return await this.generateSingleLLMResponse(appPrompt, {
            temperature: 0.2,
            maxTokens: 3000
        });
    }
    async generateDashboardComponentWithAI(semanticAnalysis, technicalSpec) {
        const dashboardPrompt = `Generate a Dashboard React component for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate a dashboard that:
1. Shows overview statistics
2. Provides quick access to main entities
3. Displays recent activity
4. Includes navigation to entity management

Return ONLY the TypeScript React code, no additional text.`;
        return await this.generateSingleLLMResponse(dashboardPrompt, {
            temperature: 0.2,
            maxTokens: 3000
        });
    }
    async generatePackageJsonWithAI(type, semanticAnalysis, technicalSpec) {
        const packagePrompt = `Generate a package.json file for the ${type} of a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}
Type: ${type}

Generate a complete package.json that includes:
1. Appropriate dependencies for ${type} development
2. Scripts for development, build, test, and start
3. Proper project metadata
4. TypeScript configuration
5. Domain-specific dependencies

For frontend: React, TypeScript, testing libraries, routing
For backend: Express, TypeScript, database drivers, authentication

Return ONLY valid JSON, no additional text.`;
        return await this.generateSingleLLMResponse(packagePrompt, {
            temperature: 0.2,
            maxTokens: 2000
        });
    }
    async generateDockerComposeWithAI(semanticAnalysis, technicalSpec) {
        const dockerPrompt = `Generate a docker-compose.yml file for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}

Generate a docker-compose file that includes:
1. Frontend service (React)
2. Backend service (Express.js)
3. PostgreSQL database service
4. Proper networking and volumes
5. Environment variables

Return ONLY the YAML content, no additional text.`;
        return await this.generateSingleLLMResponse(dockerPrompt, {
            temperature: 0.2,
            maxTokens: 2000
        });
    }
    async generateEnvExampleWithAI(semanticAnalysis, technicalSpec) {
        const envPrompt = `Generate a .env.example file for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}

Include environment variables for:
1. Database connection
2. JWT secrets
3. API keys
4. Port configurations
5. Domain-specific settings

Return ONLY the environment file content, no additional text.`;
        return await this.generateSingleLLMResponse(envPrompt, {
            temperature: 0.2,
            maxTokens: 1000
        });
    }
    async generateReadmeWithAI(semanticAnalysis, technicalSpec) {
        const readmePrompt = `Generate a README.md file for a ${semanticAnalysis.domain} application.

Project: ${technicalSpec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate a comprehensive README that includes:
1. Project description and purpose
2. Features and capabilities
3. Installation instructions
4. Usage examples
5. API documentation
6. Contributing guidelines

Return ONLY the markdown content, no additional text.`;
        return await this.generateSingleLLMResponse(readmePrompt, {
            temperature: 0.3,
            maxTokens: 3000
        });
    }
    cleanJsonResponse(response) {
        // Remove markdown code blocks if present
        let cleaned = response.trim();
        // Remove ```json at the beginning
        if (cleaned.startsWith('```json')) {
            cleaned = cleaned.substring(7);
        }
        else if (cleaned.startsWith('```')) {
            cleaned = cleaned.substring(3);
        }
        // Remove ``` at the end
        if (cleaned.endsWith('```')) {
            cleaned = cleaned.substring(0, cleaned.length - 3);
        }
        return cleaned.trim();
    }
    async generateApplicationFlowWithAI(semanticAnalysis, spec) {
        const flowPrompt = `Generate application flow configuration for a ${semanticAnalysis.domain} application.

Project: ${spec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate a JSON configuration that includes:
1. Navigation structure
2. User workflows
3. Page routing
4. Component hierarchy

Return ONLY valid JSON, no additional text.`;
        const flowResponse = await this.generateSingleLLMResponse(flowPrompt, {
            temperature: 0.3,
            maxTokens: 2000
        });
        try {
            const cleanedResponse = this.cleanJsonResponse(flowResponse);
            return JSON.parse(cleanedResponse);
        }
        catch (error) {
            this.logError('Failed to parse application flow JSON', { error: error instanceof Error ? error.message : String(error) });
            return { navigation: [], workflows: [] };
        }
    }
    async generateSidebarComponentWithAI(semanticAnalysis, spec) {
        const sidebarPrompt = `Generate a Sidebar React component for a ${semanticAnalysis.domain} application.

Project: ${spec.projectName}
Domain: ${semanticAnalysis.domain}
Entities: ${(semanticAnalysis.entities || []).map(e => e.name || 'entity').join(', ')}

Generate a sidebar component that:
1. Provides navigation to all entity management pages
2. Includes dashboard link
3. Shows user profile section
4. Implements responsive design
5. Uses modern styling

Return ONLY the TypeScript React code, no additional text.`;
        return await this.generateSingleLLMResponse(sidebarPrompt, {
            temperature: 0.2,
            maxTokens: 2000
        });
    }
    async createProjectStructure(projectPath, spec) {
        // Create main project directory
        if (!fs.existsSync(projectPath)) {
            fs.mkdirSync(projectPath, { recursive: true });
        }
        // Create frontend structure
        const frontendPath = path.join(projectPath, 'frontend');
        const frontendDirs = Array.isArray(spec.fileStructure.frontend) ? spec.fileStructure.frontend : [];
        for (const dir of frontendDirs) {
            // Only create directories, not files (skip entries with file extensions)
            if (!dir.includes('.') || dir.endsWith('/')) {
                const dirPath = dir.endsWith('/') ? dir.slice(0, -1) : dir;
                fs.mkdirSync(path.join(frontendPath, dirPath), { recursive: true });
            }
            else {
                // For file paths, create the parent directory
                const parentDir = path.dirname(dir);
                if (parentDir && parentDir !== '.') {
                    fs.mkdirSync(path.join(frontendPath, parentDir), { recursive: true });
                }
            }
        }
        // Create backend structure
        const backendPath = path.join(projectPath, 'backend');
        const backendDirs = Array.isArray(spec.fileStructure.backend) ? spec.fileStructure.backend : [];
        for (const dir of backendDirs) {
            // Only create directories, not files (skip entries with file extensions)
            if (!dir.includes('.') || dir.endsWith('/')) {
                const dirPath = dir.endsWith('/') ? dir.slice(0, -1) : dir;
                fs.mkdirSync(path.join(backendPath, dirPath), { recursive: true });
            }
            else {
                // For file paths, create the parent directory
                const parentDir = path.dirname(dir);
                if (parentDir && parentDir !== '.') {
                    fs.mkdirSync(path.join(backendPath, parentDir), { recursive: true });
                }
            }
        }
        // Create root directories
        const rootDirs = Array.isArray(spec.fileStructure.root) ? spec.fileStructure.root : [];
        for (const dir of rootDirs) {
            if (!dir.includes('.')) { // Skip files, only create directories
                fs.mkdirSync(path.join(projectPath, dir), { recursive: true });
            }
        }
    }
    async generateAllCodeWithBusinessLogic(projectPath, spec, businessLogic, requirements = []) {
        const files = {};
        const packageJsons = {};
        const scripts = {};
        // Generate business logic if not provided
        if (!businessLogic) {
            businessLogic = await this.generateBusinessLogic(spec, requirements);
        }
        // Generate frontend code with business logic integration
        const frontendFiles = await this.generateEnhancedFrontendCode(spec, businessLogic);
        Object.assign(files, frontendFiles);
        // Generate backend code with domain APIs
        const backendFiles = await this.generateEnhancedBackendCode(spec, businessLogic);
        Object.assign(files, backendFiles);
        // Generate configuration files with database schema
        const configFiles = await this.generateEnhancedConfigFiles(spec, businessLogic);
        Object.assign(files, configFiles);
        // Generate package.json files
        packageJsons['frontend/package.json'] = this.generateFrontendPackageJson(spec);
        packageJsons['backend/package.json'] = this.generateBackendPackageJson(spec);
        // Generate scripts
        scripts['setup.sh'] = this.generateSetupScript(spec);
        scripts['start.sh'] = this.generateStartScript(spec);
        // Generate documentation
        const documentation = this.generateDocumentation(spec);
        // Include package.json files in the files object for complete output
        for (const [packagePath, content] of Object.entries(packageJsons)) {
            files[packagePath] = JSON.stringify(content, null, 2);
        }
        // Include scripts in the files object
        for (const [scriptName, content] of Object.entries(scripts)) {
            files[scriptName] = content;
        }
        // Include documentation in the files object
        files['README.md'] = documentation;
        // Generate docker-compose.yml if not already present
        if (!files['docker-compose.yml']) {
            files['docker-compose.yml'] = this.generateDockerCompose(spec);
        }
        return {
            projectPath,
            files,
            packageJsons,
            scripts,
            documentation,
        };
    }
    async generateBusinessLogic(spec, requirements) {
        // Extract semantic analysis from spec metadata
        const semanticAnalysis = spec.metadata?.semanticAnalysis || spec.businessDomain;
        if (!semanticAnalysis) {
            throw new Error('Semantic analysis required for business logic generation');
        }
        // Generate domain-specific APIs using AI
        const domainAPIGenerator = new (await Promise.resolve().then(() => __importStar(require('../generators/DomainAPIGenerator')))).DomainAPIGenerator();
        const domainAPIs = await domainAPIGenerator.generateAPIsFromRequirements(requirements, spec);
        // Generate database schema using AI
        const databaseSchemaGenerator = new (await Promise.resolve().then(() => __importStar(require('../generators/DatabaseSchemaGenerator')))).DatabaseSchemaGenerator();
        const databaseSchema = await databaseSchemaGenerator.generateSchemaFromRequirements(requirements, spec);
        const initScript = databaseSchemaGenerator.generateDatabaseInitScript(databaseSchema);
        // Generate application flow using AI
        const applicationFlow = await this.generateApplicationFlowWithAI(semanticAnalysis, spec);
        const sidebarComponent = await this.generateSidebarComponentWithAI(semanticAnalysis, spec);
        return {
            technicalSpec: spec,
            projectPath: path.join(process.cwd(), 'generated-projects', spec.projectName),
            domainAPIs,
            databaseSchema: {
                ...databaseSchema,
                initScript
            },
            applicationFlow: {
                ...applicationFlow,
                sidebarComponent
            },
            enhancedComponents: {},
            businessServices: this.extractBusinessServices(domainAPIs || {}),
            dataModels: this.extractDataModels(domainAPIs || {}),
            businessLogic: {
                domainAPIs,
                databaseSchema: {
                    ...databaseSchema,
                    initScript
                },
                applicationFlow: {
                    ...applicationFlow,
                    sidebarComponent
                }
            }
        };
    }
    async generateEnhancedFrontendCode(spec, businessLogic) {
        const files = {};
        // Generate base frontend structure
        const baseFiles = await this.generateFrontendCode(spec);
        Object.assign(files, baseFiles);
        // Override App.tsx with enhanced version
        files['frontend/src/App.tsx'] = businessLogic.applicationFlow.appComponent;
        // Override Dashboard.tsx with enhanced version
        files['frontend/src/pages/Dashboard.tsx'] = businessLogic.applicationFlow.dashboardComponent;
        // Add Sidebar component
        files['frontend/src/components/Sidebar.tsx'] = businessLogic.applicationFlow.sidebarComponent;
        // Add enhanced components (with safety check)
        if (businessLogic.enhancedComponents && typeof businessLogic.enhancedComponents === 'object') {
            Object.assign(files, businessLogic.enhancedComponents);
        }
        return files;
    }
    async generateEnhancedBackendCode(spec, businessLogic) {
        const files = {};
        // Generate base backend structure (routes, config, etc.)
        const baseFiles = await this.generateBackendCode(spec);
        Object.assign(files, baseFiles);
        // Add domain-specific APIs with enhanced service implementations
        if (businessLogic.domainAPIs && typeof businessLogic.domainAPIs === 'object') {
            // The domain APIs should already contain enhanced service implementations
            // from the DomainAPIGenerator.generateDynamicServiceFile method
            Object.assign(files, businessLogic.domainAPIs);
            this.logInfo('Enhanced backend code generation completed', {
                baseFiles: Object.keys(baseFiles).length,
                domainAPIFiles: Object.keys(businessLogic.domainAPIs).length,
                totalFiles: Object.keys(files).length
            });
        }
        else {
            this.logInfo('No domain APIs found in business logic, using base backend only', {
                baseFiles: Object.keys(baseFiles).length
            });
        }
        return files;
    }
    async generateEnhancedConfigFiles(spec, businessLogic) {
        const files = {};
        // Generate base config files
        const baseFiles = await this.generateConfigFiles(spec);
        Object.assign(files, baseFiles);
        // Override init.sql with enhanced database schema
        files['init.sql'] = businessLogic.databaseSchema.initScript;
        return files;
    }
    extractRouteFilesFromSpec(spec) {
        const routes = [];
        // Always include auth and users as they're always generated
        routes.push({ name: 'auth', path: '/api/auth' });
        routes.push({ name: 'users', path: '/api/users' });
        // Extract routes from backend APIs if available
        if (spec.architecture?.backend?.apis) {
            let apis = [];
            if (Array.isArray(spec.architecture.backend.apis)) {
                apis = spec.architecture.backend.apis;
            }
            else if (typeof spec.architecture.backend.apis === 'object' && spec.architecture.backend.apis !== null) {
                apis = Object.keys(spec.architecture.backend.apis);
            }
            else {
                // If it's a string or other type, convert to array
                apis = [String(spec.architecture.backend.apis)];
            }
            apis.forEach((api) => {
                // Clean and validate route name - be more conservative
                let routeName = api.toLowerCase()
                    .replace(/\s+/g, '')
                    .replace(/api/g, '')
                    .replace(/[^a-zA-Z0-9]/g, ''); // Remove special characters
                // Skip if the route name contains auth-related substrings that would create invalid names
                if (routeName.includes('auth') && routeName !== 'auth') {
                    return; // Skip malformed auth routes like 'authlogin', 'authregister'
                }
                // Ensure route name is valid and not too short/long
                if (routeName &&
                    routeName.length > 2 &&
                    routeName.length < 20 &&
                    routeName.match(/^[a-zA-Z][a-zA-Z0-9]*$/)) {
                    const routePath = `/api/${routeName}`;
                    // Only add if we have route generation capability for this type
                    if (this.canGenerateRouteForAPIName(routeName)) {
                        routes.push({ name: routeName, path: routePath });
                    }
                }
            });
        }
        // Extract routes from features that will actually be implemented
        if (spec.features && Array.isArray(spec.features)) {
            spec.features.forEach(feature => {
                if (typeof feature === 'string') {
                    const featureLower = feature.toLowerCase();
                    // Only add routes for features that have corresponding route file generation
                    if (featureLower.includes('todo') || featureLower.includes('task')) {
                        routes.push({ name: 'todos', path: '/api/todos' });
                    }
                    if (featureLower.includes('product') && this.shouldGenerateProductRoutes(spec)) {
                        routes.push({ name: 'products', path: '/api/products' });
                    }
                    if (featureLower.includes('order') && this.shouldGenerateOrderRoutes(spec)) {
                        routes.push({ name: 'orders', path: '/api/orders' });
                    }
                }
            });
        }
        // Remove duplicates
        return routes.filter((route, index, self) => index === self.findIndex(r => r.name === route.name));
    }
    canGenerateRouteForAPIName(routeName) {
        // Check if we have route generation capability for this route type
        const name = routeName.toLowerCase();
        // Currently supported route types
        const supportedTypes = ['todo', 'task', 'user', 'auth'];
        return supportedTypes.some(type => name.includes(type));
    }
    shouldGenerateProductRoutes(spec) {
        // Only return true if we actually generate product route files
        // For now, return false until we implement product route generation
        return false;
    }
    shouldGenerateOrderRoutes(spec) {
        // Only return true if we actually generate order route files
        // For now, return false until we implement order route generation
        return false;
    }
    /**
     * SecurityAgent-style defensive server file generation
     * Uses incremental enhancement instead of bulk generation
     */
    async generateOrUpdateServerFile(projectPath, spec, routeFiles) {
        const serverPath = path.join(projectPath, 'backend', 'src', 'server.ts');
        // Check if server.ts already exists
        if (this.fileExistsSafely(serverPath)) {
            this.logInfo('Server file exists, enhancing incrementally');
            return await this.enhanceExistingServerFile(serverPath, routeFiles);
        }
        else {
            this.logInfo('Creating new server file');
            return this.generateNewServerFile(spec, routeFiles);
        }
    }
    async enhanceExistingServerFile(serverPath, routeFiles) {
        const originalContent = this.readFileSafely(serverPath);
        if (!originalContent) {
            throw new Error(`Failed to read existing server file: ${serverPath}`);
        }
        let enhancedContent = originalContent;
        let changesApplied = 0;
        // Filter and validate routes using SecurityAgent patterns
        const validRoutes = this.validateAndFilterRoutes(routeFiles);
        for (const route of validRoutes) {
            const routeVarName = `${route.name}Routes`;
            const importStatement = `import ${routeVarName} from './routes/${route.name}';`;
            const registrationStatement = `app.use('${route.path}', ${routeVarName});`;
            // SecurityAgent-style existence checks
            if (!enhancedContent.includes(routeVarName)) {
                enhancedContent = this.addImportSafely(enhancedContent, importStatement);
                changesApplied++;
                this.logInfo(`Added import for ${routeVarName}`);
            }
            if (!enhancedContent.includes(`app.use('${route.path}', ${routeVarName})`)) {
                enhancedContent = this.addRegistrationSafely(enhancedContent, registrationStatement);
                changesApplied++;
                this.logInfo(`Added registration for ${routeVarName}`);
            }
        }
        // Only write if changes were made
        if (changesApplied > 0) {
            this.writeFileSafely(serverPath, enhancedContent);
            this.logInfo(`Enhanced server file with ${changesApplied} changes`);
        }
        return enhancedContent;
    }
    generateNewServerFile(spec, routeFiles) {
        // Generate base server structure without route-specific imports
        const baseServerContent = `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { connectDatabase } from './config/database';
import { errorHandler } from './middleware/errorHandler';
import { logger } from './utils/logger';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    await connectDatabase();
    app.listen(PORT, () => {
      logger.info(\`Server running on port \${PORT}\`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
};

startServer();

export default app;`;
        return baseServerContent;
    }
    validateAndFilterRoutes(routeFiles) {
        // SecurityAgent-style validation
        const validRoutes = routeFiles.filter(route => route.name &&
            route.name.length > 0 &&
            !route.name.includes('/') &&
            !route.name.includes('//') &&
            route.name.match(/^[a-zA-Z][a-zA-Z0-9]*$/));
        // Remove duplicates using SecurityAgent patterns
        const uniqueRoutes = validRoutes.filter((route, index, self) => index === self.findIndex(r => r.name === route.name));
        return uniqueRoutes;
    }
    /**
     * SecurityAgent-style defensive route file generation
     */
    async generateRouteFilesSafely(projectPath, routeFiles) {
        const files = {};
        const validRoutes = this.validateAndFilterRoutes(routeFiles);
        for (const route of validRoutes) {
            const routeFilePath = `backend/src/routes/${route.name}.ts`;
            const fullRoutePath = path.join(projectPath, routeFilePath);
            // SecurityAgent-style existence check
            if (!this.fileExistsSafely(fullRoutePath)) {
                files[routeFilePath] = this.generateRouteFileContent(route);
                this.logInfo(`Generated new route file: ${route.name}.ts`);
            }
            else {
                this.logInfo(`Route file already exists, skipping: ${route.name}.ts`);
            }
        }
        return files;
    }
    generateRouteFileContent(route) {
        const routeName = route.name.charAt(0).toUpperCase() + route.name.slice(1);
        return `import { Router, Request, Response } from 'express';
import { auth } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

// GET ${route.path}
router.get('/', auth, async (req: Request, res: Response) => {
  try {
    // TODO: Implement ${routeName} listing logic
    res.json({ message: '${routeName} list endpoint', data: [] });
  } catch (error) {
    logger.error('Error in ${routeName} GET:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST ${route.path}
router.post('/', auth, async (req: Request, res: Response) => {
  try {
    // TODO: Implement ${routeName} creation logic
    res.status(201).json({ message: '${routeName} created successfully', data: req.body });
  } catch (error) {
    logger.error('Error in ${routeName} POST:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET ${route.path}/:id
router.get('/:id', auth, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    // TODO: Implement ${routeName} retrieval logic
    res.json({ message: \`${routeName} \${id} retrieved\`, data: { id } });
  } catch (error) {
    logger.error('Error in ${routeName} GET by ID:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT ${route.path}/:id
router.put('/:id', auth, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    // TODO: Implement ${routeName} update logic
    res.json({ message: \`${routeName} \${id} updated\`, data: { id, ...req.body } });
  } catch (error) {
    logger.error('Error in ${routeName} PUT:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE ${route.path}/:id
router.delete('/:id', auth, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    // TODO: Implement ${routeName} deletion logic
    res.json({ message: \`${routeName} \${id} deleted\` });
  } catch (error) {
    logger.error('Error in ${routeName} DELETE:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;`;
    }
    /**
     * SecurityAgent-style defensive middleware generation
     */
    async generateEssentialMiddlewareSafely(projectPath) {
        const files = {};
        // Generate auth middleware if it doesn't exist
        const authMiddlewarePath = path.join(projectPath, 'backend', 'src', 'middleware', 'auth.ts');
        if (!this.fileExistsSafely(authMiddlewarePath)) {
            files['backend/src/middleware/auth.ts'] = this.generateAuthMiddleware();
            this.logInfo('Generated auth middleware');
        }
        // Generate error handler if it doesn't exist
        const errorHandlerPath = path.join(projectPath, 'backend', 'src', 'middleware', 'errorHandler.ts');
        if (!this.fileExistsSafely(errorHandlerPath)) {
            files['backend/src/middleware/errorHandler.ts'] = this.generateErrorHandler();
            this.logInfo('Generated error handler middleware');
        }
        // Generate logger utility if it doesn't exist
        const loggerPath = path.join(projectPath, 'backend', 'src', 'utils', 'logger.ts');
        if (!this.fileExistsSafely(loggerPath)) {
            files['backend/src/utils/logger.ts'] = this.generateLogger();
            this.logInfo('Generated logger utility');
        }
        return files;
    }
    generateAuthMiddleware() {
        return `import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

export interface AuthRequest extends Request {
  user?: any;
}

export const auth = (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ error: 'Invalid token.' });
  }
};`;
    }
    generateErrorHandler() {
        return `import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
  });

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
  });
};`;
    }
    generateLogger() {
        return `import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'backend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}`;
    }
    generateDatabaseConfigWithSchema(spec, routeFiles) {
        const tableDefinitions = this.generateTableDefinitionsFromRoutes(routeFiles);
        return `import { Pool } from 'pg';
import { logger } from '../utils/logger';

// CRITICAL: Export pool as const for proper TypeScript module resolution
export const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '54329'),
  database: process.env.DB_NAME || 'gen_demo',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

export const connectDatabase = async (): Promise<void> => {
  try {
    const client = await pool.connect();
    logger.info('Database connected successfully');

    // Create tables if they don't exist
    await createTables(client);
    client.release();
    logger.info('Database tables created/verified');
  } catch (error) {
    logger.error('Database connection failed:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};

const createTables = async (client: any): Promise<void> => {
  try {
    // Create users table
    const createUsersTable = \`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createUsersTable);

    ${tableDefinitions}
  } catch (error) {
    logger.error('Failed to create tables:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};`;
    }
    generateTableDefinitionsFromRoutes(routeFiles) {
        const tableCreations = [];
        routeFiles.forEach(route => {
            switch (route.name) {
                case 'customers':
                    tableCreations.push(`
    // Create customers table
    const createCustomersTable = \`
      CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createCustomersTable);`);
                    break;
                case 'products':
                    tableCreations.push(`
    // Create products table
    const createProductsTable = \`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        category VARCHAR(100),
        stock_quantity INTEGER DEFAULT 0,
        barcode VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createProductsTable);`);
                    break;
                case 'inventory':
                    tableCreations.push(`
    // Create inventory table
    const createInventoryTable = \`
      CREATE TABLE IF NOT EXISTS inventory (
        id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER DEFAULT 0,
        reorder_point INTEGER DEFAULT 10,
        location VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createInventoryTable);`);
                    break;
                case 'payments':
                    tableCreations.push(`
    // Create payments table
    const createPaymentsTable = \`
      CREATE TABLE IF NOT EXISTS payments (
        id SERIAL PRIMARY KEY,
        order_id INTEGER,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        transaction_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createPaymentsTable);`);
                    break;
                case 'suppliers':
                    tableCreations.push(`
    // Create suppliers table
    const createSuppliersTable = \`
      CREATE TABLE IF NOT EXISTS suppliers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createSuppliersTable);`);
                    break;
                case 'orders':
                    tableCreations.push(`
    // Create orders table
    const createOrdersTable = \`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES customers(id),
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createOrdersTable);

    // Create order_items table
    const createOrderItemsTable = \`
      CREATE TABLE IF NOT EXISTS order_items (
        id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
        product_id INTEGER,
        quantity INTEGER NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    \`;
    await client.query(createOrderItemsTable);`);
                    break;
            }
        });
        return tableCreations.join('\n');
    }
    extractComponentNames(spec) {
        const components = [];
        if (spec.architecture?.frontend?.components) {
            components.push(...spec.architecture.frontend.components);
        }
        // Always include essential ecommerce components
        const essentialComponents = [
            'ProductCatalog', 'ShoppingCart', 'CustomerManager', 'InventoryManager',
            'PaymentProcessor', 'OrderManager', 'AnalyticsDashboard', 'SalesReports'
        ];
        components.push(...essentialComponents);
        if (spec.features && Array.isArray(spec.features)) {
            spec.features.forEach(feature => {
                const featureLower = feature.toLowerCase();
                if (featureLower.includes('barcode') || featureLower.includes('scanner')) {
                    components.push('ProductScanner', 'BarcodeScanner');
                }
                if (featureLower.includes('pos') || featureLower.includes('terminal')) {
                    components.push('POSTerminal');
                }
                if (featureLower.includes('inventory')) {
                    components.push('InventoryManager', 'StockAlerts');
                }
                if (featureLower.includes('customer')) {
                    components.push('CustomerManager');
                }
                if (featureLower.includes('payment')) {
                    components.push('PaymentProcessor');
                }
                if (featureLower.includes('warranty')) {
                    components.push('WarrantyTracker');
                }
                if (featureLower.includes('report')) {
                    components.push('SalesReports', 'AnalyticsDashboard');
                }
            });
        }
        return [...new Set(components)];
    }
    extractBusinessServices(domainAPIs) {
        const services = {};
        if (!domainAPIs || typeof domainAPIs !== 'object') {
            return services;
        }
        Object.entries(domainAPIs).forEach(([filePath, content]) => {
            if (filePath && filePath.includes('services/')) {
                services[filePath] = content;
            }
        });
        return services;
    }
    extractDataModels(domainAPIs) {
        const models = {};
        if (!domainAPIs || typeof domainAPIs !== 'object') {
            return models;
        }
        Object.entries(domainAPIs).forEach(([filePath, content]) => {
            if (filePath && filePath.includes('models/')) {
                models[filePath] = content;
            }
        });
        return models;
    }
    async generateFrontendCode(spec) {
        const files = {};
        // CRITICAL: Generate only components that will be properly imported
        // Use enhanced prompt for domain-specific components
        const domainComponents = this.generateDomainComponentsWithValidation(spec);
        Object.assign(files, domainComponents);
        // Generate main App component with enhanced error handling and validation
        files['frontend/src/App.tsx'] = `import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Header from './components/Header';
import Home from './pages/Home';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import PrivateRoute from './components/PrivateRoute';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSpinner from './components/LoadingSpinner';
import './App.css';

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
          <div className="App">
            <Header />
            <main className="main-content" role="main">
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/login" element={<Login />} />
                  <Route
                    path="/dashboard"
                    element={
                      <PrivateRoute>
                        <Dashboard />
                      </PrivateRoute>
                    }
                  />
                </Routes>
              </Suspense>
            </main>
          </div>
        </Router>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;`;
        // Generate Auth Context
        files['frontend/src/contexts/AuthContext.tsx'] = `import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '../services/api';

interface User {
  id: string;
  email: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      authAPI.verifyToken(token)
        .then(userData => setUser(userData))
        .catch(() => localStorage.removeItem('token'))
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    const response = await authAPI.login(email, password);
    localStorage.setItem('token', response.token);
    setUser(response.user);
  };

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
  };

  const value = {
    user,
    login,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};`;
        // Generate API service
        files['frontend/src/services/api.ts'] = `import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = \`Bearer \${token}\`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },
  
  register: async (name: string, email: string, password: string) => {
    const response = await api.post('/auth/register', { name, email, password });
    return response.data;
  },
  
  verifyToken: async (token: string) => {
    const response = await api.get('/auth/verify', {
      headers: { Authorization: \`Bearer \${token}\` }
    });
    return response.data;
  },
};

export default api;`;
        // Generate components
        files['frontend/src/components/Header.tsx'] = `import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Header: React.FC = () => {
  const { user, logout } = useAuth();

  return (
    <header className="header">
      <div className="container">
        <Link to="/" className="logo">
          ${spec.projectName}
        </Link>
        <nav className="nav">
          {user ? (
            <>
              <Link to="/dashboard">Dashboard</Link>
              <span>Welcome, {user.name}</span>
              <button onClick={logout} className="btn btn-secondary">
                Logout
              </button>
            </>
          ) : (
            <Link to="/login" className="btn btn-primary">
              Login
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Header;`;
        files['frontend/src/components/PrivateRoute.tsx'] = `import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface PrivateRouteProps {
  children: ReactNode;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  return user ? <>{children}</> : <Navigate to="/login" />;
};

export default PrivateRoute;`;
        // Generate ErrorBoundary component for enhanced error handling
        files['frontend/src/components/ErrorBoundary.tsx'] = `import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary" role="alert">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="retry-button"
          >
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;`;
        // Generate LoadingSpinner component
        files['frontend/src/components/LoadingSpinner.tsx'] = `import React from 'react';

const LoadingSpinner: React.FC = () => {
  return (
    <div className="loading-spinner" role="status" aria-label="Loading">
      <div className="spinner"></div>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default LoadingSpinner;`;
        // Generate pages
        files['frontend/src/pages/Home.tsx'] = `import React from 'react';

const Home: React.FC = () => {
  return (
    <div className="page">
      <div className="container">
        <h1>Welcome to ${spec.projectName}</h1>
        <p>${spec.description}</p>
        <div className="features">
          <h2>Features</h2>
          <ul>
            ${spec.features.map(feature => {
            // Handle both string and object features
            if (typeof feature === 'string') {
                return `<li>${feature}</li>`;
            }
            else if (feature && typeof feature === 'object') {
                const featureObj = feature;
                if (featureObj.name) {
                    return `<li>${featureObj.name}</li>`;
                }
                else if (featureObj.title) {
                    return `<li>${featureObj.title}</li>`;
                }
                else {
                    return `<li>${String(feature)}</li>`;
                }
            }
            else {
                return `<li>${String(feature)}</li>`;
            }
        }).join('\n            ')}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Home;`;
        files['frontend/src/pages/Login.tsx'] = `import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="page">
      <div className="container">
        <div className="auth-form">
          <h1>Login</h1>
          {error && <div className="error">{error}</div>}
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <button type="submit" disabled={loading} className="btn btn-primary">
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;`;
        files['frontend/src/pages/Dashboard.tsx'] = `import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="page">
      <div className="container">
        <h1>Dashboard</h1>
        <p>Welcome back, {user?.name}!</p>
        <div className="dashboard-content">
          <div className="card">
            <h3>Your Profile</h3>
            <p>Email: {user?.email}</p>
            <p>Name: {user?.name}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;`;
        // Generate styles
        files['frontend/src/App.css'] = `* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1rem 0;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
  color: #007bff;
}

.nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.page {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.auth-form {
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.loading {
  text-align: center;
  padding: 2rem;
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 1rem;
}

.features ul {
  list-style-type: disc;
  margin-left: 2rem;
}

.features li {
  margin-bottom: 0.5rem;
}`;
        // Generate index files
        files['frontend/src/index.tsx'] = `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;
        files['frontend/public/index.html'] = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="${spec.description}" />
    <title>${spec.projectName}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`;
        return files;
    }
    async generateBackendCode(spec) {
        const files = {};
        // Generate main server file using defensive patterns
        const routeFiles = this.extractRouteFilesFromSpec(spec);
        files['backend/src/server.ts'] = await this.generateOrUpdateServerFile('/tmp/' + spec.projectName, spec, routeFiles);
        // Generate route files defensively
        const routeFileContents = await this.generateRouteFilesSafely('/tmp/' + spec.projectName, routeFiles);
        Object.assign(files, routeFileContents);
        // Generate essential middleware defensively
        const middlewareContents = await this.generateEssentialMiddlewareSafely('/tmp/' + spec.projectName);
        Object.assign(files, middlewareContents);
        // Generate database configuration with schema consistency
        files['backend/src/config/database.ts'] = this.generateDatabaseConfigWithSchema(spec, routeFiles);
        // Generate auth routes with enhanced security
        files['backend/src/routes/auth.ts'] = `import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { pool } from '../config/database';
import { validateEmail, validatePassword } from '../utils/validation';
import { logger } from '../utils/logger';

const router = express.Router();

// CRITICAL SECURITY: JWT_SECRET must be provided via environment variable
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required for security');
}

// Register
router.post('/register', async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validation
    if (!name || !email || !password) {
      return res.status(400).json({ message: 'All fields are required' });
    }

    if (!validateEmail(email)) {
      return res.status(400).json({ message: 'Invalid email format' });
    }

    if (!validatePassword(password)) {
      return res.status(400).json({ 
        message: 'Password must be at least 8 characters long' 
      });
    }

    // Check if user exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const result = await pool.query(
      'INSERT INTO users (name, email, password) VALUES ($1, $2, $3) RETURNING id, name, email',
      [name, email, hashedPassword]
    );

    const user = result.rows[0];

    // Generate JWT
    const token = jwt.sign({ userId: user.id }, JWT_SECRET, { expiresIn: '7d' });

    logger.info(\`User registered: \${email}\`);

    res.status(201).json({
      message: 'User created successfully',
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Find user
    const result = await pool.query(
      'SELECT id, name, email, password FROM users WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const user = result.rows[0];

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate JWT
    const token = jwt.sign({ userId: user.id }, JWT_SECRET, { expiresIn: '7d' });

    logger.info(\`User logged in: \${email}\`);

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Verify token
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as { userId: number };
    
    const result = await pool.query(
      'SELECT id, name, email FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ message: 'Invalid token' });
    }

    const user = result.rows[0];

    res.json({
      id: user.id,
      name: user.name,
      email: user.email,
    });
  } catch (error) {
    logger.error('Token verification error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
});

export default router;`;
        // Generate user routes
        files['backend/src/routes/users.ts'] = `import express from 'express';
import { pool } from '../config/database';
import { authenticateToken } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;

    const result = await pool.query(
      'SELECT id, name, email, created_at FROM users WHERE id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update user profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Name is required' });
    }

    const result = await pool.query(
      'UPDATE users SET name = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING id, name, email',
      [name, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    logger.info(\`User profile updated: \${userId}\`);

    res.json({
      message: 'Profile updated successfully',
      user: result.rows[0],
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;`;
        // Generate todo routes if needed
        const todoRouteFiles = this.extractRouteFilesFromSpec(spec);
        if (todoRouteFiles.some(route => route.name === 'todos')) {
            files['backend/src/routes/todos.ts'] = this.generateTodoRoutes();
        }
        // Generate middleware
        files['backend/src/middleware/auth.ts'] = `import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: number };
    (req as any).userId = decoded.userId;
    next();
  } catch (error) {
    logger.error('Token authentication error:', error);
    res.status(403).json({ message: 'Invalid or expired token' });
  }
};`;
        files['backend/src/middleware/errorHandler.ts'] = `import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
  });

  res.status(500).json({
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { error: error.message }),
  });
};`;
        // Generate utilities
        files['backend/src/utils/validation.ts'] = `export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): boolean => {
  return password.length >= 8;
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};`;
        files['backend/src/utils/logger.ts'] = `interface LogLevel {
  INFO: 'info';
  ERROR: 'error';
  WARN: 'warn';
  DEBUG: 'debug';
}

const LOG_LEVELS: LogLevel = {
  INFO: 'info',
  ERROR: 'error',
  WARN: 'warn',
  DEBUG: 'debug',
};

class Logger {
  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...(data && { data }),
    };

    console.log(JSON.stringify(logEntry));
  }

  info(message: string, data?: any): void {
    this.log(LOG_LEVELS.INFO, message, data);
  }

  error(message: string, data?: any): void {
    this.log(LOG_LEVELS.ERROR, message, data);
  }

  warn(message: string, data?: any): void {
    this.log(LOG_LEVELS.WARN, message, data);
  }

  debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      this.log(LOG_LEVELS.DEBUG, message, data);
    }
  }
}

export const logger = new Logger();`;
        return files;
    }
    async generateConfigFiles(spec) {
        const files = {};
        // Generate environment files
        files['.env.example'] = `# Database
DB_HOST=localhost
DB_PORT=54329
DB_NAME=gen_demo
DB_USER=postgres
DB_PASSWORD=password

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server
PORT=5000
NODE_ENV=development

# Frontend
FRONTEND_URL=http://localhost:3000`;
        files['frontend/.env.example'] = `REACT_APP_API_URL=http://localhost:5000/api`;
        // Generate TypeScript configs
        files['frontend/tsconfig.json'] = `{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "es6"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": [
    "src"
  ]
}`;
        files['backend/tsconfig.json'] = `{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "lib": ["es2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}`;
        return files;
    }
    generateFrontendPackageJson(spec) {
        return {
            name: `${spec.projectName}-frontend`,
            version: '0.1.0',
            private: true,
            dependencies: {
                react: '^18.2.0',
                'react-dom': '^18.2.0',
                'react-router-dom': '^6.8.0',
                axios: '^1.3.0',
                '@types/react': '^18.0.0',
                '@types/react-dom': '^18.0.0',
                typescript: '^4.9.0',
            },
            scripts: {
                start: 'react-scripts start',
                build: 'react-scripts build',
                test: 'react-scripts test',
                eject: 'react-scripts eject',
            },
            eslintConfig: {
                extends: ['react-app', 'react-app/jest'],
            },
            browserslist: {
                production: ['>0.2%', 'not dead', 'not op_mini all'],
                development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version'],
            },
            devDependencies: {
                'react-scripts': '^5.0.1',
                '@testing-library/jest-dom': '^5.16.0',
                '@testing-library/react': '^13.4.0',
                '@testing-library/user-event': '^13.5.0',
            },
        };
    }
    generateBackendPackageJson(spec) {
        return {
            name: `${spec.projectName}-backend`,
            version: '1.0.0',
            description: `Backend for ${spec.projectName}`,
            main: 'dist/server.js',
            scripts: {
                start: 'node dist/server.js',
                dev: 'nodemon src/server.ts',
                build: 'tsc',
                test: 'jest',
                'test:watch': 'jest --watch',
            },
            dependencies: {
                express: '^4.18.0',
                cors: '^2.8.5',
                helmet: '^6.0.0',
                dotenv: '^16.0.0',
                jsonwebtoken: '^9.0.0',
                bcryptjs: '^2.4.3',
                pg: '^8.8.0',
                '@types/express': '^4.17.0',
                '@types/cors': '^2.8.0',
                '@types/jsonwebtoken': '^9.0.0',
                '@types/bcryptjs': '^2.4.0',
                '@types/pg': '^8.6.0',
            },
            devDependencies: {
                typescript: '^4.9.0',
                'ts-node': '^10.9.0',
                nodemon: '^2.0.0',
                '@types/node': '^18.0.0',
                jest: '^29.0.0',
                '@types/jest': '^29.0.0',
                'ts-jest': '^29.0.0',
                supertest: '^6.3.0',
                '@types/supertest': '^2.0.0',
            },
            jest: {
                preset: 'ts-jest',
                testEnvironment: 'node',
                roots: ['<rootDir>/src'],
                testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
            },
        };
    }
    generateSetupScript(spec) {
        return `#!/bin/bash

echo "Setting up ${spec.projectName}..."

# Copy environment files
cp .env.example .env
cp frontend/.env.example frontend/.env

# Install backend dependencies
echo "Installing backend dependencies..."
cd backend
npm install
npm run build
cd ..

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Setup database (requires PostgreSQL to be running)
echo "Setting up database..."
createdb ${spec.projectName.replace(/-/g, '_')}_db 2>/dev/null || echo "Database may already exist"

echo "Setup complete!"
echo "Run 'npm run start' to start the application"
`;
    }
    generateStartScript(spec) {
        return `#!/bin/bash

echo "Starting ${spec.projectName}..."

# Start backend in background
cd backend
npm run dev &
BACKEND_PID=$!

# Start frontend
cd ../frontend
npm start &
FRONTEND_PID=$!

echo "Backend PID: $BACKEND_PID"
echo "Frontend PID: $FRONTEND_PID"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
`;
    }
    generateDocumentation(spec) {
        return `# ${spec.projectName}

${spec.description}

## Features

${spec.features.map(feature => {
            // Handle both string and object features
            if (typeof feature === 'string') {
                return `- ${feature}`;
            }
            else if (feature && typeof feature === 'object') {
                const featureObj = feature;
                if (featureObj.name) {
                    return `- ${featureObj.name}`;
                }
                else if (featureObj.title) {
                    return `- ${featureObj.title}`;
                }
                else {
                    return `- ${String(feature)}`;
                }
            }
            else {
                return `- ${String(feature)}`;
            }
        }).join('\n')}

## Architecture

- **Frontend**: ${spec.architecture.frontend.framework}
- **Backend**: ${spec.architecture.backend.framework}
- **Database**: ${spec.architecture.backend.database}
- **Authentication**: ${spec.architecture.backend.authentication}

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Run the setup script:
   \`\`\`bash
   chmod +x setup.sh
   ./setup.sh
   \`\`\`

3. Start the application:
   \`\`\`bash
   chmod +x start.sh
   ./start.sh
   \`\`\`

### Manual Setup

1. Install dependencies:
   \`\`\`bash
   # Backend
   cd backend
   npm install
   
   # Frontend
   cd ../frontend
   npm install
   \`\`\`

2. Set up environment variables:
   \`\`\`bash
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   \`\`\`

3. Create database:
   \`\`\`bash
   createdb ${spec.projectName.replace(/-/g, '_')}_db
   \`\`\`

4. Start the services:
   \`\`\`bash
   # Backend (in one terminal)
   cd backend
   npm run dev
   
   # Frontend (in another terminal)
   cd frontend
   npm start
   \`\`\`

## API Endpoints

### Authentication
- \`POST /api/auth/register\` - Register a new user
- \`POST /api/auth/login\` - Login user
- \`GET /api/auth/verify\` - Verify JWT token

### Users
- \`GET /api/users/profile\` - Get user profile (authenticated)
- \`PUT /api/users/profile\` - Update user profile (authenticated)

## Testing

Run tests for backend:
\`\`\`bash
cd backend
npm test
\`\`\`

Run tests for frontend:
\`\`\`bash
cd frontend
npm test
\`\`\`

## Deployment

See deployment documentation in the deployment section.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the ISC License.
`;
    }
    async writeFilesToDisk(generatedCode) {
        // Clear previous import/export registrations
        this.importExportManager.clear();
        // First pass: validate TypeScript syntax and fix issues
        for (const [filePath, content] of Object.entries(generatedCode.files)) {
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                const validatedContent = this.validateAndFixTypeScriptSyntax(filePath, content);
                generatedCode.files[filePath] = validatedContent;
            }
        }
        // Second pass: scan and register all imports/exports
        for (const [filePath, content] of Object.entries(generatedCode.files)) {
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                this.scanAndRegisterImportsExports(filePath, content);
            }
        }
        // Validate import/export consistency
        const validationResult = this.importExportManager.validateConsistency(generatedCode.projectPath);
        if (!validationResult.success) {
            this.logInfo('Import/export consistency issues detected', {
                issues: validationResult.issues,
                duplicateImports: validationResult.duplicateImports.length
            });
        }
        // Write all code files with import/export fixes
        for (const [filePath, content] of Object.entries(generatedCode.files)) {
            const fullPath = path.join(generatedCode.projectPath, filePath);
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            // Fix duplicate imports and case consistency for TypeScript files
            let finalContent = content;
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                finalContent = this.importExportManager.fixDuplicateImports(filePath, content);
                finalContent = this.fixImportCaseConsistency(finalContent, generatedCode.projectPath);
            }
            fs.writeFileSync(fullPath, finalContent, 'utf8');
        }
        // Write package.json files
        for (const [packagePath, content] of Object.entries(generatedCode.packageJsons)) {
            const fullPath = path.join(generatedCode.projectPath, packagePath);
            fs.writeFileSync(fullPath, JSON.stringify(content, null, 2), 'utf8');
        }
        // Write scripts
        for (const [scriptName, content] of Object.entries(generatedCode.scripts)) {
            const fullPath = path.join(generatedCode.projectPath, scriptName);
            fs.writeFileSync(fullPath, content, 'utf8');
            fs.chmodSync(fullPath, '755'); // Make executable
        }
        // Write documentation
        const readmePath = path.join(generatedCode.projectPath, 'README.md');
        fs.writeFileSync(readmePath, generatedCode.documentation, 'utf8');
    }
    /**
     * Validate TypeScript syntax and fix common issues
     */
    validateAndFixTypeScriptSyntax(filePath, content) {
        try {
            // Create a TypeScript source file for syntax checking
            const sourceFile = typescript.createSourceFile(filePath, content, typescript.ScriptTarget.ES2020, true, filePath.endsWith('.tsx') ? typescript.ScriptKind.TSX : typescript.ScriptKind.TS);
            // Check for basic syntax errors by examining the source file
            const syntaxErrors = this.checkBasicSyntaxErrors(sourceFile, content);
            if (syntaxErrors.length > 0) {
                this.logWarn(`TypeScript syntax issues found in ${filePath}:`, {
                    errors: syntaxErrors
                });
                // Apply common fixes
                let fixedContent = content;
                // Fix 1: Missing closing braces
                fixedContent = this.fixMissingClosingBraces(fixedContent);
                // Fix 2: Incomplete function declarations
                fixedContent = this.fixIncompleteFunctions(fixedContent);
                // Fix 3: Missing semicolons
                fixedContent = this.fixMissingSemicolons(fixedContent);
                // Fix 4: Unclosed string literals
                fixedContent = this.fixUnclosedStrings(fixedContent);
                // Fix 5: Missing imports
                fixedContent = this.fixMissingImports(fixedContent, filePath);
                // Fix 6: Repair malformed imports from previous destructive fixes
                fixedContent = this.repairMalformedImports(fixedContent);
                // Fix 7: Remove destructive artifacts
                fixedContent = this.removeDestructiveArtifacts(fixedContent);
                return fixedContent;
            }
            return content;
        }
        catch (error) {
            this.logError(`Failed to validate TypeScript syntax for ${filePath}:`, { error: String(error) });
            return content;
        }
    }
    /**
     * Check for basic syntax errors without creating a full program
     */
    checkBasicSyntaxErrors(sourceFile, content) {
        const errors = [];
        // Check for parse errors using TypeScript's getParseTreeNode
        try {
            // Simple syntax validation by checking if the source file has any syntax errors
            const syntaxErrors = sourceFile.parseDiagnostics;
            if (syntaxErrors && Array.isArray(syntaxErrors) && syntaxErrors.length > 0) {
                syntaxErrors.forEach((diagnostic) => {
                    if (diagnostic.messageText) {
                        const message = typeof diagnostic.messageText === 'string'
                            ? diagnostic.messageText
                            : diagnostic.messageText.messageText;
                        errors.push(message);
                    }
                });
            }
        }
        catch (parseError) {
            // If we can't access parse diagnostics, fall back to basic checks
            this.logger.debug('Could not access parse diagnostics, using basic validation');
        }
        // Check for common syntax issues
        const lines = content.split('\n');
        // Check for unmatched braces
        let braceCount = 0;
        let inString = false;
        let stringChar = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                const prevChar = j > 0 ? line[j - 1] : '';
                if (!inString && (char === '"' || char === "'" || char === '`')) {
                    inString = true;
                    stringChar = char;
                }
                else if (inString && char === stringChar && prevChar !== '\\') {
                    inString = false;
                    stringChar = '';
                }
                else if (!inString) {
                    if (char === '{')
                        braceCount++;
                    else if (char === '}')
                        braceCount--;
                }
            }
        }
        if (braceCount !== 0) {
            errors.push(`Unmatched braces: ${braceCount > 0 ? 'missing closing' : 'extra closing'} braces`);
        }
        return errors;
    }
    /**
     * Fix missing closing braces in code
     */
    fixMissingClosingBraces(content) {
        const lines = content.split('\n');
        let braceCount = 0;
        let inString = false;
        let stringChar = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                const prevChar = j > 0 ? line[j - 1] : '';
                // Handle string literals
                if ((char === '"' || char === "'" || char === '`') && prevChar !== '\\') {
                    if (!inString) {
                        inString = true;
                        stringChar = char;
                    }
                    else if (char === stringChar) {
                        inString = false;
                        stringChar = '';
                    }
                }
                // Count braces outside of strings
                if (!inString) {
                    if (char === '{') {
                        braceCount++;
                    }
                    else if (char === '}') {
                        braceCount--;
                    }
                }
            }
        }
        // If we have unclosed braces, add closing braces
        if (braceCount > 0) {
            const closingBraces = '}'.repeat(braceCount);
            content += '\n' + closingBraces;
            this.logInfo(`Fixed ${braceCount} missing closing braces`);
        }
        return content;
    }
    /**
     * Fix incomplete function declarations
     */
    fixIncompleteFunctions(content) {
        // Fix functions that are missing opening braces
        const functionPattern = /export\s+const\s+\w+\s*=\s*\([^)]*\)\s*=>\s*$/gm;
        content = content.replace(functionPattern, (match) => {
            return match + ' {\n  // TODO: Implement function body\n}';
        });
        // Fix arrow functions without bodies
        const arrowFunctionPattern = /=>\s*$/gm;
        content = content.replace(arrowFunctionPattern, '=> {\n  // TODO: Implement function body\n}');
        return content;
    }
    /**
     * Fix missing semicolons
     */
    fixMissingSemicolons(content) {
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            // Add semicolons to statements that need them
            if (line &&
                !line.endsWith(';') &&
                !line.endsWith('{') &&
                !line.endsWith('}') &&
                !line.endsWith(',') &&
                !line.startsWith('//') &&
                !line.startsWith('/*') &&
                !line.includes('//') &&
                (line.includes('return ') ||
                    line.includes('const ') ||
                    line.includes('let ') ||
                    line.includes('var ') ||
                    line.match(/^\s*\w+\s*\(/))) {
                lines[i] = lines[i] + ';';
            }
        }
        return lines.join('\n');
    }
    /**
     * Fix unclosed string literals
     */
    fixUnclosedStrings(content) {
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let inString = false;
            let stringChar = '';
            let lastStringStart = -1;
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                const prevChar = j > 0 ? line[j - 1] : '';
                if ((char === '"' || char === "'" || char === '`') && prevChar !== '\\') {
                    if (!inString) {
                        inString = true;
                        stringChar = char;
                        lastStringStart = j;
                    }
                    else if (char === stringChar) {
                        inString = false;
                        stringChar = '';
                    }
                }
            }
            // If string is still open at end of line, close it
            if (inString && lastStringStart >= 0) {
                lines[i] = line + stringChar;
                this.logInfo(`Fixed unclosed string literal on line ${i + 1}`);
            }
        }
        return lines.join('\n');
    }
    /**
     * Fix missing imports based on file content and path
     */
    fixMissingImports(content, filePath) {
        const lines = content.split('\n');
        const imports = [];
        // Check if React import is needed
        if ((filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) &&
            content.includes('React.') &&
            !content.includes("import React from 'react'")) {
            imports.push("import React from 'react';");
        }
        // Check for common missing imports
        if (content.includes('useState') && !content.includes('useState')) {
            if (!content.includes("import React, { useState }")) {
                imports.push("import React, { useState } from 'react';");
            }
        }
        if (content.includes('useEffect') && !content.includes('useEffect')) {
            if (!content.includes("import React, { useEffect }")) {
                imports.push("import React, { useEffect } from 'react';");
            }
        }
        // Add missing imports at the top
        if (imports.length > 0) {
            const firstImportIndex = lines.findIndex(line => line.trim().startsWith('import'));
            if (firstImportIndex >= 0) {
                lines.splice(firstImportIndex, 0, ...imports);
            }
            else {
                lines.unshift(...imports, '');
            }
        }
        return lines.join('\n');
    }
    /**
     * Fix import case consistency issues
     */
    fixImportCaseConsistency(content, projectPath) {
        // Fix common case sensitivity issues in imports
        const importPattern = /import\s+(?:\{[^}]+\}|\w+)\s+from\s+['"]([^'"]+)['"];?/g;
        return content.replace(importPattern, (match, importPath) => {
            // Fix service imports - ensure PascalCase for service files
            if (importPath.includes('/services/')) {
                const serviceNameMatch = importPath.match(/\/services\/(\w+)(?:\.js)?$/);
                if (serviceNameMatch) {
                    const serviceName = serviceNameMatch[1];
                    const pascalCaseServiceName = serviceName.charAt(0).toUpperCase() + serviceName.slice(1);
                    // Ensure the service name is PascalCase
                    if (serviceName !== pascalCaseServiceName) {
                        const correctedPath = importPath.replace(`/services/${serviceName}`, `/services/${pascalCaseServiceName}`);
                        return match.replace(importPath, correctedPath);
                    }
                }
            }
            // Fix component imports - ensure PascalCase for component files
            if (importPath.includes('/components/')) {
                const componentNameMatch = importPath.match(/\/components\/(\w+)(?:\.tsx?)?$/);
                if (componentNameMatch) {
                    const componentName = componentNameMatch[1];
                    const pascalCaseComponentName = componentName.charAt(0).toUpperCase() + componentName.slice(1);
                    if (componentName !== pascalCaseComponentName) {
                        const correctedPath = importPath.replace(`/components/${componentName}`, `/components/${pascalCaseComponentName}`);
                        return match.replace(importPath, correctedPath);
                    }
                }
            }
            // Ensure .js extension is present for relative imports
            if (importPath.startsWith('./') || importPath.startsWith('../')) {
                if (!importPath.endsWith('.js') && !importPath.endsWith('.jsx') &&
                    !importPath.endsWith('.ts') && !importPath.endsWith('.tsx')) {
                    const correctedPath = importPath + '.js';
                    return match.replace(importPath, correctedPath);
                }
            }
            return match;
        });
    }
    /**
     * Scan file content and register imports/exports with the ImportExportManager
     */
    scanAndRegisterImportsExports(filePath, content) {
        try {
            // Register imports
            const importRegex = /^import\s+(?:(\w+)(?:\s*,\s*)?)?(?:\{\s*([^}]+)\s*\})?(?:\s*,\s*(\w+))?\s+from\s+['"]([^'"]+)['"];?$/gm;
            let match;
            while ((match = importRegex.exec(content)) !== null) {
                const [, defaultImport, namedImports, , fromPath] = match;
                // Register default import
                if (defaultImport) {
                    this.importExportManager.registerImport(filePath, defaultImport, fromPath, true);
                }
                // Register named imports
                if (namedImports) {
                    namedImports.split(',').forEach(imp => {
                        const importName = imp.trim();
                        if (importName) {
                            this.importExportManager.registerImport(filePath, importName, fromPath, false);
                        }
                    });
                }
            }
            // Register exports
            const exportRegex = /^export\s+(?:default\s+)?(?:class|interface|function|const|let|var)\s+(\w+)/gm;
            while ((match = exportRegex.exec(content)) !== null) {
                const [fullMatch, exportName] = match;
                const isDefault = fullMatch.includes('default');
                this.importExportManager.registerExport(filePath, exportName, isDefault);
            }
            // Register export statements
            const exportStatementRegex = /^export\s+\{\s*([^}]+)\s*\}/gm;
            while ((match = exportStatementRegex.exec(content)) !== null) {
                const [, exports] = match;
                exports.split(',').forEach(exp => {
                    const exportName = exp.trim();
                    if (exportName) {
                        this.importExportManager.registerExport(filePath, exportName, false);
                    }
                });
            }
            // Register default exports
            const defaultExportRegex = /^export\s+default\s+(\w+)/gm;
            while ((match = defaultExportRegex.exec(content)) !== null) {
                const [, exportName] = match;
                this.importExportManager.registerExport(filePath, exportName, true);
            }
        }
        catch (error) {
            this.logInfo(`Error scanning imports/exports in ${filePath}`, {
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    generateDomainComponentsWithValidation(spec) {
        // Enhanced method with validation - will replace the old one
        return this.generateDomainComponents(spec);
    }
    /**
     * Repair malformed import statements caused by destructive auto-fixes
     */
    repairMalformedImports(content) {
        let fixedContent = content;
        // Fix malformed imports with nuclear fix comments
        const malformedImportRegex = /import\s+.*?\/\/.*?Nuclear fix.*?import.*?['"]([^'"]+)['"];?/g;
        fixedContent = fixedContent.replace(malformedImportRegex, (match, moduleName) => {
            return `import ${moduleName} from '${moduleName}';`;
        });
        // Fix imports with broken syntax
        fixedContent = fixedContent.replace(/import\s+([^'"]*?)\s*\/\/.*?commented import.*?['"]([^'"]+)['"];?/g, (match, importPart, moduleName) => {
            // Extract the actual import name from the broken syntax
            const cleanImportPart = importPart.replace(/\s*\/\/.*$/, '').trim();
            if (cleanImportPart) {
                return `import ${cleanImportPart} from '${moduleName}';`;
            }
            return `import ${moduleName} from '${moduleName}';`;
        });
        return fixedContent;
    }
    /**
     * Remove destructive artifacts from previous auto-fixes
     */
    removeDestructiveArtifacts(content) {
        let fixedContent = content;
        // Remove @ts-nocheck comments
        fixedContent = fixedContent.replace(/\/\/ @ts-nocheck\s*\n?/g, '');
        // Remove template artifacts
        fixedContent = fixedContent.replace(/```typescript`?/g, '');
        fixedContent = fixedContent.replace(/```/g, '');
        // Fix function calls without proper definitions
        if (fixedContent.includes('createServer()') && !fixedContent.includes('function createServer') && !fixedContent.includes('const createServer')) {
            // Add proper createServer function
            const hasExpressImport = fixedContent.includes("import express from 'express'");
            if (!hasExpressImport) {
                fixedContent = `import express from 'express';\n\n${fixedContent}`;
            }
            const createServerFunction = `
function createServer() {
  const app = express();
  return app;
}
`;
            fixedContent = fixedContent.replace('const app: any = createServer();', `${createServerFunction}\nconst app = createServer();`);
        }
        return fixedContent;
    }
    generateDomainComponents(spec) {
        const files = {};
        // Analyze features to generate domain-specific components
        spec.features.forEach(feature => {
            const featureStr = typeof feature === 'string' ? feature : String(feature);
            const components = this.mapFeatureToComponents(featureStr, spec);
            Object.assign(files, components);
        });
        return files;
    }
    mapFeatureToComponents(feature, spec) {
        const files = {};
        const featureLower = feature.toLowerCase();
        // Barcode scanning components
        if (featureLower.includes('barcode') || featureLower.includes('scan')) {
            files['frontend/src/components/scanning/BarcodeScanner.tsx'] = this.generateBarcodeScannerComponent();
            files['frontend/src/components/scanning/ProductScanner.tsx'] = this.generateProductScannerComponent();
        }
        // Inventory management components
        if (featureLower.includes('inventory') || featureLower.includes('stock')) {
            files['frontend/src/components/Inventory/InventoryManager.tsx'] = this.generateInventoryManagerComponent();
            files['frontend/src/components/Inventory/StockAlerts.tsx'] = this.generateStockAlertsComponent();
        }
        // POS components (only for physical retail, not e-commerce)
        const isPhysicalRetail = (featureLower.includes('pos terminal') || featureLower.includes('point of sale')) &&
            !featureLower.includes('ecommerce') && !featureLower.includes('e-commerce') &&
            !featureLower.includes('online');
        if (isPhysicalRetail) {
            files['frontend/src/components/POS/POSTerminal.tsx'] = this.generatePOSTerminalComponent();
            files['frontend/src/components/POS/ShoppingCart.tsx'] = this.generateShoppingCartComponent();
        }
        // Customer management components
        if (featureLower.includes('customer')) {
            files['frontend/src/components/Customers/CustomerManager.tsx'] = this.generateCustomerManagerComponent();
            files['frontend/src/components/Customers/CustomerHistory.tsx'] = this.generateCustomerHistoryComponent();
        }
        // Payment components
        if (featureLower.includes('payment') || featureLower.includes('cash') || featureLower.includes('card')) {
            files['frontend/src/components/payments/PaymentProcessor.tsx'] = this.generatePaymentProcessorComponent();
            files['frontend/src/components/payments/PaymentMethods.tsx'] = this.generatePaymentMethodsComponent();
        }
        // Reports and analytics components
        if (featureLower.includes('report') || featureLower.includes('analytics') || featureLower.includes('dashboard')) {
            files['frontend/src/components/reports/SalesReports.tsx'] = this.generateSalesReportsComponent();
            files['frontend/src/components/Analytics/AnalyticsDashboard.tsx'] = this.generateAnalyticsDashboardComponent();
        }
        // Warranty components
        if (featureLower.includes('warranty')) {
            files['frontend/src/components/Warranty/WarrantyTracker.tsx'] = this.generateWarrantyTrackerComponent();
            files['frontend/src/components/Warranty/WarrantyClaims.tsx'] = this.generateWarrantyClaimsComponent();
        }
        // Supplier components
        if (featureLower.includes('supplier')) {
            files['frontend/src/components/Suppliers/SupplierManager.tsx'] = this.generateSupplierManagerComponent();
            files['frontend/src/components/Suppliers/OrderManagement.tsx'] = this.generateOrderManagementComponent();
        }
        return files;
    }
    // Barcode scanning components
    generateBarcodeScannerComponent() {
        return `import React, { useState, useRef, useEffect } from 'react';

interface BarcodeScannerProps {
  onScan: (barcode: string) => void;
  onError?: (error: string) => void;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({ onScan, onError }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const videoRef = useRef<HTMLVideoElement>(null);

  const startScanning = async () => {
    try {
      setIsScanning(true);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      onError?.('Camera access denied or not available');
      setIsScanning(false);
    }
  };

  const stopScanning = () => {
    setIsScanning(false);
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
    }
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualBarcode.trim()) {
      onScan(manualBarcode.trim());
      setManualBarcode('');
    }
  };

  return (
    <div className="barcode-scanner">
      <h3>Barcode Scanner</h3>

      {/* Camera Scanner */}
      <div className="camera-section">
        {!isScanning ? (
          <button onClick={startScanning} className="btn btn-primary">
            Start Camera Scanner
          </button>
        ) : (
          <div>
            <video ref={videoRef} autoPlay playsInline />
            <button onClick={stopScanning} className="btn btn-secondary">
              Stop Scanner
            </button>
          </div>
        )}
      </div>

      {/* Manual Entry */}
      <div className="manual-entry">
        <h4>Manual Entry</h4>
        <form onSubmit={handleManualSubmit}>
          <input
            type="text"
            value={manualBarcode}
            onChange={(e) => setManualBarcode(e.target.value)}
            placeholder="Enter barcode manually"
            className="form-control"
          />
          <button type="submit" className="btn btn-primary">
            Scan Barcode
          </button>
        </form>
      </div>
    </div>
  );
};

export default BarcodeScanner;`;
    }
    generateProductScannerComponent() {
        return `import React, { useState } from 'react';
import BarcodeScanner from './BarcodeScanner';

interface Product {
  id: string;
  name: string;
  barcode: string;
  price: number;
  stock: number;
  category: string;
}

interface ProductScannerProps {
  onProductFound: (product: Product) => void;
}

const ProductScanner: React.FC<ProductScannerProps> = ({ onProductFound }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [lastScanned, setLastScanned] = useState('');

  const handleBarcodeScan = async (barcode: string) => {
    if (barcode === lastScanned) return; // Prevent duplicate scans

    setLoading(true);
    setError('');
    setLastScanned(barcode);

    try {
      // TODO: Replace with actual API call
      const response = await fetch(\`/api/products/barcode/\${barcode}\`);

      if (!response.ok) {
        throw new Error('Product not found');
      }

      const product = await response.json();
      onProductFound(product);
    } catch (err) {
      setError(\`Product not found for barcode: \${barcode}\`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="product-scanner">
      <h2>Product Scanner</h2>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      {loading && (
        <div className="loading">
          Searching for product...
        </div>
      )}

      <BarcodeScanner
        onScan={handleBarcodeScan}
        onError={setError}
      />

      {lastScanned && (
        <div className="last-scanned">
          Last scanned: {lastScanned}
        </div>
      )}
    </div>
  );
};

export default ProductScanner;`;
    }
    // POS Terminal Components
    generatePOSTerminalComponent() {
        return `import React, { useState } from 'react';
import ProductScanner from '../scanning/ProductScanner';
import ShoppingCart from './ShoppingCart';
import PaymentProcessor from '../payments/PaymentProcessor';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  barcode: string;
}

const POSTerminal: React.FC = () => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [currentStep, setCurrentStep] = useState<'scan' | 'review' | 'payment'>('scan');
  const [total, setTotal] = useState(0);

  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        barcode: product.barcode
      }]);
    }

    updateTotal();
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
    updateTotal();
  };

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity }
        : item
    ));
    updateTotal();
  };

  const updateTotal = () => {
    const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setTotal(newTotal);
  };

  const proceedToPayment = () => {
    if (cart.length > 0) {
      setCurrentStep('payment');
    }
  };

  const completeSale = () => {
    // TODO: Process sale, print receipt, update inventory
    setCart([]);
    setTotal(0);
    setCurrentStep('scan');
    alert('Sale completed successfully!');
  };

  return (
    <div className="pos-terminal">
      <h1>POS Terminal</h1>

      <div className="pos-layout">
        <div className="left-panel">
          {currentStep === 'scan' && (
            <ProductScanner onProductFound={addToCart} />
          )}

          {currentStep === 'payment' && (
            <PaymentProcessor
              total={total}
              onPaymentComplete={completeSale}
              onCancel={() => setCurrentStep('scan')}
            />
          )}
        </div>

        <div className="right-panel">
          <ShoppingCart
            items={cart}
            total={total}
            onUpdateQuantity={updateQuantity}
            onRemoveItem={removeFromCart}
            onProceedToPayment={proceedToPayment}
            canProceed={cart.length > 0}
          />
        </div>
      </div>
    </div>
  );
};

export default POSTerminal;`;
    }
    generateShoppingCartComponent() {
        return `import React from 'react';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  barcode: string;
}

interface ShoppingCartProps {
  items: CartItem[];
  total: number;
  onUpdateQuantity: (productId: string, quantity: number) => void;
  onRemoveItem: (productId: string) => void;
  onProceedToPayment: () => void;
  canProceed: boolean;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({
  items,
  total,
  onUpdateQuantity,
  onRemoveItem,
  onProceedToPayment,
  canProceed
}) => {
  return (
    <div className="shopping-cart">
      <h3>Shopping Cart</h3>

      {items.length === 0 ? (
        <div className="empty-cart">
          <p>Cart is empty</p>
          <p>Scan products to add them to cart</p>
        </div>
      ) : (
        <>
          <div className="cart-items">
            {items.map(item => (
              <div key={item.id} className="cart-item">
                <div className="item-info">
                  <h4>{item.name}</h4>
                  <p>Barcode: {item.barcode}</p>
                  <p className="price">\${item.price.toFixed(2)}</p>
                </div>

                <div className="quantity-controls">
                  <button
                    onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
                    className="btn btn-sm"
                  >
                    -
                  </button>
                  <span className="quantity">{item.quantity}</span>
                  <button
                    onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                    className="btn btn-sm"
                  >
                    +
                  </button>
                </div>

                <div className="item-total">
                  \${(item.price * item.quantity).toFixed(2)}
                </div>

                <button
                  onClick={() => onRemoveItem(item.id)}
                  className="btn btn-danger btn-sm"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>

          <div className="cart-summary">
            <div className="total">
              <strong>Total: \${total.toFixed(2)}</strong>
            </div>

            <button
              onClick={onProceedToPayment}
              disabled={!canProceed}
              className="btn btn-primary btn-large"
            >
              Proceed to Payment
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ShoppingCart;`;
    }
    // Payment Components
    generatePaymentProcessorComponent() {
        return `import React, { useState } from 'react';

interface PaymentProcessorProps {
  total: number;
  onPaymentComplete: () => void;
  onCancel: () => void;
}

const PaymentProcessor: React.FC<PaymentProcessorProps> = ({
  total,
  onPaymentComplete,
  onCancel
}) => {
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'mobile'>('cash');
  const [cashReceived, setCashReceived] = useState('');
  const [processing, setProcessing] = useState(false);

  const handleCashPayment = () => {
    const received = parseFloat(cashReceived);
    if (received >= total) {
      setProcessing(true);
      setTimeout(() => {
        onPaymentComplete();
        setProcessing(false);
      }, 1000);
    } else {
      alert('Insufficient cash amount');
    }
  };

  const handleCardPayment = () => {
    setProcessing(true);
    // Simulate card processing
    setTimeout(() => {
      onPaymentComplete();
      setProcessing(false);
    }, 2000);
  };

  const change = cashReceived ? parseFloat(cashReceived) - total : 0;

  return (
    <div className="payment-processor">
      <h3>Payment Processing</h3>
      <div className="total-amount">
        <h2>Total: \${total.toFixed(2)}</h2>
      </div>

      <div className="payment-methods">
        <div className="method-selector">
          <label>
            <input
              type="radio"
              value="cash"
              checked={paymentMethod === 'cash'}
              onChange={(e) => setPaymentMethod(e.target.value as any)}
            />
            Cash
          </label>
          <label>
            <input
              type="radio"
              value="card"
              checked={paymentMethod === 'card'}
              onChange={(e) => setPaymentMethod(e.target.value as any)}
            />
            Card
          </label>
          <label>
            <input
              type="radio"
              value="mobile"
              checked={paymentMethod === 'mobile'}
              onChange={(e) => setPaymentMethod(e.target.value as any)}
            />
            Mobile Payment
          </label>
        </div>

        {paymentMethod === 'cash' && (
          <div className="cash-payment">
            <label>Cash Received:</label>
            <input
              type="number"
              step="0.01"
              value={cashReceived}
              onChange={(e) => setCashReceived(e.target.value)}
              placeholder="0.00"
            />
            {change > 0 && (
              <div className="change">
                Change: \${change.toFixed(2)}
              </div>
            )}
            <button
              onClick={handleCashPayment}
              disabled={processing || !cashReceived || parseFloat(cashReceived) < total}
              className="btn btn-primary"
            >
              {processing ? 'Processing...' : 'Complete Cash Payment'}
            </button>
          </div>
        )}

        {paymentMethod === 'card' && (
          <div className="card-payment">
            <p>Insert or swipe card</p>
            <button
              onClick={handleCardPayment}
              disabled={processing}
              className="btn btn-primary"
            >
              {processing ? 'Processing Card...' : 'Process Card Payment'}
            </button>
          </div>
        )}

        {paymentMethod === 'mobile' && (
          <div className="mobile-payment">
            <p>Show QR code to customer</p>
            <div className="qr-placeholder">
              [QR Code would appear here]
            </div>
            <button
              onClick={handleCardPayment}
              disabled={processing}
              className="btn btn-primary"
            >
              {processing ? 'Processing...' : 'Confirm Mobile Payment'}
            </button>
          </div>
        )}
      </div>

      <button onClick={onCancel} className="btn btn-secondary">
        Cancel Payment
      </button>
    </div>
  );
};

export default PaymentProcessor;`;
    }
    // Placeholder methods for other components
    generatePaymentMethodsComponent() {
        return `import React from 'react';

const PaymentMethods: React.FC = () => {
  return (
    <div className="payment-methods">
      <h3>Payment Methods Configuration</h3>
      <p>Configure accepted payment methods here</p>
    </div>
  );
};

export default PaymentMethods;`;
    }
    generateInventoryManagerComponent() {
        return `import React from 'react';

const InventoryManager: React.FC = () => {
  return (
    <div className="inventory-manager">
      <h3>Inventory Management</h3>
      <p>Manage stock levels and inventory</p>
    </div>
  );
};

export default InventoryManager;`;
    }
    generateStockAlertsComponent() {
        return `import React from 'react';

const StockAlerts: React.FC = () => {
  return (
    <div className="stock-alerts">
      <h3>Stock Alerts</h3>
      <p>Low stock and reorder point alerts</p>
    </div>
  );
};

export default StockAlerts;`;
    }
    generateCustomerManagerComponent() {
        return `import React from 'react';

const CustomerManager: React.FC = () => {
  return (
    <div className="customer-manager">
      <h3>Customer Management</h3>
      <p>Manage customer information and history</p>
    </div>
  );
};

export default CustomerManager;`;
    }
    generateCustomerHistoryComponent() {
        return `import React from 'react';

const CustomerHistory: React.FC = () => {
  return (
    <div className="customer-history">
      <h3>Customer Purchase History</h3>
      <p>View customer purchase history</p>
    </div>
  );
};

export default CustomerHistory;`;
    }
    generateSalesReportsComponent() {
        return `import React from 'react';

const SalesReports: React.FC = () => {
  return (
    <div className="sales-reports">
      <h3>Sales Reports</h3>
      <p>Generate and view sales reports</p>
    </div>
  );
};

export default SalesReports;`;
    }
    generateAnalyticsDashboardComponent() {
        return `import React from 'react';

const AnalyticsDashboard: React.FC = () => {
  return (
    <div className="analytics-dashboard">
      <h3>Analytics Dashboard</h3>
      <p>View sales analytics and performance metrics</p>
    </div>
  );
};

export default AnalyticsDashboard;`;
    }
    generateWarrantyTrackerComponent() {
        return `import React from 'react';

const WarrantyTracker: React.FC = () => {
  return (
    <div className="warranty-tracker">
      <h3>Warranty Tracking</h3>
      <p>Track warranty information for parts</p>
    </div>
  );
};

export default WarrantyTracker;`;
    }
    generateWarrantyClaimsComponent() {
        return `import React from 'react';

const WarrantyClaims: React.FC = () => {
  return (
    <div className="warranty-claims">
      <h3>Warranty Claims</h3>
      <p>Process warranty claims</p>
    </div>
  );
};

export default WarrantyClaims;`;
    }
    generateSupplierManagerComponent() {
        return `import React from 'react';

const SupplierManager: React.FC = () => {
  return (
    <div className="supplier-manager">
      <h3>Supplier Management</h3>
      <p>Manage supplier information and orders</p>
    </div>
  );
};

export default SupplierManager;`;
    }
    generateOrderManagementComponent() {
        return `import React from 'react';

const OrderManagement: React.FC = () => {
  return (
    <div className="order-management">
      <h3>Order Management</h3>
      <p>Manage supplier orders and deliveries</p>
    </div>
  );
};

export default OrderManagement;`;
    }
    generateTodoRoutes() {
        return `import express from 'express';
import { pool } from '../config/database';
import { authenticateToken } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();

// Get all todos for authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;

    const result = await pool.query(
      'SELECT * FROM todos WHERE user_id = $1 ORDER BY created_at DESC',
      [userId]
    );

    res.json(result.rows);
  } catch (error) {
    logger.error('Get todos error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create new todo
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;
    const { title, description, priority } = req.body;

    if (!title) {
      return res.status(400).json({ message: 'Title is required' });
    }

    const result = await pool.query(
      'INSERT INTO todos (user_id, title, description, priority, completed) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [userId, title, description || '', priority || 'medium', false]
    );

    logger.info(\`Todo created: \${result.rows[0].id} by user \${userId}\`);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    logger.error('Create todo error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update todo
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;
    const todoId = parseInt(req.params.id);
    const { title, description, priority, completed } = req.body;

    // Check if todo belongs to user
    const checkResult = await pool.query(
      'SELECT id FROM todos WHERE id = $1 AND user_id = $2',
      [todoId, userId]
    );

    if (checkResult.rows.length === 0) {
      return res.status(404).json({ message: 'Todo not found' });
    }

    const result = await pool.query(
      'UPDATE todos SET title = COALESCE($1, title), description = COALESCE($2, description), priority = COALESCE($3, priority), completed = COALESCE($4, completed), updated_at = CURRENT_TIMESTAMP WHERE id = $5 AND user_id = $6 RETURNING *',
      [title, description, priority, completed, todoId, userId]
    );

    logger.info(\`Todo updated: \${todoId} by user \${userId}\`);
    res.json(result.rows[0]);
  } catch (error) {
    logger.error('Update todo error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete todo
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const userId = (req as any).userId;
    const todoId = parseInt(req.params.id);

    const result = await pool.query(
      'DELETE FROM todos WHERE id = $1 AND user_id = $2 RETURNING id',
      [todoId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Todo not found' });
    }

    logger.info(\`Todo deleted: \${todoId} by user \${userId}\`);
    res.status(204).send();
  } catch (error) {
    logger.error('Delete todo error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;`;
    }
    /**
     * Generate docker-compose.yml for the application
     */
    generateDockerCompose(spec) {
        return `version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: ${spec.projectName}_postgres
    environment:
      POSTGRES_DB: ${spec.projectName}
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ${spec.projectName}_backend
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/${spec.projectName}
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    networks:
      - app-network
    volumes:
      - ./backend:/app
      - /app/node_modules

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ${spec.projectName}_frontend
    environment:
      REACT_APP_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - app-network
    volumes:
      - ./frontend:/app
      - /app/node_modules

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
`;
    }
    /**
     * Generate essential files that are critical for application functionality
     */
    async generateEssentialFiles(files, technicalSpec, semanticAnalysis) {
        this.logInfo('Generating essential configuration files');
        // Generate TypeScript configuration for backend
        files['backend/tsconfig.json'] = this.generateTsConfig();
        // Generate main entry point for backend (index.ts)
        files['backend/src/index.ts'] = this.generateBackendIndex();
        // Generate Jest configuration (ES modules compatible)
        files['backend/jest.config.cjs'] = this.generateJestConfig();
        // Generate frontend index.tsx if missing
        if (!files['frontend/src/index.tsx']) {
            files['frontend/src/index.tsx'] = this.generateFrontendIndex();
        }
        // Generate TypeScript configuration for frontend
        files['frontend/tsconfig.json'] = this.generateFrontendTsConfig();
        // Generate essential React pages
        await this.generateEssentialReactPages(files, semanticAnalysis);
        this.logInfo('Essential files generation completed');
    }
    /**
     * Generate TypeScript configuration
     */
    generateTsConfig() {
        return JSON.stringify({
            compilerOptions: {
                target: "ES2020",
                module: "commonjs",
                outDir: "./dist",
                rootDir: "./src",
                strict: true,
                esModuleInterop: true,
                skipLibCheck: true,
                forceConsistentCasingInFileNames: true,
                resolveJsonModule: true,
                declaration: true,
                declarationMap: true,
                sourceMap: true
            },
            include: ["src/**/*"],
            exclude: ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
        }, null, 2);
    }
    /**
     * Generate backend index.ts entry point
     */
    generateBackendIndex() {
        return `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from './server';

const app = createServer();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors());

app.listen(PORT, () => {
  console.log(\`🚀 Server running on port \${PORT}\`);
  console.log(\`📊 Environment: \${process.env.NODE_ENV || 'development'}\`);
});

export default app;
`;
    }
    /**
     * Generate Jest configuration for ES modules
     */
    generateJestConfig() {
        return `module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleFileExtensions: ['ts', 'js', 'json'],
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: false
    }
  }
};
`;
    }
    /**
     * Generate frontend index.tsx
     */
    generateFrontendIndex() {
        return `import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import App from './App';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <App />
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
);
`;
    }
    /**
     * Generate frontend TypeScript configuration
     */
    generateFrontendTsConfig() {
        return JSON.stringify({
            compilerOptions: {
                target: "es5",
                lib: ["dom", "dom.iterable", "es6"],
                allowJs: true,
                skipLibCheck: true,
                esModuleInterop: true,
                allowSyntheticDefaultImports: true,
                strict: true,
                forceConsistentCasingInFileNames: true,
                noFallthroughCasesInSwitch: true,
                module: "esnext",
                moduleResolution: "node",
                resolveJsonModule: true,
                isolatedModules: true,
                noEmit: true,
                jsx: "react-jsx"
            },
            include: ["src"],
            exclude: ["node_modules"]
        }, null, 2);
    }
    /**
     * Generate essential React pages that should be in the pages folder
     */
    async generateEssentialReactPages(files, semanticAnalysis) {
        // Generate Home page
        files['frontend/src/pages/HomePage.tsx'] = this.generateHomePage(semanticAnalysis);
        // Generate Dashboard page
        files['frontend/src/pages/DashboardPage.tsx'] = this.generateDashboardPage(semanticAnalysis);
        // Generate Login page
        files['frontend/src/pages/LoginPage.tsx'] = this.generateLoginPage();
        // Generate NotFound page
        files['frontend/src/pages/NotFoundPage.tsx'] = this.generateNotFoundPage();
        this.logInfo('Generated essential React pages', {
            pages: ['HomePage', 'DashboardPage', 'LoginPage', 'NotFoundPage']
        });
    }
    /**
     * Generate Home page component
     */
    generateHomePage(semanticAnalysis) {
        return `import React from 'react';
import { Container, Typography, Box, Button, Grid, Card, CardContent } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h2" component="h1" gutterBottom align="center">
          Welcome to ${semanticAnalysis.domain} Application
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom align="center" color="text.secondary">
          Your comprehensive ${semanticAnalysis.domain} management solution
        </Typography>

        <Grid container spacing={4} sx={{ mt: 4 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h5" component="h3" gutterBottom>
                  Get Started
                </Typography>
                <Typography variant="body1" paragraph>
                  Access your dashboard to manage all ${semanticAnalysis.domain} features.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => navigate('/dashboard')}
                >
                  Go to Dashboard
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h5" component="h3" gutterBottom>
                  Features
                </Typography>
                <Typography variant="body1" paragraph>
                  Explore all the powerful features available in this application.
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => navigate('/features')}
                >
                  Learn More
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default HomePage;
`;
    }
    /**
     * Generate Dashboard page component
     */
    generateDashboardPage(semanticAnalysis) {
        return `import React from 'react';
import { Container, Typography, Box, Grid, Card, CardContent, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="h6" component="h2" gutterBottom color="text.secondary">
          Manage your ${semanticAnalysis.domain} application
        </Typography>

        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  Quick Actions
                </Typography>
                <Typography variant="body2" paragraph>
                  Access frequently used features
                </Typography>
                <Button variant="contained" size="small">
                  View All
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  Recent Activity
                </Typography>
                <Typography variant="body2" paragraph>
                  See your latest actions
                </Typography>
                <Button variant="outlined" size="small">
                  View History
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  Settings
                </Typography>
                <Typography variant="body2" paragraph>
                  Configure your preferences
                </Typography>
                <Button variant="outlined" size="small">
                  Open Settings
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default DashboardPage;
`;
    }
    /**
     * Generate Login page component
     */
    generateLoginPage() {
        return `import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      // TODO: Implement actual authentication
      if (email && password) {
        navigate('/dashboard');
      } else {
        setError('Please enter both email and password');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Typography component="h1" variant="h4" align="center" gutterBottom>
            Sign In
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              autoComplete="email"
              autoFocus
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
            >
              Sign In
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
`;
    }
    /**
     * Generate NotFound page component
     */
    generateNotFoundPage() {
        return `import React from 'react';
import { Container, Typography, Box, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <Typography variant="h1" component="h1" gutterBottom>
          404
        </Typography>
        <Typography variant="h4" component="h2" gutterBottom>
          Page Not Found
        </Typography>
        <Typography variant="body1" paragraph>
          The page you are looking for doesn't exist or has been moved.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/')}
          sx={{ mt: 2 }}
        >
          Go Home
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
`;
    }
    /**
     * Validate and resolve file structure conflicts between BusinessLogicAgent and CodingAgent
     */
    async validateAndResolveFileStructureConflicts(projectPath, businessLogic) {
        if (!projectPath || !businessLogic) {
            return;
        }
        try {
            // Check for existing files from BusinessLogicAgent
            const backendPath = path.join(projectPath, 'backend', 'src');
            if (fs.existsSync(backendPath)) {
                this.logInfo('Detected existing backend structure from BusinessLogicAgent', {
                    backendPath
                });
                // Check for conflicting model files
                const modelsPath = path.join(backendPath, 'models');
                if (fs.existsSync(modelsPath)) {
                    const existingModels = fs.readdirSync(modelsPath)
                        .filter(file => file.endsWith('.ts'))
                        .map(file => file.replace('.ts', ''));
                    this.logInfo('Found existing model files from BusinessLogicAgent', {
                        models: existingModels
                    });
                }
            }
        }
        catch (error) {
            this.logError('Error validating file structure conflicts', {
                error: error instanceof Error ? error.message : String(error),
                projectPath
            });
        }
    }
}
exports.CodingAgent = CodingAgent;
//# sourceMappingURL=CodingAgent.js.map