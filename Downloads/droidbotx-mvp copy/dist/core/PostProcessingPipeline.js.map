{"version": 3, "file": "PostProcessingPipeline.js", "sourceRoot": "", "sources": ["../../src/core/PostProcessingPipeline.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAClC,qDAAkE;AAiBlE;;GAEG;AACH,MAAa,sBAAsB;IAIjC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,+BAAc,CAAC,WAAW,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAC/B,IAAY,EACZ,QAAgB,EAChB,OAAuB;QAEvB,MAAM,MAAM,GAAyB;YACnC,OAAO,EAAE,IAAI;YACb,aAAa,EAAE,IAAI;YACnB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,YAAY,EAAE,EAAE;SACjB,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAErE,qCAAqC;YACrC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAEtD,0CAA0C;YAC1C,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAC9F,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE3D,oCAAoC;YACpC,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAChF,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEhD,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACtF,MAAM,CAAC,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEpD,4BAA4B;YAC5B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC1F,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YAElD,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,QAAQ;gBACR,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;gBACjC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM;gBACrC,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM;aACzC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACxG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,IAAY;QAC1C,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,qCAAqC;QACrC,MAAM,gBAAgB,GAAG;YACvB,gCAAgC;YAChC,iGAAiG;YACjG,sBAAsB;YACtB,eAAe;YACf,WAAW;YACX,YAAY;YACZ,8CAA8C;YAC9C,QAAQ;SACT,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,sCAAsC;QACtC,MAAM,gBAAgB,GAAG;YACvB,qBAAqB;YACrB,sBAAsB;YACtB,sBAAsB;YACtB,wBAAwB;YACxB,sBAAsB;YACtB,wBAAwB;YACxB,uBAAuB;SACxB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAY,EAAE,QAAgB;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,mCAAmC;QACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACpD,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;YACtB,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE9B,gCAAgC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;YAC7D,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAEnC,qCAAqC;YACrC,MAAM,aAAa,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;YAClE,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,2BAA2B;QAC3B,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACzC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,IAAY;QACzC,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC;QAEjB,6CAA6C;QAC7C,MAAM,gBAAgB,GAAG,2IAA2I,CAAC;QAErK,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE;YACrE,KAAK,CAAC,IAAI,CAAC,yCAAyC,OAAO,EAAE,CAAC,CAAC;YAC/D,OAAO,SAAS,OAAO,UAAU,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,IAAY;QAC7C,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC;QAEjB,kCAAkC;QAClC,MAAM,qBAAqB,GAAG,cAAc,CAAC;QAC7C,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;YACnE,KAAK,CAAC,IAAI,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;YAC9D,OAAO,GAAG,YAAY,IAAI,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,+BAA+B,CAAC,IAAY;QAClD,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC;QAEjB,wCAAwC;QACxC,MAAM,aAAa,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACvD,IAAI,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,yDAAyD;YACzD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;oBACtD,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;oBAChB,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBAClD,MAAM;gBACR,CAAC;YACH,CAAC;YACD,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,QAAgB;QAC5D,MAAM,MAAM,GAAG;YACb,IAAI;YACJ,MAAM,EAAE,EAAc;YACtB,QAAQ,EAAE,EAAc;YACxB,KAAK,EAAE,EAAc;SACtB,CAAC;QAEF,iCAAiC;QACjC,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;QAED,uCAAuC;QACvC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,IAAY;QACjD,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YACjC,MAAM,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE;gBACtC,eAAe,EAAE;oBACf,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM;oBAC9B,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;oBAC9B,MAAM,EAAE,KAAK,EAAE,iCAAiC;oBAChD,YAAY,EAAE,IAAI;iBACnB;gBACD,iBAAiB,EAAE,IAAI;aACxB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBAC5C,MAAM,OAAO,GAAG,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;oBAC9E,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;wBACxD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4CAA4C;YAC5C,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,IAAY,EAAE,QAAgB;QACjE,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,6BAA6B;QAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAED,sBAAsB;QACtB,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;YACpD,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;YACrD,IAAI,UAAU,KAAK,WAAW,EAAE,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,qBAAqB,UAAU,UAAU,WAAW,QAAQ,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;YACR,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AA7SD,wDA6SC;AAED,kBAAe,sBAAsB,CAAC"}