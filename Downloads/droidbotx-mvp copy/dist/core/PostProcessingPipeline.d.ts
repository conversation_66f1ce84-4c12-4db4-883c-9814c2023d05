import { ProjectContext } from './TemplateEngine';
export interface PostProcessingResult {
    success: boolean;
    processedCode: string;
    issues: string[];
    warnings: string[];
    appliedFixes: string[];
}
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    score: number;
}
/**
 * Comprehensive post-processing pipeline for generated code
 */
export declare class PostProcessingPipeline {
    private logger;
    private templateEngine;
    constructor();
    /**
     * Process generated code through comprehensive pipeline
     */
    processGeneratedCode(code: string, fileName: string, context: ProjectContext): Promise<PostProcessingResult>;
    /**
     * Remove markdown artifacts with comprehensive patterns
     */
    private removeMarkdownArtifacts;
    /**
     * Fix React imports by detecting used hooks and components
     */
    private fixReactImports;
    /**
     * Convert template placeholders to actual imports
     */
    private convertTemplatePlaceholdersToImports;
    /**
     * Fix code structure issues like duplicate classes, variable redefinitions, etc.
     */
    private fixCodeStructureIssues;
    /**
     * Fix type consistency issues between frontend and backend
     */
    private fixTypeConsistencyIssues;
    /**
     * Fix common syntax issues
     */
    private fixCommonSyntaxIssues;
    /**
     * Fix broken SQL template literals
     */
    private fixSQLTemplateLiterals;
    /**
     * Fix incomplete function calls
     */
    private fixIncompleteFunctionCalls;
    /**
     * Fix unterminated template literals
     */
    private fixUnterminatedTemplateLiterals;
    /**
     * Validate and repair code
     */
    private validateAndRepair;
    /**
     * Validate TypeScript syntax
     */
    private validateTypeScriptSyntax;
    /**
     * Perform final validation
     */
    private performFinalValidation;
}
export default PostProcessingPipeline;
//# sourceMappingURL=PostProcessingPipeline.d.ts.map