"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostProcessingPipeline = void 0;
const Logger_1 = require("./Logger");
const TemplateEngine_1 = require("./TemplateEngine");
/**
 * Comprehensive post-processing pipeline for generated code
 */
class PostProcessingPipeline {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.templateEngine = TemplateEngine_1.TemplateEngine.getInstance();
    }
    /**
     * Process generated code through comprehensive pipeline
     */
    async processGeneratedCode(code, fileName, context) {
        const result = {
            success: true,
            processedCode: code,
            issues: [],
            warnings: [],
            appliedFixes: []
        };
        try {
            this.logger.debug('Starting post-processing pipeline', { fileName });
            // Phase 1: Remove markdown artifacts
            result.processedCode = this.removeMarkdownArtifacts(result.processedCode);
            result.appliedFixes.push('Markdown artifact removal');
            // Phase 2: Template variable substitution
            result.processedCode = this.templateEngine.substituteVariables(result.processedCode, context);
            result.appliedFixes.push('Template variable substitution');
            // Phase 3: Fix React imports (for React components)
            if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
                const reactResult = this.fixReactImports(result.processedCode);
                result.processedCode = reactResult.code;
                result.appliedFixes.push(...reactResult.fixes);
            }
            // Phase 4: Convert template placeholders to actual imports
            const importResult = this.convertTemplatePlaceholdersToImports(result.processedCode, fileName);
            result.processedCode = importResult.code;
            result.appliedFixes.push(...importResult.fixes);
            // Phase 5: Fix code structure issues
            const structureResult = this.fixCodeStructureIssues(result.processedCode, fileName);
            result.processedCode = structureResult.code;
            result.appliedFixes.push(...structureResult.fixes);
            // Phase 6: Fix type consistency issues
            const typeResult = this.fixTypeConsistencyIssues(result.processedCode, fileName);
            result.processedCode = typeResult.code;
            result.appliedFixes.push(...typeResult.fixes);
            // Phase 7: Fix common syntax issues
            const syntaxResult = this.fixCommonSyntaxIssues(result.processedCode, fileName);
            result.processedCode = syntaxResult.code;
            result.appliedFixes.push(...syntaxResult.fixes);
            // Phase 4: Validate and repair
            const validationResult = await this.validateAndRepair(result.processedCode, fileName);
            result.processedCode = validationResult.code;
            result.issues.push(...validationResult.issues);
            result.warnings.push(...validationResult.warnings);
            result.appliedFixes.push(...validationResult.fixes);
            // Phase 5: Final validation
            const finalValidation = await this.performFinalValidation(result.processedCode, fileName);
            result.issues.push(...finalValidation.errors);
            result.warnings.push(...finalValidation.warnings);
            result.success = result.issues.length === 0;
            this.logger.debug('Post-processing pipeline completed', {
                fileName,
                success: result.success,
                issuesCount: result.issues.length,
                warningsCount: result.warnings.length,
                fixesApplied: result.appliedFixes.length
            });
        }
        catch (error) {
            result.success = false;
            result.issues.push(`Post-processing failed: ${error instanceof Error ? error.message : String(error)}`);
            this.logger.error('Post-processing pipeline failed', { fileName, error });
        }
        return result;
    }
    /**
     * Remove markdown artifacts with comprehensive patterns
     */
    removeMarkdownArtifacts(code) {
        let cleaned = code;
        // ENHANCED: More comprehensive markdown removal patterns
        const markdownPatterns = [
            // Remove opening markdown blocks (preserve content)
            /^```[a-zA-Z]*\s*\n?/gm,
            /```[a-zA-Z]*\s*\n?/g,
            // Remove closing markdown blocks
            /\n?```\s*$/gm,
            /\n?```/g,
            // Remove any remaining backticks (3 or more)
            /`{3,}/g,
            // Remove standalone markdown markers
            /^```\s*$/gm,
            /```\s*$/gm
        ];
        // Apply patterns multiple times to catch nested cases
        for (let i = 0; i < 3; i++) {
            for (const pattern of markdownPatterns) {
                const before = cleaned;
                cleaned = cleaned.replace(pattern, '');
                if (before !== cleaned) {
                    this.logger.debug(`Markdown removal iteration ${i + 1}: Applied pattern ${pattern.source}`);
                }
            }
        }
        // Remove common AI response artifacts
        const artifactPatterns = [
            /^Here's the.*?:\s*/i,
            /^Here is the.*?:\s*/i,
            /^I'll create.*?:\s*/i,
            /^Let me create.*?:\s*/i,
            /^This is the.*?:\s*/i,
            /^The following.*?:\s*/i,
            /^Below is the.*?:\s*/i,
            /^This code.*?:\s*/i,
            /^The code.*?:\s*/i
        ];
        for (const pattern of artifactPatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        // Remove leading/trailing whitespace and empty lines
        cleaned = cleaned.trim();
        // Remove excessive empty lines (more than 2 consecutive)
        cleaned = cleaned.replace(/\n{3,}/g, '\n\n');
        return cleaned;
    }
    /**
     * Fix React imports by detecting used hooks and components
     */
    fixReactImports(code) {
        const fixes = [];
        let fixed = code;
        // Detect React hooks and features used in the code
        const reactFeatures = {
            useState: /useState\s*[<(]/g.test(code),
            useEffect: /useEffect\s*\(/g.test(code),
            useContext: /useContext\s*\(/g.test(code),
            useReducer: /useReducer\s*\(/g.test(code),
            useCallback: /useCallback\s*\(/g.test(code),
            useMemo: /useMemo\s*\(/g.test(code),
            useRef: /useRef\s*[<(]/g.test(code),
            useImperativeHandle: /useImperativeHandle\s*\(/g.test(code),
            useLayoutEffect: /useLayoutEffect\s*\(/g.test(code),
            useDebugValue: /useDebugValue\s*\(/g.test(code),
            Component: /extends\s+Component/g.test(code),
            PureComponent: /extends\s+PureComponent/g.test(code),
            Fragment: /<Fragment|<React\.Fragment/g.test(code),
            createContext: /createContext\s*\(/g.test(code),
            forwardRef: /forwardRef\s*\(/g.test(code),
            memo: /memo\s*\(/g.test(code)
        };
        // Check current imports
        const importMatch = fixed.match(/^import\s+.*?from\s+['"]react['"];?\s*$/m);
        const hasReactImport = !!importMatch;
        if (hasReactImport) {
            const currentImport = importMatch[0];
            const usedFeatures = Object.keys(reactFeatures).filter(feature => reactFeatures[feature]);
            if (usedFeatures.length > 0) {
                // Extract current imports
                const defaultImportMatch = currentImport.match(/import\s+(\w+)/);
                const namedImportsMatch = currentImport.match(/import\s+(?:\w+,\s*)?\{\s*([^}]+)\s*\}/);
                const hasDefaultImport = defaultImportMatch && defaultImportMatch[1] === 'React';
                const currentNamedImports = namedImportsMatch ?
                    namedImportsMatch[1].split(',').map(imp => imp.trim()) : [];
                // Determine what needs to be imported
                const neededImports = usedFeatures.filter(feature => !currentNamedImports.includes(feature));
                if (neededImports.length > 0) {
                    const allNamedImports = [...new Set([...currentNamedImports, ...neededImports])];
                    const newImport = hasDefaultImport ?
                        `import React, { ${allNamedImports.join(', ')} } from 'react';` :
                        `import { ${allNamedImports.join(', ')} } from 'react';`;
                    fixed = fixed.replace(currentImport, newImport);
                    fixes.push(`Added missing React imports: ${neededImports.join(', ')}`);
                }
            }
        }
        else {
            // No React import exists, add one if React features are used
            const usedFeatures = Object.keys(reactFeatures).filter(feature => reactFeatures[feature]);
            const needsReactDefault = /React\./g.test(code) || /<[A-Z]/g.test(code) || /React\.FC/g.test(code);
            if (usedFeatures.length > 0 || needsReactDefault) {
                const newImport = needsReactDefault && usedFeatures.length > 0 ?
                    `import React, { ${usedFeatures.join(', ')} } from 'react';` :
                    needsReactDefault ?
                        `import React from 'react';` :
                        `import { ${usedFeatures.join(', ')} } from 'react';`;
                // Insert import at the very beginning of the file
                const lines = fixed.split('\n');
                let insertIndex = 0;
                // Skip any empty lines at the beginning
                while (insertIndex < lines.length && lines[insertIndex].trim() === '') {
                    insertIndex++;
                }
                lines.splice(insertIndex, 0, newImport);
                fixed = lines.join('\n');
                fixes.push(`Added React import with: ${usedFeatures.join(', ')}`);
            }
        }
        return { code: fixed, fixes };
    }
    /**
     * Convert template placeholders to actual imports
     */
    convertTemplatePlaceholdersToImports(code, fileName) {
        const fixes = [];
        let fixed = code;
        // Only process files that actually have template placeholders
        // Check for obvious template placeholder patterns first
        const hasTemplatePlaceholders = /^import\s+\{[^}]+\}\s*$/gm.test(code) &&
            !code.includes('from \'') &&
            !code.includes('from "');
        if (!hasTemplatePlaceholders) {
            // File already has proper imports, skip template replacement
            return { code: fixed, fixes };
        }
        // Define common template placeholder to import mappings
        // Only match placeholders that are clearly template variables (standalone on lines or at start of imports)
        const importMappings = [
            // Express imports - only match if they're standalone or at start of line
            {
                placeholder: /^import\s+\{\s*Router,\s*Request,\s*Response,?\s*NextFunction\s*\}\s*$/gm,
                replacement: "import { Router, Request, Response, NextFunction } from 'express';",
                description: 'Convert Express imports placeholder'
            },
            {
                placeholder: /^import\s+\{\s*Router,\s*Request,\s*Response\s*\}\s*$/gm,
                replacement: "import { Router, Request, Response } from 'express';",
                description: 'Convert Express Router imports placeholder'
            },
            {
                placeholder: /^import\s+\{\s*Request,\s*Response\s*\}\s*$/gm,
                replacement: "import { Request, Response } from 'express';",
                description: 'Convert Express Request/Response imports placeholder'
            },
            // Database imports - only match if they're standalone or at start of line
            {
                placeholder: /^import\s+\{\s*Pool,?\s*QueryResult\s*\}\s*$/gm,
                replacement: "import { Pool, QueryResult } from 'pg';",
                description: 'Convert PostgreSQL imports placeholder'
            },
            {
                placeholder: /^import\s+\{\s*Pool\s*\}\s*$/gm,
                replacement: "import { Pool } from 'pg';",
                description: 'Convert PostgreSQL Pool import placeholder'
            },
            // JWT imports
            {
                placeholder: /^import\s+\{\s*jwt\s*\}\s*$/gm,
                replacement: "import jwt from 'jsonwebtoken';",
                description: 'Convert JWT import placeholder'
            },
            // Validation imports
            {
                placeholder: /^import\s+\{\s*body,\s*validationResult\s*\}\s*$/gm,
                replacement: "import { body, validationResult } from 'express-validator';",
                description: 'Convert express-validator imports placeholder'
            },
            // Logger imports
            {
                placeholder: /\{\s*Logger\s*\}/g,
                replacement: "import { Logger } from '../utils/Logger.js';",
                description: 'Convert Logger import placeholder'
            },
            {
                placeholder: /\{\s*createLogger,\s*Logger\s*\}/g,
                replacement: "import { createLogger, Logger } from 'winston';",
                description: 'Convert Winston Logger imports placeholder'
            },
            // Error imports
            {
                placeholder: /\{\s*DatabaseError\s*\}/g,
                replacement: "import { DatabaseError } from '../errors/DatabaseError.js';",
                description: 'Convert DatabaseError import placeholder'
            },
            {
                placeholder: /\{\s*ValidationError\s*\}/g,
                replacement: "import { ValidationError } from '../errors/ValidationError.js';",
                description: 'Convert ValidationError import placeholder'
            },
            {
                placeholder: /\{\s*UserNotFoundError\s*\}/g,
                replacement: "import { UserNotFoundError } from '../errors/UserNotFoundError.js';",
                description: 'Convert UserNotFoundError import placeholder'
            },
            {
                placeholder: /\{\s*InvalidCredentialsError\s*\}/g,
                replacement: "import { InvalidCredentialsError } from '../errors/InvalidCredentialsError.js';",
                description: 'Convert InvalidCredentialsError import placeholder'
            },
            // Model imports (dynamic based on filename)
            {
                placeholder: /\{\s*Product\s*\}/g,
                replacement: "import { Product } from '../models/Product.js';",
                description: 'Convert Product model import placeholder'
            },
            {
                placeholder: /\{\s*User\s*\}/g,
                replacement: "import { User } from '../models/User.js';",
                description: 'Convert User model import placeholder'
            },
            {
                placeholder: /\{\s*Category\s*\}/g,
                replacement: "import { Category } from '../models/Category.js';",
                description: 'Convert Category model import placeholder'
            },
            // Repository imports
            {
                placeholder: /\{\s*ProductRepository\s*\}/g,
                replacement: "import { ProductRepository } from '../repositories/ProductRepository.js';",
                description: 'Convert ProductRepository import placeholder'
            },
            {
                placeholder: /\{\s*UserRepository\s*\}/g,
                replacement: "import { UserRepository } from '../repositories/UserRepository.js';",
                description: 'Convert UserRepository import placeholder'
            },
            {
                placeholder: /\{\s*CategoryRepository\s*\}/g,
                replacement: "import { CategoryRepository } from '../repositories/CategoryRepository.js';",
                description: 'Convert CategoryRepository import placeholder'
            },
            // Utility imports
            {
                placeholder: /\{\s*hashPassword,\s*comparePasswords\s*\}/g,
                replacement: "import { hashPassword, comparePasswords } from '../utils/auth.js';",
                description: 'Convert auth utilities import placeholder'
            }
        ];
        // Apply import mappings
        for (const mapping of importMappings) {
            const originalCode = fixed;
            fixed = fixed.replace(mapping.placeholder, mapping.replacement);
            if (originalCode !== fixed) {
                fixes.push(mapping.description);
                this.logger.debug(`Applied import conversion: ${mapping.description}`);
            }
        }
        // Fix malformed imports that got double-replaced
        const malformedImportFixes = [
            {
                pattern: /import import \{([^}]+)\} from '([^']+)'; from '([^']+)';/g,
                replacement: "import { $1 } from '$2';",
                description: 'Fix malformed double imports'
            },
            {
                pattern: /import import ([^;]+); from '([^']+)';/g,
                replacement: "import $1 from '$2';",
                description: 'Fix malformed default imports'
            },
            // More aggressive patterns for edge cases
            {
                pattern: /import import \{([^}]+)\} from '([^']+)';/g,
                replacement: "import { $1 } from '$2';",
                description: 'Fix double import statements'
            },
            {
                pattern: /import ([^;]+); from '([^']+)';/g,
                replacement: "import $1 from '$2';",
                description: 'Fix split import statements'
            }
        ];
        for (const fix of malformedImportFixes) {
            const originalCode = fixed;
            fixed = fixed.replace(fix.pattern, fix.replacement);
            if (originalCode !== fixed) {
                fixes.push(fix.description);
                this.logger.debug(`Applied malformed import fix: ${fix.description}`);
            }
        }
        // Fix object literal placeholders that should be actual code
        const objectLiteralFixes = [
            // Fix incomplete object literals and function calls
            {
                pattern: /\{\s*connectionString:\s*process\.env\.DATABASE_URL,?\s*\}/g,
                replacement: `{
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
}`,
                description: 'Fix database connection config'
            },
            {
                pattern: /\{\s*windowMs:\s*15\s*\*\s*60\s*\*\s*1000[^}]*\}/g,
                replacement: `{
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}`,
                description: 'Fix rate limiting config'
            }
        ];
        for (const fix of objectLiteralFixes) {
            const originalCode = fixed;
            fixed = fixed.replace(fix.pattern, fix.replacement);
            if (originalCode !== fixed) {
                fixes.push(fix.description);
                this.logger.debug(`Applied object literal fix: ${fix.description}`);
            }
        }
        return { code: fixed, fixes };
    }
    /**
     * Fix code structure issues like duplicate classes, variable redefinitions, etc.
     */
    fixCodeStructureIssues(code, fileName) {
        const fixes = [];
        let fixed = code;
        // Remove excessive empty lines at the beginning
        fixed = fixed.replace(/^(\s*\n){3,}/, '\n\n');
        if (fixed !== code) {
            fixes.push('Removed excessive empty lines at beginning');
        }
        // Fix duplicate class definitions
        const classMatches = [...fixed.matchAll(/export class (\w+)/g)];
        const classNames = classMatches.map(match => match[1]);
        const duplicateClasses = classNames.filter((name, index) => classNames.indexOf(name) !== index);
        if (duplicateClasses.length > 0) {
            for (const className of duplicateClasses) {
                // Find all occurrences of the duplicate class
                const classRegex = new RegExp(`export class ${className}[\\s\\S]*?(?=export class|$)`, 'g');
                const matches = [...fixed.matchAll(classRegex)];
                if (matches.length > 1) {
                    // Keep only the first occurrence, remove others
                    for (let i = 1; i < matches.length; i++) {
                        fixed = fixed.replace(matches[i][0], '');
                    }
                    fixes.push(`Removed duplicate class definition: ${className}`);
                }
            }
        }
        // Fix variable redefinitions (like const taskRoutes being defined multiple times)
        const variablePattern = /const\s+(\w+)\s*=/g;
        const variables = new Map();
        let match;
        while ((match = variablePattern.exec(fixed)) !== null) {
            const varName = match[1];
            variables.set(varName, (variables.get(varName) || 0) + 1);
        }
        // Fix redefined variables
        for (const [varName, count] of variables.entries()) {
            if (count > 1) {
                // Find all occurrences and rename subsequent ones
                const varRegex = new RegExp(`const\\s+${varName}\\s*=`, 'g');
                let occurrenceCount = 0;
                fixed = fixed.replace(varRegex, (match) => {
                    occurrenceCount++;
                    if (occurrenceCount > 1) {
                        return `const ${varName}${occurrenceCount} =`;
                    }
                    return match;
                });
                if (occurrenceCount > 1) {
                    fixes.push(`Fixed variable redefinition: ${varName}`);
                }
            }
        }
        // Fix import path extensions for TypeScript files
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const importPattern = /import\s+.*?\s+from\s+['"]([^'"]+)['"];?/g;
            fixed = fixed.replace(importPattern, (match, path) => {
                // Add .js extension to relative imports that don't have an extension
                if (path.startsWith('.') && !path.includes('.')) {
                    return match.replace(path, `${path}.js`);
                }
                return match;
            });
            fixes.push('Fixed import path extensions');
        }
        return { code: fixed, fixes };
    }
    /**
     * Fix type consistency issues between frontend and backend
     */
    fixTypeConsistencyIssues(code, fileName) {
        const fixes = [];
        let fixed = code;
        // Standardize ID types to string (more consistent across frontend/backend)
        if (fileName.includes('frontend') || fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
            // Frontend: ensure IDs are strings
            fixed = fixed.replace(/id:\s*number/g, 'id: string');
            if (fixed !== code) {
                fixes.push('Standardized ID types to string in frontend');
            }
        }
        else if (fileName.includes('backend') || fileName.endsWith('.ts')) {
            // Backend: ensure IDs are strings for consistency
            fixed = fixed.replace(/id:\s*number/g, 'id: string');
            if (fixed !== code) {
                fixes.push('Standardized ID types to string in backend');
            }
        }
        // Standardize status enums
        const statusEnumPattern = /status:\s*['"]?(pending|in-progress|completed|cancelled)['"]?/gi;
        fixed = fixed.replace(statusEnumPattern, (match, status) => {
            const standardStatus = status.toLowerCase().replace('-', '_');
            return `status: '${standardStatus}'`;
        });
        // Fix common type mismatches
        const typeFixes = [
            {
                pattern: /createdAt:\s*Date/g,
                replacement: 'createdAt: string',
                description: 'Standardized createdAt to string type'
            },
            {
                pattern: /updatedAt:\s*Date/g,
                replacement: 'updatedAt: string',
                description: 'Standardized updatedAt to string type'
            },
            {
                pattern: /\bdate:\s*Date/g,
                replacement: 'date: string',
                description: 'Standardized date fields to string type'
            }
        ];
        for (const fix of typeFixes) {
            const originalCode = fixed;
            fixed = fixed.replace(fix.pattern, fix.replacement);
            if (originalCode !== fixed) {
                fixes.push(fix.description);
            }
        }
        return { code: fixed, fixes };
    }
    /**
     * Fix common syntax issues
     */
    fixCommonSyntaxIssues(code, fileName) {
        let fixed = code;
        const fixes = [];
        // Fix broken SQL template literals
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const sqlFixes = this.fixSQLTemplateLiterals(fixed);
            fixed = sqlFixes.code;
            fixes.push(...sqlFixes.fixes);
            // Fix incomplete function calls
            const functionFixes = this.fixIncompleteFunctionCalls(fixed);
            fixed = functionFixes.code;
            fixes.push(...functionFixes.fixes);
            // Fix unterminated template literals
            const templateFixes = this.fixUnterminatedTemplateLiterals(fixed);
            fixed = templateFixes.code;
            fixes.push(...templateFixes.fixes);
        }
        // Fix excessive whitespace
        fixed = fixed.replace(/\n{3,}/g, '\n\n');
        fixed = fixed.split('\n').map(line => line.trimEnd()).join('\n');
        return { code: fixed, fixes };
    }
    /**
     * Fix broken SQL template literals
     */
    fixSQLTemplateLiterals(code) {
        const fixes = [];
        let fixed = code;
        // Pattern: const query = `;` followed by SQL
        const brokenSQLPattern = /const\s+(\w+)\s*=\s*`;?\s*\n(\s*(?:INSERT|SELECT|UPDATE|DELETE|CREATE|DROP|ALTER)[\s\S]*?)(?=\n\s*const|\n\s*\}|\n\s*return|\n\s*\/\/|$)/g;
        fixed = fixed.replace(brokenSQLPattern, (match, varName, sqlContent) => {
            fixes.push(`Fixed broken SQL template literal for ${varName}`);
            return `const ${varName} = \`\n${sqlContent.trim()}\n\`;`;
        });
        return { code: fixed, fixes };
    }
    /**
     * Fix incomplete function calls
     */
    fixIncompleteFunctionCalls(code) {
        const fixes = [];
        let fixed = code;
        // Pattern: function(; or method(;
        const incompleteCallPattern = /(\w+)\(\s*;/g;
        fixed = fixed.replace(incompleteCallPattern, (match, functionName) => {
            fixes.push(`Fixed incomplete function call: ${functionName}`);
            return `${functionName}()`;
        });
        return { code: fixed, fixes };
    }
    /**
     * Fix unterminated template literals
     */
    fixUnterminatedTemplateLiterals(code) {
        const fixes = [];
        let fixed = code;
        // Count backticks and fix if odd number
        const backtickCount = (fixed.match(/`/g) || []).length;
        if (backtickCount % 2 !== 0) {
            // Find the last incomplete template literal and close it
            const lines = fixed.split('\n');
            for (let i = lines.length - 1; i >= 0; i--) {
                if (lines[i].includes('`') && !lines[i].match(/`.*`/)) {
                    lines[i] += '`';
                    fixes.push('Fixed unterminated template literal');
                    break;
                }
            }
            fixed = lines.join('\n');
        }
        return { code: fixed, fixes };
    }
    /**
     * Validate and repair code
     */
    async validateAndRepair(code, fileName) {
        const result = {
            code,
            issues: [],
            warnings: [],
            fixes: []
        };
        // Validate template substitution
        const templateValidation = this.templateEngine.validateSubstitution(code);
        if (!templateValidation.isValid) {
            result.warnings.push(`Unsubstituted template variables: ${templateValidation.unsubstituted.join(', ')}`);
        }
        // Validate syntax for TypeScript files
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const syntaxValidation = await this.validateTypeScriptSyntax(code);
            result.issues.push(...syntaxValidation.errors);
            result.warnings.push(...syntaxValidation.warnings);
        }
        return result;
    }
    /**
     * Validate TypeScript syntax
     */
    async validateTypeScriptSyntax(code) {
        const errors = [];
        const warnings = [];
        try {
            const ts = require('typescript');
            const result = ts.transpileModule(code, {
                compilerOptions: {
                    target: ts.ScriptTarget.ES2020,
                    module: ts.ModuleKind.CommonJS,
                    strict: false, // Less strict for generated code
                    skipLibCheck: true
                },
                reportDiagnostics: true
            });
            if (result.diagnostics && result.diagnostics.length > 0) {
                for (const diagnostic of result.diagnostics) {
                    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
                    if (diagnostic.category === ts.DiagnosticCategory.Error) {
                        errors.push(message);
                    }
                    else {
                        warnings.push(message);
                    }
                }
            }
        }
        catch (error) {
            // TypeScript not available, skip validation
            warnings.push('TypeScript validation skipped (compiler not available)');
        }
        return { errors, warnings };
    }
    /**
     * Perform final validation
     */
    async performFinalValidation(code, fileName) {
        const errors = [];
        const warnings = [];
        // Check for remaining issues
        if (code.includes('```')) {
            errors.push('Markdown artifacts still present');
        }
        if (code.includes('undefined Application')) {
            errors.push('Unsubstituted template variables found');
        }
        // Basic syntax checks
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const openBraces = (code.match(/\{/g) || []).length;
            const closeBraces = (code.match(/\}/g) || []).length;
            if (openBraces !== closeBraces) {
                errors.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
            }
        }
        const score = Math.max(0, 100 - (errors.length * 20) - (warnings.length * 5));
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            score
        };
    }
}
exports.PostProcessingPipeline = PostProcessingPipeline;
exports.default = PostProcessingPipeline;
//# sourceMappingURL=PostProcessingPipeline.js.map