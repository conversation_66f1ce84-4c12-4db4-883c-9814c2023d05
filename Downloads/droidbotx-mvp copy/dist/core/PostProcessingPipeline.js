"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostProcessingPipeline = void 0;
const Logger_1 = require("./Logger");
const TemplateEngine_1 = require("./TemplateEngine");
/**
 * Comprehensive post-processing pipeline for generated code
 */
class PostProcessingPipeline {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.templateEngine = TemplateEngine_1.TemplateEngine.getInstance();
    }
    /**
     * Process generated code through comprehensive pipeline
     */
    async processGeneratedCode(code, fileName, context) {
        const result = {
            success: true,
            processedCode: code,
            issues: [],
            warnings: [],
            appliedFixes: []
        };
        try {
            this.logger.debug('Starting post-processing pipeline', { fileName });
            // Phase 1: Remove markdown artifacts
            result.processedCode = this.removeMarkdownArtifacts(result.processedCode);
            result.appliedFixes.push('Markdown artifact removal');
            // Phase 2: Template variable substitution
            result.processedCode = this.templateEngine.substituteVariables(result.processedCode, context);
            result.appliedFixes.push('Template variable substitution');
            // Phase 3: Fix common syntax issues
            const syntaxResult = this.fixCommonSyntaxIssues(result.processedCode, fileName);
            result.processedCode = syntaxResult.code;
            result.appliedFixes.push(...syntaxResult.fixes);
            // Phase 4: Validate and repair
            const validationResult = await this.validateAndRepair(result.processedCode, fileName);
            result.processedCode = validationResult.code;
            result.issues.push(...validationResult.issues);
            result.warnings.push(...validationResult.warnings);
            result.appliedFixes.push(...validationResult.fixes);
            // Phase 5: Final validation
            const finalValidation = await this.performFinalValidation(result.processedCode, fileName);
            result.issues.push(...finalValidation.errors);
            result.warnings.push(...finalValidation.warnings);
            result.success = result.issues.length === 0;
            this.logger.debug('Post-processing pipeline completed', {
                fileName,
                success: result.success,
                issuesCount: result.issues.length,
                warningsCount: result.warnings.length,
                fixesApplied: result.appliedFixes.length
            });
        }
        catch (error) {
            result.success = false;
            result.issues.push(`Post-processing failed: ${error instanceof Error ? error.message : String(error)}`);
            this.logger.error('Post-processing pipeline failed', { fileName, error });
        }
        return result;
    }
    /**
     * Remove markdown artifacts with comprehensive patterns
     */
    removeMarkdownArtifacts(code) {
        let cleaned = code;
        // Enhanced markdown removal patterns
        const markdownPatterns = [
            // Language-specific code blocks
            /```(?:typescript|ts|javascript|js|tsx|jsx|sql|yaml|yml|json|dockerfile|bash|sh|html|css)\s*\n?/g,
            // Generic code blocks
            /```\w*\s*\n?/g,
            /```\s*$/gm,
            /^```\s*$/gm,
            // Inline code markers that shouldn't be there
            /`{3,}/g
        ];
        for (const pattern of markdownPatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        // Remove common AI response artifacts
        const artifactPatterns = [
            /^Here's the.*?:\s*/i,
            /^Here is the.*?:\s*/i,
            /^I'll create.*?:\s*/i,
            /^Let me create.*?:\s*/i,
            /^This is the.*?:\s*/i,
            /^The following.*?:\s*/i,
            /^Below is the.*?:\s*/i
        ];
        for (const pattern of artifactPatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        return cleaned.trim();
    }
    /**
     * Fix common syntax issues
     */
    fixCommonSyntaxIssues(code, fileName) {
        let fixed = code;
        const fixes = [];
        // Fix broken SQL template literals
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const sqlFixes = this.fixSQLTemplateLiterals(fixed);
            fixed = sqlFixes.code;
            fixes.push(...sqlFixes.fixes);
            // Fix incomplete function calls
            const functionFixes = this.fixIncompleteFunctionCalls(fixed);
            fixed = functionFixes.code;
            fixes.push(...functionFixes.fixes);
            // Fix unterminated template literals
            const templateFixes = this.fixUnterminatedTemplateLiterals(fixed);
            fixed = templateFixes.code;
            fixes.push(...templateFixes.fixes);
        }
        // Fix excessive whitespace
        fixed = fixed.replace(/\n{3,}/g, '\n\n');
        fixed = fixed.split('\n').map(line => line.trimEnd()).join('\n');
        return { code: fixed, fixes };
    }
    /**
     * Fix broken SQL template literals
     */
    fixSQLTemplateLiterals(code) {
        const fixes = [];
        let fixed = code;
        // Pattern: const query = `;` followed by SQL
        const brokenSQLPattern = /const\s+(\w+)\s*=\s*`;?\s*\n(\s*(?:INSERT|SELECT|UPDATE|DELETE|CREATE|DROP|ALTER)[\s\S]*?)(?=\n\s*const|\n\s*\}|\n\s*return|\n\s*\/\/|$)/g;
        fixed = fixed.replace(brokenSQLPattern, (match, varName, sqlContent) => {
            fixes.push(`Fixed broken SQL template literal for ${varName}`);
            return `const ${varName} = \`\n${sqlContent.trim()}\n\`;`;
        });
        return { code: fixed, fixes };
    }
    /**
     * Fix incomplete function calls
     */
    fixIncompleteFunctionCalls(code) {
        const fixes = [];
        let fixed = code;
        // Pattern: function(; or method(;
        const incompleteCallPattern = /(\w+)\(\s*;/g;
        fixed = fixed.replace(incompleteCallPattern, (match, functionName) => {
            fixes.push(`Fixed incomplete function call: ${functionName}`);
            return `${functionName}()`;
        });
        return { code: fixed, fixes };
    }
    /**
     * Fix unterminated template literals
     */
    fixUnterminatedTemplateLiterals(code) {
        const fixes = [];
        let fixed = code;
        // Count backticks and fix if odd number
        const backtickCount = (fixed.match(/`/g) || []).length;
        if (backtickCount % 2 !== 0) {
            // Find the last incomplete template literal and close it
            const lines = fixed.split('\n');
            for (let i = lines.length - 1; i >= 0; i--) {
                if (lines[i].includes('`') && !lines[i].match(/`.*`/)) {
                    lines[i] += '`';
                    fixes.push('Fixed unterminated template literal');
                    break;
                }
            }
            fixed = lines.join('\n');
        }
        return { code: fixed, fixes };
    }
    /**
     * Validate and repair code
     */
    async validateAndRepair(code, fileName) {
        const result = {
            code,
            issues: [],
            warnings: [],
            fixes: []
        };
        // Validate template substitution
        const templateValidation = this.templateEngine.validateSubstitution(code);
        if (!templateValidation.isValid) {
            result.warnings.push(`Unsubstituted template variables: ${templateValidation.unsubstituted.join(', ')}`);
        }
        // Validate syntax for TypeScript files
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const syntaxValidation = await this.validateTypeScriptSyntax(code);
            result.issues.push(...syntaxValidation.errors);
            result.warnings.push(...syntaxValidation.warnings);
        }
        return result;
    }
    /**
     * Validate TypeScript syntax
     */
    async validateTypeScriptSyntax(code) {
        const errors = [];
        const warnings = [];
        try {
            const ts = require('typescript');
            const result = ts.transpileModule(code, {
                compilerOptions: {
                    target: ts.ScriptTarget.ES2020,
                    module: ts.ModuleKind.CommonJS,
                    strict: false, // Less strict for generated code
                    skipLibCheck: true
                },
                reportDiagnostics: true
            });
            if (result.diagnostics && result.diagnostics.length > 0) {
                for (const diagnostic of result.diagnostics) {
                    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
                    if (diagnostic.category === ts.DiagnosticCategory.Error) {
                        errors.push(message);
                    }
                    else {
                        warnings.push(message);
                    }
                }
            }
        }
        catch (error) {
            // TypeScript not available, skip validation
            warnings.push('TypeScript validation skipped (compiler not available)');
        }
        return { errors, warnings };
    }
    /**
     * Perform final validation
     */
    async performFinalValidation(code, fileName) {
        const errors = [];
        const warnings = [];
        // Check for remaining issues
        if (code.includes('```')) {
            errors.push('Markdown artifacts still present');
        }
        if (code.includes('undefined Application')) {
            errors.push('Unsubstituted template variables found');
        }
        // Basic syntax checks
        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {
            const openBraces = (code.match(/\{/g) || []).length;
            const closeBraces = (code.match(/\}/g) || []).length;
            if (openBraces !== closeBraces) {
                errors.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
            }
        }
        const score = Math.max(0, 100 - (errors.length * 20) - (warnings.length * 5));
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            score
        };
    }
}
exports.PostProcessingPipeline = PostProcessingPipeline;
exports.default = PostProcessingPipeline;
//# sourceMappingURL=PostProcessingPipeline.js.map