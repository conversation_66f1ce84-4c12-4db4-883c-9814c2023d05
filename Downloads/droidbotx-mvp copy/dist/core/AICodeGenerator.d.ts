import { BaseAgent } from './BaseAgent';
import { BusinessDomainAnalysis, BusinessEntity } from './SemanticAnalyzer';
import { ProjectContext } from './TemplateEngine';
export interface CodeGenerationRequest {
    type: 'frontend-component' | 'backend-route' | 'database-schema' | 'service-class' | 'model-class' | 'configuration';
    entity?: BusinessEntity;
    context: CodeGenerationContext;
    specifications: CodeSpecifications;
}
export interface CodeGenerationContext {
    projectName: string;
    businessDomain: BusinessDomainAnalysis;
    targetFramework: string;
    architecturalPatterns: string[];
    existingEntities: string[];
    relatedEntities: BusinessEntity[];
}
export interface CodeSpecifications {
    fileName: string;
    purpose: string;
    requirements: string[];
    dependencies: string[];
    businessRules: string[];
    validationRules: string[];
    securityRequirements: string[];
}
export interface GeneratedCode {
    fileName: string;
    content: string;
    dependencies: string[];
    imports: string[];
    exports: string[];
    tests?: string;
}
export declare class AICodeGenerator extends BaseAgent {
    private templateEngine;
    private postProcessingPipeline;
    private currentProjectContext;
    constructor();
    /**
     * Set project context for template substitution
     */
    setProjectContext(context: ProjectContext): void;
    canHandle(task: any): boolean;
    execute(task: any): Promise<any>;
    /**
     * Generate a React component based on business entity
     */
    generateReactComponent(request: CodeGenerationRequest): Promise<GeneratedCode>;
    /**
     * Generate Express.js route based on business entity
     */
    generateExpressRoute(request: CodeGenerationRequest): Promise<GeneratedCode>;
    /**
     * Generate database schema based on business entities
     */
    generateDatabaseSchema(entities: BusinessEntity[], context: CodeGenerationContext): Promise<GeneratedCode>;
    /**
     * Generate service class based on business entity
     */
    generateServiceClass(request: CodeGenerationRequest): Promise<GeneratedCode>;
    private buildReactComponentPrompt;
    private buildExpressRoutePrompt;
    private buildDatabaseSchemaPrompt;
    private buildServiceClassPrompt;
    private parseCodeResponse;
    private extractImports;
    private extractExports;
    private extractDependencies;
    /**
     * Clean common AI response artifacts from generated code
     */
    private cleanCodeArtifacts;
    /**
     * Substitute template variables with actual project context using TemplateEngine
     */
    private substituteTemplateVariables;
    /**
     * Validate extracted code for common issues
     */
    private validateExtractedCode;
    /**
     * Get current project context for template substitution
     */
    private getProjectContext;
    /**
     * Extract component name from file name
     */
    private extractComponentName;
    /**
     * Extract service name from file name
     */
    private extractServiceName;
}
//# sourceMappingURL=AICodeGenerator.d.ts.map