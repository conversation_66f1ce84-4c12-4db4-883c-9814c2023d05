/**
 * Advanced Quality Gates System
 * Implements comprehensive validation for code quality, type safety, and integration correctness
 * Ensures production-ready code before proceeding to next phase
 */
export interface QualityMetric {
    name: string;
    value: number;
    threshold: number;
    weight: number;
    status: 'pass' | 'fail' | 'warning';
    details?: string;
}
export interface QualityGateRule {
    id: string;
    name: string;
    description: string;
    category: 'code_quality' | 'type_safety' | 'security' | 'performance' | 'integration';
    severity: 'critical' | 'high' | 'medium' | 'low';
    validator: (context: any) => Promise<QualityMetric>;
}
export interface QualityGateResult {
    gateId: string;
    gateName: string;
    passed: boolean;
    score: number;
    maxScore: number;
    metrics: QualityMetric[];
    violations: Array<{
        rule: string;
        severity: string;
        message: string;
        suggestion?: string;
    }>;
    recommendations: string[];
    executionTime: number;
}
export interface ComprehensiveQualityReport {
    overallPassed: boolean;
    overallScore: number;
    gateResults: QualityGateResult[];
    summary: {
        totalGates: number;
        passedGates: number;
        failedGates: number;
        criticalViolations: number;
        highViolations: number;
        mediumViolations: number;
        lowViolations: number;
    };
    recommendations: string[];
    nextSteps: string[];
}
export declare class AdvancedQualityGatesSystem {
    private logger;
    private llmProvider;
    private qualityGates;
    private globalThresholds;
    constructor();
    /**
     * Execute comprehensive quality validation
     */
    executeQualityValidation(context: any, phase: string): Promise<ComprehensiveQualityReport>;
    /**
     * Execute a single quality gate
     */
    private executeQualityGate;
    /**
     * Generate comprehensive quality report
     */
    private generateComprehensiveReport;
    /**
     * Generate AI-powered suggestion for improvement
     */
    private generateSuggestion;
    /**
     * Generate overall recommendations
     */
    private generateOverallRecommendations;
    /**
     * Generate next steps based on validation results
     */
    private generateNextSteps;
    /**
     * Get category from gate ID
     */
    private getCategoryFromGateId;
    /**
     * Initialize quality gates for different phases
     */
    private initializeQualityGates;
    /**
     * Initialize quality thresholds - more balanced approach
     */
    private initializeThresholds;
    private validateDatabaseSchema;
    private validateDatabaseTypes;
    private validateAPIContracts;
    private validateBusinessLogicQuality;
    private validateCodeCompilation;
    private validateTypeSafety;
    private validateSecurity;
}
//# sourceMappingURL=AdvancedQualityGatesSystem.d.ts.map