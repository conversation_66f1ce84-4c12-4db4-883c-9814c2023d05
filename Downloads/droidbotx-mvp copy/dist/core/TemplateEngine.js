"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateEngine = void 0;
const Logger_1 = require("./Logger");
/**
 * Centralized template engine for consistent variable substitution across DroidBotX
 */
class TemplateEngine {
    constructor() {
        this.templates = new Map();
        this.globalVariables = [];
        this.logger = Logger_1.Logger.getInstance();
        this.initializeGlobalVariables();
    }
    /**
     * Initialize global template variables
     */
    initializeGlobalVariables() {
        this.globalVariables = [
            // Project name variations
            {
                pattern: /undefined Application/g,
                replacement: (context) => context.projectName || 'Application',
                description: 'Replace undefined Application with project name'
            },
            {
                pattern: /undefined management solution/g,
                replacement: (context) => context.description || `${context.domain} management solution`,
                description: 'Replace undefined management solution with project description'
            },
            {
                pattern: /\{projectName\}/g,
                replacement: (context) => context.projectName || 'Project',
                description: 'Replace {projectName} template variable'
            },
            {
                pattern: /\{projectDescription\}/g,
                replacement: (context) => context.description || 'Application',
                description: 'Replace {projectDescription} template variable'
            },
            {
                pattern: /\{domain\}/g,
                replacement: (context) => context.domain || 'business',
                description: 'Replace {domain} template variable'
            },
            {
                pattern: /\{businessDomain\}/g,
                replacement: (context) => context.businessDomain || context.domain || 'business',
                description: 'Replace {businessDomain} template variable'
            },
            // Welcome messages and titles
            {
                pattern: /Welcome to undefined/g,
                replacement: (context) => `Welcome to ${context.projectName || 'Application'}`,
                description: 'Fix welcome message with undefined'
            },
            {
                pattern: /Your comprehensive undefined/g,
                replacement: (context) => `Your comprehensive ${context.description || 'application'}`,
                description: 'Fix comprehensive description with undefined'
            },
            // Database and API context
            {
                pattern: /\{dbName\}/g,
                replacement: (context) => context.dbName || `${context.projectName?.toLowerCase().replace(/\s+/g, '_')}_db` || 'app_db',
                description: 'Replace {dbName} template variable'
            },
            {
                pattern: /\{apiPrefix\}/g,
                replacement: (context) => context.apiPrefix || '/api/v1',
                description: 'Replace {apiPrefix} template variable'
            },
            // Entity-related variables
            {
                pattern: /\{entityNames\}/g,
                replacement: (context) => context.entities?.map(e => e.name).join(', ') || 'entities',
                description: 'Replace {entityNames} with comma-separated entity names'
            },
            {
                pattern: /\{entityCount\}/g,
                replacement: (context) => String(context.entities?.length || 0),
                description: 'Replace {entityCount} with number of entities'
            }
        ];
    }
    /**
     * Register a template for reuse
     */
    registerTemplate(name, template) {
        this.templates.set(name, template);
        this.logger.debug(`Template registered: ${name}`);
    }
    /**
     * Render a registered template with context
     */
    renderTemplate(templateName, context) {
        const template = this.templates.get(templateName);
        if (!template) {
            throw new Error(`Template ${templateName} not found`);
        }
        return this.substituteVariables(template, context);
    }
    /**
     * Substitute template variables in any string content
     */
    substituteVariables(content, context) {
        let substituted = content;
        let substitutionsApplied = 0;
        // Apply global variable substitutions
        for (const variable of this.globalVariables) {
            const originalContent = substituted;
            if (typeof variable.replacement === 'string') {
                substituted = substituted.replace(variable.pattern, variable.replacement);
            }
            else {
                substituted = substituted.replace(variable.pattern, () => variable.replacement(context));
            }
            if (originalContent !== substituted) {
                substitutionsApplied++;
                this.logger.debug(`Applied substitution: ${variable.description}`);
            }
        }
        // Apply custom Handlebars-style variables
        substituted = this.applyHandlebarsVariables(substituted, context);
        this.logger.debug(`Template substitution completed`, {
            substitutionsApplied,
            originalLength: content.length,
            finalLength: substituted.length
        });
        return substituted;
    }
    /**
     * Apply Handlebars-style template variables {{variable}}
     */
    applyHandlebarsVariables(content, context) {
        return content.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
            const value = this.getNestedProperty(context, path);
            return value !== undefined ? String(value) : match;
        });
    }
    /**
     * Get nested property from context object
     */
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    /**
     * Validate that all template variables have been substituted
     */
    validateSubstitution(content) {
        const unsubstituted = [];
        // Check for remaining template variables
        const patterns = [
            /undefined Application/g,
            /undefined management/g,
            /\{[^}]+\}/g,
            /\{\{[^}]+\}\}/g
        ];
        for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
                unsubstituted.push(...matches);
            }
        }
        return {
            isValid: unsubstituted.length === 0,
            unsubstituted: [...new Set(unsubstituted)] // Remove duplicates
        };
    }
    /**
     * Create project context from technical specification
     */
    createProjectContext(technicalSpec, semanticAnalysis) {
        return {
            projectName: technicalSpec.projectName || 'Application',
            description: technicalSpec.description || `${semanticAnalysis?.domain || 'business'} management application`,
            domain: semanticAnalysis?.domain || technicalSpec.businessDomain?.name || 'business',
            businessDomain: technicalSpec.businessDomain?.name,
            dbName: `${(technicalSpec.projectName || 'app').toLowerCase().replace(/\s+/g, '_')}_db`,
            apiPrefix: '/api/v1',
            entities: semanticAnalysis?.entities || [],
            userRoles: semanticAnalysis?.userRoles || ['admin', 'user'],
            features: technicalSpec.features || []
        };
    }
    static getInstance() {
        if (!TemplateEngine.instance) {
            TemplateEngine.instance = new TemplateEngine();
        }
        return TemplateEngine.instance;
    }
}
exports.TemplateEngine = TemplateEngine;
exports.default = TemplateEngine;
//# sourceMappingURL=TemplateEngine.js.map