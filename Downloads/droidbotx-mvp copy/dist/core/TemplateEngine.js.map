{"version": 3, "file": "TemplateEngine.js", "sourceRoot": "", "sources": ["../../src/core/TemplateEngine.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAwBlC;;GAEG;AACH,MAAa,cAAc;IAKzB;QAHQ,cAAS,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC3C,oBAAe,GAAuB,EAAE,CAAC;QAG/C,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,IAAI,CAAC,eAAe,GAAG;YACrB,0BAA0B;YAC1B;gBACE,OAAO,EAAE,wBAAwB;gBACjC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI,aAAa;gBAC9D,WAAW,EAAE,iDAAiD;aAC/D;YACD;gBACE,OAAO,EAAE,gCAAgC;gBACzC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI,GAAG,OAAO,CAAC,MAAM,sBAAsB;gBACxF,WAAW,EAAE,gEAAgE;aAC9E;YACD;gBACE,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI,SAAS;gBAC1D,WAAW,EAAE,yCAAyC;aACvD;YACD;gBACE,OAAO,EAAE,yBAAyB;gBAClC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI,aAAa;gBAC9D,WAAW,EAAE,gDAAgD;aAC9D;YACD;gBACE,OAAO,EAAE,aAAa;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,UAAU;gBACtD,WAAW,EAAE,oCAAoC;aAClD;YACD;gBACE,OAAO,EAAE,qBAAqB;gBAC9B,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,IAAI,UAAU;gBAChF,WAAW,EAAE,4CAA4C;aAC1D;YAED,8BAA8B;YAC9B;gBACE,OAAO,EAAE,uBAAuB;gBAChC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,cAAc,OAAO,CAAC,WAAW,IAAI,aAAa,EAAE;gBAC9E,WAAW,EAAE,oCAAoC;aAClD;YACD;gBACE,OAAO,EAAE,+BAA+B;gBACxC,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,sBAAsB,OAAO,CAAC,WAAW,IAAI,aAAa,EAAE;gBACtF,WAAW,EAAE,8CAA8C;aAC5D;YAED,2BAA2B;YAC3B;gBACE,OAAO,EAAE,aAAa;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,IAAI,QAAQ;gBACvH,WAAW,EAAE,oCAAoC;aAClD;YACD;gBACE,OAAO,EAAE,gBAAgB;gBACzB,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,IAAI,SAAS;gBACxD,WAAW,EAAE,uCAAuC;aACrD;YAED,2BAA2B;YAC3B;gBACE,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU;gBACrF,WAAW,EAAE,yDAAyD;aACvE;YACD;gBACE,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;gBAC/D,WAAW,EAAE,+CAA+C;aAC7D;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,IAAY,EAAE,QAAgB;QACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,YAAoB,EAAE,OAAuB;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,OAAe,EAAE,OAAuB;QACjE,IAAI,WAAW,GAAG,OAAO,CAAC;QAC1B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,sCAAsC;QACtC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,MAAM,eAAe,GAAG,WAAW,CAAC;YAEpC,IAAI,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;gBAC7C,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,CAAE,QAAQ,CAAC,WAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;YACzG,CAAC;YAED,IAAI,eAAe,KAAK,WAAW,EAAE,CAAC;gBACpC,oBAAoB,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAElE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YACnD,oBAAoB;YACpB,cAAc,EAAE,OAAO,CAAC,MAAM;YAC9B,WAAW,EAAE,WAAW,CAAC,MAAM;SAChC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAAe,EAAE,OAAuB;QACvE,OAAO,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACjE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACpD,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,GAAQ,EAAE,IAAY;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAC7C,OAAO,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAe;QACzC,MAAM,aAAa,GAAa,EAAE,CAAC;QAEnC,yCAAyC;QACzC,MAAM,QAAQ,GAAG;YACf,wBAAwB;YACxB,uBAAuB;YACvB,YAAY;YACZ,gBAAgB;SACjB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,OAAO,EAAE,CAAC;gBACZ,aAAa,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,aAAa,CAAC,MAAM,KAAK,CAAC;YACnC,aAAa,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,oBAAoB;SAChE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,aAAkB,EAAE,gBAAsB;QACpE,OAAO;YACL,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,aAAa;YACvD,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,GAAG,gBAAgB,EAAE,MAAM,IAAI,UAAU,yBAAyB;YAC5G,MAAM,EAAE,gBAAgB,EAAE,MAAM,IAAI,aAAa,CAAC,cAAc,EAAE,IAAI,IAAI,UAAU;YACpF,cAAc,EAAE,aAAa,CAAC,cAAc,EAAE,IAAI;YAClD,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK;YACvF,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,IAAI,EAAE;YAC1C,SAAS,EAAE,gBAAgB,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;YAC3D,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,EAAE;SACvC,CAAC;IACJ,CAAC;IAOM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;CACF;AAtND,wCAsNC;AAED,kBAAe,cAAc,CAAC"}