export interface ProjectContext {
    projectName: string;
    description: string;
    domain: string;
    businessDomain?: string;
    dbName?: string;
    apiPrefix?: string;
    entities?: Array<{
        name: string;
        description?: string;
        fields?: Array<{
            name: string;
            type: string;
            required?: boolean;
        }>;
    }>;
    userRoles?: string[];
    features?: string[];
}
export interface TemplateVariable {
    pattern: RegExp;
    replacement: string | ((context: ProjectContext) => string);
    description: string;
}
/**
 * Centralized template engine for consistent variable substitution across DroidBotX
 */
export declare class TemplateEngine {
    private logger;
    private templates;
    private globalVariables;
    constructor();
    /**
     * Initialize global template variables
     */
    private initializeGlobalVariables;
    /**
     * Register a template for reuse
     */
    registerTemplate(name: string, template: string): void;
    /**
     * Render a registered template with context
     */
    renderTemplate(templateName: string, context: ProjectContext): string;
    /**
     * Substitute template variables in any string content
     */
    substituteVariables(content: string, context: ProjectContext): string;
    /**
     * Apply Handlebars-style template variables {{variable}}
     */
    private applyHandlebarsVariables;
    /**
     * Get nested property from context object
     */
    private getNestedProperty;
    /**
     * Validate that all template variables have been substituted
     */
    validateSubstitution(content: string): {
        isValid: boolean;
        unsubstituted: string[];
    };
    /**
     * Create project context from technical specification
     */
    createProjectContext(technicalSpec: any, semanticAnalysis?: any): ProjectContext;
    /**
     * Get template engine instance (singleton pattern)
     */
    private static instance;
    static getInstance(): TemplateEngine;
}
export default TemplateEngine;
//# sourceMappingURL=TemplateEngine.d.ts.map