{"root": ["./src/index.ts", "./src/agents/businesslogicagent.ts", "./src/agents/codingagent.ts", "./src/agents/databaseagent.ts", "./src/agents/debuggeragent.ts", "./src/agents/deploymentagent.ts", "./src/agents/devopsagent.ts", "./src/agents/planningagent.ts", "./src/agents/securityagent.ts", "./src/agents/testingagent.ts", "./src/agents/uiuxagent.ts", "./src/agents/index.ts", "./src/agents/__tests__/codingagent.test.ts", "./src/agents/__tests__/deploymentagent.test.ts", "./src/agents/__tests__/planningagent.test.ts", "./src/cli/app.ts", "./src/cli/__tests__/app.test.ts", "./src/coordination/businesslogicintegrationvalidator.ts", "./src/coordination/coordinatedfixingstrategy.ts", "./src/coordination/coordinationlockmanager.ts", "./src/coordination/feedbackmanager.ts", "./src/core/aicodegenerationengine.ts", "./src/core/aicodegenerator.ts", "./src/core/advancedimportmanager.ts", "./src/core/advancedqualitygatessystem.ts", "./src/core/baseagent.ts", "./src/core/comprehensivetestingframework.ts", "./src/core/configmanager.ts", "./src/core/contractfirstapigenerator.ts", "./src/core/crosslayerintegrationframework.ts", "./src/core/databasecodesynchronizer.ts", "./src/core/databaseschemaintegration.ts", "./src/core/enhancedworkfloworchestrator.ts", "./src/core/llmprovidersystem.ts", "./src/core/logger.ts", "./src/core/monitoringobservabilitysystem.ts", "./src/core/optimizedfilemanager.ts", "./src/core/parallelllmmanager.ts", "./src/core/performanceoptimization.ts", "./src/core/performanceoptimizationsystem.ts", "./src/core/postprocessingpipeline.ts", "./src/core/productiondeploymentinfrastructure.ts", "./src/core/qualitygatesframework.ts", "./src/core/securityhardeningsystem.ts", "./src/core/semanticanalyzer.ts", "./src/core/servicestandardization.ts", "./src/core/templateengine.ts", "./src/core/testqualityframework.ts", "./src/core/toolmanager.ts", "./src/core/index.ts", "./src/generators/applicationflowgenerator.ts", "./src/generators/databaseschemagenerator.ts", "./src/generators/domainapigenerator.ts", "./src/infrastructure/databasemanager.ts", "./src/infrastructure/databasemigrationmanager.ts", "./src/infrastructure/parallelprocessingmanager.ts", "./src/infrastructure/performancemanager.ts", "./src/intelligence/errorrecoveryworkflow.ts", "./src/intelligence/selfhealingmanager.ts", "./src/orchestration/contractfirstcoordination.ts", "./src/orchestration/dependencyvalidator.ts", "./src/orchestration/enhancedsequentialorchestrator.ts", "./src/orchestration/intelligentcachemanager.ts", "./src/orchestration/intelligenterrorrecovery.ts", "./src/orchestration/orchestrator.ts", "./src/orchestration/sharedstatemanager.ts", "./src/orchestration/streamingcodegenerator.ts", "./src/orchestration/index.ts", "./src/orchestration/stubs.ts", "./src/quality/advancedqualityanalyzer.ts", "./src/quality/advancedqualitygateframework.ts", "./src/quality/compilationqualitygate.ts", "./src/quality/comprehensivecrosslayervalidator.ts", "./src/quality/enhancedqualitygates.ts", "./src/quality/proactivequalitysystem.ts", "./src/test-utils/mockhelpers.ts", "./src/test-utils/testapp.ts", "./src/types/generatedcode.ts", "./src/utils/importexportmanager.ts", "./src/validation/precompilationvalidator.ts", "./src/validation/servicepatternstandardizer.ts"], "errors": true, "version": "5.9.2"}