{"name": "droidbotx-mvp", "version": "1.0.0", "description": "DroidBotX MVP - A simplified multi-agent system for generating production-ready full-stack applications", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"droidbotx": "dist/cli/app.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "cli": "node dist/cli/app.js", "cli:dev": "ts-node src/cli/app.ts", "test": "jest", "test:watch": "jest --watch", "test:e2e": "./e2e/e2e.sh", "test:all": "npm test && npm run test:e2e", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "agents", "full-stack", "code-generation", "typescript"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.5", "dotenv": "^16.4.5", "pg": "^8.16.3"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^22.5.1", "@types/pg": "^8.15.5", "eslint": "^9.9.1", "jest": "^29.7.0", "prettier": "^3.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "globals": {"ts-jest": {"useESM": false}}}}