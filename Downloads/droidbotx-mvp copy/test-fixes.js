#!/usr/bin/env node

/**
 * Test script to validate our post-processing fixes
 * This will apply our fixes to the existing generated code to see if they work
 */

const fs = require('fs');
const path = require('path');

// Import our post-processing pipeline
const { PostProcessingPipeline } = require('./dist/core/PostProcessingPipeline.js');
const { Logger } = require('./dist/core/Logger.js');

async function testFixes() {
  console.log('🔧 Testing Post-Processing Fixes...\n');

  const logger = new Logger('test-fixes');
  const pipeline = new PostProcessingPipeline(logger);

  // Test files from the generated project
  const testFiles = [
    'generated-projects/taskflow-pro/backend/src/models/Task.ts',
    'generated-projects/taskflow-pro/backend/src/routes/taskRoutes.ts',
    'generated-projects/taskflow-pro/backend/src/services/TaskService.ts',
    'generated-projects/taskflow-pro/frontend/src/components/TaskList.tsx',
    'generated-projects/taskflow-pro/frontend/src/components/UserList.tsx'
  ];

  for (const filePath of testFiles) {
    const fullPath = path.join(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      continue;
    }

    console.log(`\n📁 Processing: ${filePath}`);
    console.log('=' .repeat(60));

    try {
      const originalCode = fs.readFileSync(fullPath, 'utf8');
      console.log(`📊 Original file size: ${originalCode.length} characters`);

      // Apply our post-processing pipeline
      // Create a minimal context for testing
      const context = {
        projectName: 'test-project',
        businessDomain: { domain: 'test' },
        requirements: [],
        entities: [],
        apiEndpoints: [],
        databaseSchema: { tables: [] }
      };

      const result = await pipeline.processGeneratedCode(originalCode, path.basename(filePath), context);

      console.log(`📊 Processed file size: ${result.processedCode.length} characters`);
      console.log(`🔧 Applied fixes: ${result.appliedFixes.length}`);
      
      if (result.appliedFixes.length > 0) {
        console.log('✅ Fixes applied:');
        result.appliedFixes.forEach((fix, index) => {
          console.log(`   ${index + 1}. ${fix}`);
        });

        // Save the fixed version for comparison
        const fixedPath = fullPath.replace(/\.(ts|tsx)$/, '.fixed.$1');
        fs.writeFileSync(fixedPath, result.processedCode);
        console.log(`💾 Fixed version saved to: ${fixedPath}`);
      } else {
        console.log('ℹ️  No fixes needed');
      }

      // Check for remaining issues
      const remainingIssues = [];
      
      // Check for markdown artifacts
      if (result.processedCode.includes('```')) {
        remainingIssues.push('Markdown artifacts still present');
      }

      // Check for missing React imports
      if (filePath.endsWith('.tsx') && result.processedCode.includes('useState') &&
          !result.processedCode.includes('import { useState') &&
          !result.processedCode.includes('import React, { useState')) {
        remainingIssues.push('Missing React useState import');
      }

      // Check for actual template placeholders (not legitimate object literals)
      const templateIssues = [];

      // Check for standalone template placeholders (not in imports or object literals)
      if (/^import\s+\{\s*Router\s*\}\s*$/m.test(result.processedCode) ||
          /^import\s+\{\s*Pool\s*\}\s*$/m.test(result.processedCode)) {
        templateIssues.push('Template placeholders still present');
      }

      // Check for malformed imports
      if (result.processedCode.includes('import import')) {
        templateIssues.push('Malformed double imports');
      }
      if (result.processedCode.includes('from \'express\'; from')) {
        templateIssues.push('Malformed import statements');
      }

      if (templateIssues.length > 0) {
        remainingIssues.push(...templateIssues);
      }

      // Check for duplicate class definitions
      const classMatches = result.processedCode.match(/export class \w+/g);
      if (classMatches && classMatches.length > 1) {
        const classNames = classMatches.map(match => match.split(' ')[2]);
        const duplicates = classNames.filter((name, index) => classNames.indexOf(name) !== index);
        if (duplicates.length > 0) {
          remainingIssues.push(`Duplicate classes: ${duplicates.join(', ')}`);
        }
      }

      if (remainingIssues.length > 0) {
        console.log('⚠️  Remaining issues:');
        remainingIssues.forEach((issue, index) => {
          console.log(`   ${index + 1}. ${issue}`);
        });
      } else {
        console.log('✅ No remaining issues detected');
      }

    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  console.log('\n🎯 Test Summary');
  console.log('=' .repeat(60));
  console.log('Post-processing pipeline test completed.');
  console.log('Check the .fixed.ts/.fixed.tsx files to see the improvements.');
}

// Run the test
testFixes().catch(console.error);
